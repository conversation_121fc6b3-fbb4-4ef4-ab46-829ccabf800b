<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/db_config.php';

try {
    // Get only image files for the media picker
    $query = "
        SELECT * FROM media_files
        WHERE mime_type LIKE 'image/%'
        ORDER BY created_at DESC
        LIMIT 50
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $files = $stmt->fetchAll();

    // Format files for frontend
    $formatted_files = [];
    foreach ($files as $file) {
        // Fix path - remove 'admin/' prefix if present since we're already in admin directory
        $display_path = $file['file_path'];
        if (strpos($display_path, 'admin/') === 0) {
            $display_path = substr($display_path, 6); // Remove 'admin/' prefix
        }

        $formatted_files[] = [
            'id' => $file['id'],
            'original_name' => $file['original_name'],
            'file_name' => $file['file_name'],
            'file_path' => $display_path,
            'file_size' => $file['file_size'],
            'mime_type' => $file['mime_type'],
            'description' => $file['description'],
            'created_at' => $file['created_at'],
            'formatted_date' => date('M j, Y', strtotime($file['created_at']))
        ];
    }

    $response = [
        'success' => true,
        'files' => $formatted_files,
        'total' => count($formatted_files)
    ];

    echo json_encode($response);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
