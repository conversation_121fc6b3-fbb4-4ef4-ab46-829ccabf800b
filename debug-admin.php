<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug: Checking add-post.php</h2>";

// Check if files exist
$files_to_check = [
    'admin/includes/db_config.php',
    'admin/includes/auth.php', 
    'admin/add-post.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p>✅ File exists: $file</p>";
    } else {
        echo "<p>❌ File missing: $file</p>";
    }
}

// Try to include the files one by one
try {
    echo "<h3>Testing includes...</h3>";
    
    echo "<p>Including db_config.php...</p>";
    require_once 'admin/includes/db_config.php';
    echo "<p>✅ db_config.php loaded</p>";
    
    echo "<p>Including auth.php...</p>";
    require_once 'admin/includes/auth.php';
    echo "<p>✅ auth.php loaded</p>";
    
    // Test if we're logged in
    echo "<p>Login status: " . (isLoggedIn() ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";
    
    // Test database connection
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM blog_posts");
    $result = $stmt->fetch();
    echo "<p>✅ Database connection working: " . $result['count'] . " posts</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Error trace: " . $e->getTraceAsString() . "</p>";
}

echo "<h3>Now let's check what happens when we visit add-post.php...</h3>";
echo "<p><a href='admin/add-post.php' target='_blank'>Click here to open add-post.php</a></p>";
?>
