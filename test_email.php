<?php
// Test script to verify <PERSON><PERSON><PERSON>ail<PERSON> and SMTP configuration
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include PHPMailer
require_once 'vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

// Load configuration
$config = require_once 'config.php';

echo "<h2>Testing PHPMailer Configuration</h2>";
echo "<p><strong>SMTP Host:</strong> " . $config['smtp']['host'] . "</p>";
echo "<p><strong>SMTP Port:</strong> " . $config['smtp']['port'] . "</p>";
echo "<p><strong>SMTP Username:</strong> " . $config['smtp']['username'] . "</p>";
echo "<p><strong>SMTP Encryption:</strong> " . $config['smtp']['encryption'] . "</p>";

try {
    // Create PHPMailer instance
    $mail = new PHPMailer(true);

    // Server settings
    $mail->isSMTP();
    $mail->Host = $config['smtp']['host'];
    $mail->SMTPAuth = true;
    $mail->Username = $config['smtp']['username'];
    $mail->Password = $config['smtp']['password'];
    $mail->SMTPSecure = ($config['smtp']['encryption'] === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = $config['smtp']['port'];
    
    // Enable verbose debug output
    $mail->SMTPDebug = SMTP::DEBUG_CONNECTION;
    $mail->Debugoutput = 'html';

    // Recipients
    $mail->setFrom($config['email']['from_address'], $config['email']['from_name']);
    $mail->addAddress($config['email']['to_address'], $config['email']['to_name']);

    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Test Email - PHPMailer Configuration Test';
    $mail->Body = '
    <h2>PHPMailer Test Email</h2>
    <p>This is a test email to verify that PHPMailer is working correctly with your SMTP configuration.</p>
    <p><strong>Sent at:</strong> ' . date('Y-m-d H:i:s') . '</p>
    <p><strong>Configuration:</strong></p>
    <ul>
        <li>SMTP Host: ' . $config['smtp']['host'] . '</li>
        <li>SMTP Port: ' . $config['smtp']['port'] . '</li>
        <li>SMTP Username: ' . $config['smtp']['username'] . '</li>
        <li>SMTP Encryption: ' . $config['smtp']['encryption'] . '</li>
    </ul>
    <p>If you receive this email, your PHPMailer configuration is working correctly!</p>
    ';
    $mail->AltBody = 'PHPMailer Test Email - Configuration is working correctly! Sent at: ' . date('Y-m-d H:i:s');

    // Send email
    $mail->send();
    echo "<div style='background: #d1fae5; border: 1px solid #10b981; padding: 1rem; margin: 1rem 0; border-radius: 0.5rem;'>";
    echo "<h3 style='color: #065f46; margin: 0 0 0.5rem 0;'>✅ Success!</h3>";
    echo "<p style='color: #065f46; margin: 0;'>Test email sent successfully!</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #fee2e2; border: 1px solid #ef4444; padding: 1rem; margin: 1rem 0; border-radius: 0.5rem;'>";
    echo "<h3 style='color: #991b1b; margin: 0 0 0.5rem 0;'>❌ Error!</h3>";
    echo "<p style='color: #991b1b; margin: 0;'><strong>Message:</strong> " . $e->getMessage() . "</p>";
    if ($mail->ErrorInfo) {
        echo "<p style='color: #991b1b; margin: 0.5rem 0 0 0;'><strong>SMTP Error:</strong> " . $mail->ErrorInfo . "</p>";
    }
    echo "</div>";
}

echo "<hr>";
echo "<h3>Configuration Details:</h3>";
echo "<pre>";
print_r($config);
echo "</pre>";
?>
