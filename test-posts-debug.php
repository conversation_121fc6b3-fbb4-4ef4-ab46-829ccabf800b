<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simulate login
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_full_name'] = 'Administrator';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h2>Testing posts.php functionality</h2>";

try {
    require_once 'admin/includes/db_config.php';
    require_once 'admin/includes/auth.php';
    
    echo "<p>✅ Auth and DB loaded</p>";
    echo "<p>Login status: " . (isLoggedIn() ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";
    
    // Test the database queries that posts.php uses
    echo "<h3>Testing database queries...</h3>";
    
    // Count posts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM blog_posts");
    $result = $stmt->fetch();
    $total_posts = $result ? $result['total'] : 0;
    echo "<p>✅ Total posts: $total_posts</p>";
    
    // Get posts with categories
    $stmt = $pdo->prepare("
        SELECT bp.*, c.name as category_name 
        FROM blog_posts bp 
        LEFT JOIN categories c ON bp.category_id = c.id 
        ORDER BY bp.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $posts = $stmt->fetchAll();
    echo "<p>✅ Posts query successful: " . count($posts) . " posts retrieved</p>";
    
    // Test CSRF token generation
    $csrf_token = generateCSRFToken();
    echo "<p>✅ CSRF token generated: " . substr($csrf_token, 0, 10) . "...</p>";
    
    echo "<h3>posts.php should work now</h3>";
    echo "<p><a href='admin/posts.php' target='_blank'>Test posts.php</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Error trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
}
?>
