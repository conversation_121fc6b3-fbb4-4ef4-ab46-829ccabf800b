<?php
require_once 'admin/includes/db_config.php';

// Get all posts with featured images
$stmt = $pdo->prepare("SELECT id, title, featured_image FROM blog_posts WHERE featured_image IS NOT NULL AND featured_image != ''");
$stmt->execute();
$posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h2>Image Verification Report</h2>";
echo "<p>Checking all featured images in the database...</p>";

$missing_images = [];
$found_images = [];

foreach ($posts as $post) {
    $original_path = $post['featured_image'];
    $filename = basename($original_path);
    $correct_path = 'admin/uploads/' . $filename;
    $full_path = __DIR__ . '/' . $correct_path;
    
    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd;'>";
    echo "<strong>Post #{$post['id']}: {$post['title']}</strong><br>";
    echo "Database path: <code>{$original_path}</code><br>";
    echo "Expected path: <code>{$correct_path}</code><br>";
    echo "Full filesystem path: <code>{$full_path}</code><br>";
    
    if (file_exists($full_path)) {
        echo "<span style='color: green;'>✅ File exists</span>";
        $found_images[] = $filename;
    } else {
        echo "<span style='color: red;'>❌ File missing</span>";
        $missing_images[] = $filename;
    }
    echo "</div>";
}

echo "<h3>Summary</h3>";
echo "<p><strong>Found images:</strong> " . count($found_images) . "</p>";
echo "<p><strong>Missing images:</strong> " . count($missing_images) . "</p>";

if (!empty($missing_images)) {
    echo "<h4>Missing files:</h4>";
    echo "<ul>";
    foreach ($missing_images as $filename) {
        echo "<li><code>{$filename}</code></li>";
    }
    echo "</ul>";
}

// List all files in uploads directory
echo "<h3>Files in admin/uploads/ directory:</h3>";
$upload_dir = __DIR__ . '/admin/uploads/';
if (is_dir($upload_dir)) {
    $files = scandir($upload_dir);
    echo "<ul>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && $file != '.htaccess') {
            echo "<li><code>{$file}</code></li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>Upload directory does not exist!</p>";
}
?>
