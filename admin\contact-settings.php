<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_settings') {
    try {
        $updated_count = 0;

        foreach ($_POST as $key => $value) {
            if (strpos($key, 'setting_') === 0) {
                $setting_key = substr($key, 8); // Remove 'setting_' prefix

                $stmt = $mysqli->prepare("UPDATE site_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?");
                $stmt->bind_param("ss", $value, $setting_key);

                if ($stmt->execute() && $stmt->affected_rows > 0) {
                    $updated_count++;
                }
            }
        }

        $message = "Successfully updated $updated_count contact settings!";

    } catch (Exception $e) {
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Fetch all contact settings grouped by type
$contact_settings = [];
$social_settings = [];

try {
    $stmt = $mysqli->query("SELECT * FROM site_settings WHERE is_active = 1 ORDER BY setting_group, display_order");

    while ($row = $stmt->fetch_assoc()) {
        if ($row['setting_group'] === 'contact') {
            $contact_settings[] = $row;
        } elseif ($row['setting_group'] === 'social') {
            $social_settings[] = $row;
        }
    }

} catch (Exception $e) {
    $error = "Error fetching settings: " . $e->getMessage();
}

$current_user = getCurrentAdmin();
if (!$current_user) {
    $current_user = ['username' => 'Unknown'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Settings - Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .settings-form {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .settings-group {
            margin-bottom: 2rem;
        }

        .settings-group h3 {
            color: #1e40af;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-group .description {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.95rem;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1e40af;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .btn-primary {
            background: #1e40af;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .alert {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .preview-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .preview-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            background: white;
            border-radius: 4px;
        }

        .preview-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .preview-content {
            flex: 1;
        }

        .preview-label {
            font-weight: 600;
            color: #374151;
            font-size: 0.875rem;
        }

        .preview-value {
            color: #1e40af;
            font-size: 0.95rem;
        }
    </style>
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars($current_user['username']); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item">
                <span class="icon">📁</span>
                Categories
            </a>
            <a href="media.php" class="menu-item">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item active">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1>Contact Settings</h1>
            <p>Manage contact information displayed across your website</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <form method="POST" class="settings-form">
            <input type="hidden" name="action" value="update_settings">

            <!-- Contact Information Section -->
            <div class="settings-group">
                <h3>📞 Contact Information</h3>

                <?php foreach ($contact_settings as $setting): ?>
                    <div class="form-group">
                        <label for="setting_<?php echo $setting['setting_key']; ?>">
                            <?php echo htmlspecialchars($setting['setting_label']); ?>
                        </label>
                        <?php if ($setting['setting_description']): ?>
                            <div class="description">
                                <?php echo htmlspecialchars($setting['setting_description']); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($setting['setting_type'] === 'textarea'): ?>
                            <textarea
                                name="setting_<?php echo $setting['setting_key']; ?>"
                                id="setting_<?php echo $setting['setting_key']; ?>"
                                rows="3"
                            ><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>
                        <?php else: ?>
                            <input
                                type="<?php echo $setting['setting_type'] === 'email' ? 'email' : ($setting['setting_type'] === 'phone' ? 'tel' : 'text'); ?>"
                                name="setting_<?php echo $setting['setting_key']; ?>"
                                id="setting_<?php echo $setting['setting_key']; ?>"
                                value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                <?php echo $setting['setting_type'] === 'email' ? 'placeholder="<EMAIL>"' : ''; ?>
                                <?php echo $setting['setting_type'] === 'phone' ? 'placeholder="(*************"' : ''; ?>
                            >
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Social Media Section (Collapsible) -->
            <div class="settings-group">
                <h3 onclick="toggleSocialSection()" style="cursor: pointer; display: flex; align-items: center; justify-content: space-between;">
                    🌐 Social Media Links (Optional)
                    <span id="social-toggle" style="font-size: 0.8rem; color: #6b7280;">▼ Click to expand</span>
                </h3>

                <div id="social-settings" style="display: none; margin-top: 1rem;">
                    <p style="color: #6b7280; font-size: 0.9rem; margin-bottom: 1rem;">
                        Social media links are optional. Leave blank or use "#" to hide social icons.
                    </p>

                    <?php foreach ($social_settings as $setting): ?>
                        <div class="form-group">
                            <label for="setting_<?php echo $setting['setting_key']; ?>">
                                <?php echo htmlspecialchars($setting['setting_label']); ?>
                            </label>
                            <?php if ($setting['setting_description']): ?>
                                <div class="description">
                                    <?php echo htmlspecialchars($setting['setting_description']); ?>
                                </div>
                            <?php endif; ?>

                            <input
                                type="url"
                                name="setting_<?php echo $setting['setting_key']; ?>"
                                id="setting_<?php echo $setting['setting_key']; ?>"
                                value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                placeholder="https://example.com or leave blank"
                            >
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <button type="submit" class="btn-primary">
                💾 Save Contact Settings
            </button>
        </form>

        <!-- Preview Section -->
        <div class="preview-section">
            <h3>📋 Current Contact Information Preview</h3>
            <p style="color: #6b7280; margin-bottom: 1rem;">This is how your contact information will appear on the website:</p>

            <?php foreach ($contact_settings as $setting): ?>
                <?php if (in_array($setting['setting_key'], ['phone_emergency', 'email_general', 'address_primary'])): ?>
                    <div class="preview-item">
                        <div class="preview-icon">
                            <?php
                            if ($setting['setting_type'] === 'phone') echo '📞';
                            elseif ($setting['setting_type'] === 'email') echo '📧';
                            elseif ($setting['setting_type'] === 'address') echo '📍';
                            else echo '📄';
                            ?>
                        </div>
                        <div class="preview-content">
                            <div class="preview-label"><?php echo htmlspecialchars($setting['setting_label']); ?></div>
                            <div class="preview-value"><?php echo htmlspecialchars($setting['setting_value']); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </main>

    <script src="js/admin.js"></script>
    <script>
        // Auto-format phone numbers
        document.querySelectorAll('input[type="tel"]').forEach(function(input) {
            input.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length >= 10) {
                    value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
                }
                this.value = value;
            });
        });

        // Toggle social media section
        function toggleSocialSection() {
            const socialSettings = document.getElementById('social-settings');
            const toggle = document.getElementById('social-toggle');

            if (socialSettings.style.display === 'none') {
                socialSettings.style.display = 'block';
                toggle.textContent = '▲ Click to collapse';
            } else {
                socialSettings.style.display = 'none';
                toggle.textContent = '▼ Click to expand';
            }
        }

        // Show success message and auto-hide
        const successAlert = document.querySelector('.alert-success');
        if (successAlert) {
            setTimeout(function() {
                successAlert.style.opacity = '0';
                setTimeout(function() {
                    successAlert.style.display = 'none';
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
