<?php
// Simple session test
echo "<h2>Session Test</h2>";

// Start session
session_start();
echo "<p>✅ Session started</p>";
echo "<p>Session ID: " . session_id() . "</p>";

// Set test session variable
$_SESSION['test'] = 'Session is working!';
echo "<p>✅ Session variable set</p>";

// Test session persistence
if (isset($_SESSION['test'])) {
    echo "<p>✅ Session variable retrieved: " . $_SESSION['test'] . "</p>";
} else {
    echo "<p>❌ Session variable not found</p>";
}

// Display all session data
echo "<h3>All Session Data:</h3>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
?>
