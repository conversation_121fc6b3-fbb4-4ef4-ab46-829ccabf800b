<?php
// Simple test to populate database via web server
include 'admin/includes/db_config.php';

// Check if we can connect
if (!$pdo) {
    die("Database connection failed");
}

echo "<h2>Database Connection Test</h2>";
echo "<p>✅ Connected to database successfully!</p>";

// Check if posts table exists
try {    $stmt = $pdo->query("SHOW TABLES LIKE 'blog_posts'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ Blog posts table exists</p>";
    } else {
        echo "<p>❌ Blog posts table does not exist</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p>❌ Error checking tables: " . $e->getMessage() . "</p>";
    exit;
}

// Sample blog posts
$sample_posts = [
    [
        'title' => 'Cryptocurrency Fraud Reaches Record Highs in 2025',
        'slug' => 'cryptocurrency-fraud-reaches-record-highs-2025',
        'content' => "## The Growing Threat of Cryptocurrency Fraud

New data reveals that cryptocurrency-related fraud has increased by 85% this year, with investment scams being the most common type. As digital currencies become more mainstream, fraudsters are developing increasingly sophisticated methods to deceive victims.

### Common Cryptocurrency Scams

1. **Investment Platforms**: Fake investment websites promising guaranteed returns
2. **Social Media Scams**: Fraudsters impersonating celebrities or influencers
3. **Phishing Attacks**: Fake wallet applications and exchange platforms
4. **Romance Scams**: Building relationships to solicit cryptocurrency investments

### Protection Strategies

- Always verify the legitimacy of investment platforms
- Never send cryptocurrency to unknown individuals
- Use hardware wallets for large holdings
- Report suspicious activity immediately

If you've been a victim of cryptocurrency fraud, our digital forensics team can help trace transactions and recover funds through legal channels.",
        'excerpt' => 'Cryptocurrency fraud has increased by 85% this year, with investment scams being the most common type affecting victims worldwide.',
        'category' => 'Cryptocurrency',
        'author' => 'Digital Forensics Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'cryptocurrency,fraud,investment,scam,bitcoin'
    ],
    [
        'title' => 'Romance Scams: How to Protect Yourself from Online Dating Fraud',
        'slug' => 'romance-scams-protect-yourself-online-dating-fraud',
        'content' => "## Understanding Romance Scams

Romance scams have become one of the fastest-growing types of fraud, with victims losing billions of dollars annually. These scams exploit human emotions and the desire for companionship, making them particularly devastating.

### How Romance Scams Work

Scammers create fake profiles on dating sites and social media platforms, often using stolen photos of attractive individuals. They spend weeks or months building emotional connections with their victims before introducing financial requests.

### Warning Signs

- **Professes love very quickly**
- **Avoids phone calls or video chats**
- **Claims to be traveling or deployed overseas**
- **Asks for money for emergencies**
- **Requests gift cards or cryptocurrency**
- **Has very few photos or photos that seem too professional**

### Protection Tips

1. **Reverse image search** any photos they send
2. **Insist on video calls** early in the relationship  
3. **Never send money** to someone you haven't met in person
4. **Be suspicious** of sob stories requiring financial help
5. **Trust your instincts** if something feels off

### If You've Been Scammed

Don't be embarrassed - these scammers are professionals who manipulate emotions expertly. Contact our team immediately to discuss recovery options and evidence preservation.",
        'excerpt' => 'Romance scams exploit emotions and trust, resulting in billions in losses. Learn the warning signs and protection strategies.',
        'category' => 'Romance Scams',
        'author' => 'Fraud Prevention Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'romance,scam,dating,fraud,online'
    ],
    [
        'title' => 'Investment Fraud Recovery: Legal Options and Digital Evidence',
        'slug' => 'investment-fraud-recovery-legal-options-digital-evidence',
        'content' => "## Recovering from Investment Fraud

Investment fraud victims often feel helpless, but there are legal remedies and recovery options available. The key is acting quickly to preserve evidence and understanding your legal rights.

### Types of Investment Fraud

**Ponzi Schemes**: Using new investor money to pay earlier investors
**Pump and Dump**: Artificially inflating stock prices then selling
**Binary Options Scams**: Fake trading platforms that steal deposits
**Forex Fraud**: Unregulated foreign exchange trading schemes
**Cryptocurrency Scams**: Fake ICOs and investment platforms

### Immediate Steps for Victims

1. **Stop all contact** with the fraudsters
2. **Document everything** - emails, messages, transaction records
3. **Report to authorities** - SEC, CFTC, FBI, local police
4. **Contact your bank** to attempt transaction reversals
5. **Preserve digital evidence** before it's deleted

### Legal Recovery Options

- **Civil litigation** against perpetrators and enablers
- **Criminal restitution** through prosecutor cooperation
- **Insurance claims** if applicable
- **Asset recovery** through forensic investigation

### Digital Forensics Role

Our team specializes in:
- Blockchain transaction analysis
- Email and communications forensics
- Financial transaction tracing
- Digital asset recovery
- Evidence preservation for litigation

Time is critical in fraud cases. Contact our emergency response team immediately to maximize your recovery chances.",
        'excerpt' => 'Investment fraud victims have legal remedies and recovery options available. Learn about digital evidence preservation and asset recovery.',
        'category' => 'Investment Fraud',
        'author' => 'Legal Recovery Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'investment,fraud,recovery,legal,evidence'
    ],
    [
        'title' => '10 Essential Tips for Avoiding Online Fraud in 2025',
        'slug' => '10-essential-tips-avoiding-online-fraud-2025',
        'content' => "## Staying Safe in the Digital Age

As technology evolves, so do the tactics used by fraudsters. Here are ten essential tips to protect yourself from online fraud in 2025.

### 1. Use Strong, Unique Passwords

Create complex passwords for each account and use a reputable password manager. Enable two-factor authentication wherever possible.

### 2. Verify Before You Trust

Always verify the identity of individuals or organizations requesting personal or financial information through independent channels.

### 3. Be Cautious with Public Wi-Fi

Avoid accessing sensitive accounts on public networks. Use a VPN when necessary.

### 4. Keep Software Updated

Regularly update your devices, browsers, and security software to protect against the latest threats.

### 5. Monitor Financial Accounts

Check bank and credit card statements regularly for unauthorized transactions.

### 6. Think Before You Click

Be suspicious of unexpected emails, text messages, or social media messages with links or attachments.

### 7. Research Investment Opportunities

Verify the legitimacy of investment platforms and financial advisors through regulatory databases.

### 8. Protect Personal Information

Be selective about what personal information you share on social media and online platforms.

### 9. Trust Your Instincts

If something seems too good to be true or feels suspicious, investigate further before proceeding.

### 10. Know How to Report Fraud

Familiarize yourself with the proper channels for reporting fraud in your jurisdiction.

### Emergency Response

If you suspect you've been targeted or victimized by fraud, contact our 24/7 emergency response team for immediate assistance and guidance.",
        'excerpt' => 'Stay protected in 2025 with these essential tips for avoiding online fraud, from password security to investment verification.',
        'category' => 'Prevention',
        'author' => 'Security Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'prevention,security,online,fraud,tips'
    ],
    [
        'title' => 'Phishing Attacks Evolution: New Tactics and Defense Strategies',
        'slug' => 'phishing-attacks-evolution-new-tactics-defense-strategies',
        'content' => "## The Evolution of Phishing Attacks

Phishing attacks have become increasingly sophisticated, moving beyond simple email scams to complex, multi-channel campaigns that can fool even security-conscious individuals.

### Modern Phishing Techniques

**Spear Phishing**: Highly targeted attacks using personal information
**Smishing**: SMS-based phishing attacks
**Vishing**: Voice call phishing using social engineering
**Business Email Compromise (BEC)**: Targeting business communications
**Social Media Phishing**: Fake messages on social platforms

### AI-Enhanced Phishing

Criminals now use artificial intelligence to:
- Create more convincing fake communications
- Generate realistic voice clones for vishing
- Automate personalized attack campaigns
- Bypass traditional security filters

### Red Flags to Watch For

- **Urgent language** creating pressure to act quickly
- **Generic greetings** like \"Dear Customer\"
- **Suspicious URLs** that don't match the claimed sender
- **Requests for sensitive information** via email or text
- **Unexpected attachments** or download links

### Advanced Defense Strategies

1. **Email Authentication**: Implement DMARC, SPF, and DKIM
2. **Security Awareness Training**: Regular employee education
3. **Multi-Factor Authentication**: Add extra security layers
4. **Email Filtering**: Use advanced threat detection
5. **Incident Response Plan**: Prepare for when attacks succeed

### When Phishing Succeeds

If you've fallen victim to a phishing attack:
- Change all passwords immediately
- Contact your financial institutions
- Run comprehensive malware scans
- Monitor accounts for unauthorized activity
- Report the incident to authorities

Our digital forensics team can help investigate phishing attacks, trace perpetrators, and recover compromised data.",
        'excerpt' => 'Phishing attacks are evolving with AI and new tactics. Learn about modern threats and advanced defense strategies.',
        'category' => 'Phishing',
        'author' => 'Cybersecurity Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'phishing,cybersecurity,email,fraud,AI'
    ],
    [
        'title' => 'Blockchain Forensics: Tracing Cryptocurrency Transactions',
        'slug' => 'blockchain-forensics-tracing-cryptocurrency-transactions',
        'content' => "## Understanding Blockchain Forensics

Contrary to popular belief, cryptocurrency transactions are not anonymous but pseudonymous. Blockchain forensics leverages this transparency to trace illegal activities and recover stolen funds.

### How Blockchain Analysis Works

Every cryptocurrency transaction is recorded on a public ledger. Our forensics experts use specialized tools to:

- **Map transaction flows** between addresses
- **Identify exchange connections** where crypto is converted to fiat
- **Cluster addresses** belonging to the same entity
- **Track mixing services** used to obscure trails
- **Correlate with traditional evidence** like IP addresses and timestamps

### Common Investigation Scenarios

**Ransomware Payments**: Tracing payments to identify perpetrators
**Investment Scams**: Following stolen funds to recovery points
**Dark Web Transactions**: Connecting illegal marketplace activities
**Money Laundering**: Identifying complex layering schemes
**Theft and Fraud**: Tracking stolen cryptocurrency

### Advanced Techniques

Our team employs cutting-edge blockchain analysis techniques:

- **Pattern Recognition**: Identifying behavioral signatures
- **Cross-Chain Analysis**: Following funds across different blockchains
- **DeFi Protocol Analysis**: Tracking through decentralized finance
- **Privacy Coin Investigation**: Analyzing Monero and similar currencies
- **NFT Forensics**: Investigating non-fungible token fraud

### Legal Admissibility

Blockchain evidence must meet legal standards:
- **Chain of custody** documentation
- **Technical accuracy** in analysis methods
- **Expert testimony** to explain findings
- **Correlation** with traditional evidence

### Recovery Success Stories

Our blockchain forensics team has successfully:
- Recovered over $50 million in stolen cryptocurrency
- Identified perpetrators in major fraud cases
- Provided evidence leading to criminal convictions
- Assisted in civil asset recovery proceedings

### Working with Law Enforcement

We collaborate closely with:
- Federal agencies (FBI, Secret Service, IRS-CI)
- International partners (Europol, Interpol)
- Local law enforcement agencies
- Regulatory bodies (FinCEN, SEC)

Contact our blockchain forensics team for expert investigation and recovery services.",
        'excerpt' => 'Blockchain forensics can trace cryptocurrency transactions to identify perpetrators and recover stolen funds through expert analysis.',
        'category' => 'Blockchain Forensics',
        'author' => 'Blockchain Analysis Team',
        'featured_image' => '',
        'status' => 'published',
        'tags' => 'blockchain,forensics,cryptocurrency,investigation,recovery'
    ]
];

echo "<h3>Inserting Sample Posts...</h3>";

// Insert each post
foreach ($sample_posts as $index => $post) {
    try {        $stmt = $pdo->prepare("
            INSERT INTO blog_posts (title, slug, content, excerpt, category, author, featured_image, status, tags, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $post['title'],
            $post['slug'],
            $post['content'],
            $post['excerpt'],
            $post['category'],
            $post['author'],
            $post['featured_image'],
            $post['status'],
            $post['tags']
        ]);
        
        echo "<p>✅ Post " . ($index + 1) . ": '" . htmlspecialchars($post['title']) . "' inserted successfully</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error inserting post " . ($index + 1) . ": " . $e->getMessage() . "</p>";
    }
}

echo "<h3>Database Population Complete!</h3>";
echo "<p><a href='admin/login.php'>Go to Admin Dashboard</a></p>";
echo "<p><a href='index.html'>View Homepage</a></p>";
echo "<p><a href='news.html'>View News Page</a></p>";
?>
