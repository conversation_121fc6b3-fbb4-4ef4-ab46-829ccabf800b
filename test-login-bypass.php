<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session and simulate being logged in
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_full_name'] = 'Administrator';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h2>Testing add-post.php with simulated login</h2>";

try {
    // Include the required files
    require_once 'admin/includes/db_config.php';
    require_once 'admin/includes/auth.php';
    
    echo "<p>✅ Files loaded successfully</p>";
    echo "<p>Login status: " . (isLoggedIn() ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";
    
    if (isLoggedIn()) {
        echo "<p>✅ User is logged in, add-post.php should load</p>";
        echo "<p><a href='admin/add-post.php' target='_blank'>Test add-post.php</a></p>";
        echo "<p><a href='admin/posts.php' target='_blank'>Test posts.php</a></p>";
        echo "<p><a href='admin/edit-post.php' target='_blank'>Test edit-post.php</a></p>";
    } else {
        echo "<p>❌ Still not logged in</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
