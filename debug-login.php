<?php
session_start();
require_once 'admin/includes/db_config.php';
require_once 'admin/includes/auth.php';

echo "<h2>Login Debug Test</h2>";

// Test 1: Generate CSRF token
$csrf_token = generateCSRFToken();
echo "<p>✅ CSRF Token Generated: " . substr($csrf_token, 0, 20) . "...</p>";

// Test 2: Test authentication with correct credentials
echo "<h3>Testing Authentication</h3>";
$test_result = authenticate('admin', 'admin123');
if ($test_result) {
    echo "<p>✅ Authentication successful with admin/admin123</p>";
    echo "<p>Session data: " . print_r($_SESSION, true) . "</p>";
} else {
    echo "<p>❌ Authentication failed with admin/admin123</p>";
    
    // Check if user exists in database
    $stmt = $pdo->prepare("SELECT username, password FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>User found in database: " . $user['username'] . "</p>";
        echo "<p>Password hash: " . substr($user['password'], 0, 20) . "...</p>";
        
        // Test password verification
        $password_check = password_verify('admin123', $user['password']);
        echo "<p>Password verification: " . ($password_check ? 'PASS' : 'FAIL') . "</p>";
    } else {
        echo "<p>❌ No admin user found in database</p>";
    }
}

// Test 3: Test CSRF verification
echo "<h3>Testing CSRF Protection</h3>";
$csrf_valid = verifyCSRFToken($csrf_token);
echo "<p>CSRF Verification: " . ($csrf_valid ? 'VALID' : 'INVALID') . "</p>";

// Test 4: Simulate form submission
echo "<h3>Login Form Test</h3>";
echo "<form method='POST' action='admin/login.php' style='background: #f0f0f0; padding: 20px; border-radius: 8px;'>";
echo "<input type='hidden' name='csrf_token' value='" . $csrf_token . "'>";
echo "<p><label>Username: <input type='text' name='username' value='admin'></label></p>";
echo "<p><label>Password: <input type='password' name='password' value='admin123'></label></p>";
echo "<p><button type='submit' style='padding: 10px 20px; background: #1e40af; color: white; border: none; border-radius: 4px;'>Test Login</button></p>";
echo "</form>";

// Test 5: Clear session and logout
if (isset($_GET['clear'])) {
    session_unset();
    session_destroy();
    echo "<p>✅ Session cleared</p>";
    echo "<p><a href='?'>Refresh Page</a></p>";
} else {
    echo "<p><a href='?clear=1'>Clear Session</a></p>";
}

echo "<p><a href='admin/login.php'>Go to Login Page</a></p>";
?>
