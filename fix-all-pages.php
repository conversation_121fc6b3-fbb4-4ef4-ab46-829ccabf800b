<?php
/**
 * Fix All Pages - CSS Cache Busting and Remove Contact Loader
 * This script updates all HTML files to:
 * 1. Add cache-busting parameter to CSS
 * 2. Remove contact-loader.js script
 */

// List of HTML files to fix (excluding contact.html which is already fixed)
$htmlFiles = [
    'services.html',
    'crypto-scam.html',
    'romance-scam.html',
    'phishing-scam.html',
    'wire-fraud.html',
    'digital-forensics.html',
    'bec-scam.html',
    'apply.html',
    'team.html',
    'testimonials.html'
];

$fixedFiles = [];
$errors = [];

foreach ($htmlFiles as $file) {
    if (!file_exists($file)) {
        $errors[] = "File not found: $file";
        continue;
    }
    
    try {
        // Read file content
        $content = file_get_contents($file);
        
        if ($content === false) {
            $errors[] = "Could not read file: $file";
            continue;
        }
        
        // Fix 1: Add cache-busting to CSS
        $content = str_replace(
            'href="css/modern.css"',
            'href="css/modern.css?v=2025"',
            $content
        );
        
        // Fix 2: Remove contact-loader.js script
        $content = preg_replace(
            '/\s*<script src="js\/contact-loader\.js"><\/script>\s*/',
            "\n",
            $content
        );
        
        // Write back to file
        if (file_put_contents($file, $content) !== false) {
            $fixedFiles[] = $file;
        } else {
            $errors[] = "Could not write to file: $file";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error processing $file: " . $e->getMessage();
    }
}

// Output results
echo "<!DOCTYPE html>\n";
echo "<html><head><title>Fix All Pages Results</title></head><body>\n";
echo "<h1>🔧 Fix All Pages Results</h1>\n";

if (!empty($fixedFiles)) {
    echo "<h2>✅ Successfully Fixed Files (" . count($fixedFiles) . "):</h2>\n";
    echo "<ul>\n";
    foreach ($fixedFiles as $file) {
        echo "<li><strong>$file</strong> - CSS cache-busting added, contact-loader.js removed</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($errors)) {
    echo "<h2>❌ Errors (" . count($errors) . "):</h2>\n";
    echo "<ul>\n";
    foreach ($errors as $error) {
        echo "<li style='color: red;'>$error</li>\n";
    }
    echo "</ul>\n";
}

echo "<h2>📋 Summary:</h2>\n";
echo "<p><strong>Total files processed:</strong> " . count($htmlFiles) . "</p>\n";
echo "<p><strong>Successfully fixed:</strong> " . count($fixedFiles) . "</p>\n";
echo "<p><strong>Errors:</strong> " . count($errors) . "</p>\n";

echo "<h2>🎯 What Was Fixed:</h2>\n";
echo "<ol>\n";
echo "<li><strong>CSS Cache Busting:</strong> Changed <code>css/modern.css</code> to <code>css/modern.css?v=2025</code></li>\n";
echo "<li><strong>Removed Contact Loader:</strong> Removed <code>&lt;script src=\"js/contact-loader.js\"&gt;&lt;/script&gt;</code></li>\n";
echo "</ol>\n";

echo "<h2>✅ Benefits:</h2>\n";
echo "<ul>\n";
echo "<li>Emergency phone styling will now show correctly online</li>\n";
echo "<li>No more JavaScript API errors in console</li>\n";
echo "<li>Footer displays static contact information immediately</li>\n";
echo "<li>Consistent styling across all pages</li>\n";
echo "</ul>\n";

echo "<p><strong>Next Steps:</strong> Clear your browser cache and test the pages. The footer should now display contact information immediately with proper emergency phone styling.</p>\n";

echo "</body></html>\n";
?>
