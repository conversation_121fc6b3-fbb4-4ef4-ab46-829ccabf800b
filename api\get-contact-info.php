<?php
/**
 * Contact Information API
 * Returns dynamic contact information from database
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/includes/db_config.php';

try {
    // Check if site_settings table exists
    $table_check = $mysqli->query("SHOW TABLES LIKE 'site_settings'");
    $contact_info = [];

    if ($table_check && $table_check->num_rows > 0) {
        // Fetch all active contact settings
        $stmt = $mysqli->query("
            SELECT setting_key, setting_value, setting_type, setting_group
            FROM site_settings
            WHERE is_active = 1
            ORDER BY setting_group, display_order
        ");

        if ($stmt) {
            while ($row = $stmt->fetch_assoc()) {
                $contact_info[$row['setting_key']] = [
                    'value' => $row['setting_value'],
                    'type' => $row['setting_type'],
                    'group' => $row['setting_group']
                ];
            }
        }
    }
    
    // Structure the response for easy use
    $response = [
        'success' => true,
        'data' => [
            'phones' => [
                'emergency' => $contact_info['phone_emergency']['value'] ?? '(*************',
                'general' => $contact_info['phone_general']['value'] ?? '+1 (555) 123-DEMO'
            ],
            'emails' => [
                'general' => $contact_info['email_general']['value'] ?? '<EMAIL>',
                'help' => $contact_info['email_help']['value'] ?? '<EMAIL>',
                'emergency' => $contact_info['email_emergency']['value'] ?? '<EMAIL>'
            ],
            'addresses' => [
                'primary' => $contact_info['address_primary']['value'] ?? '123 Demo Street, Demo City, DC 12345, United States',
                'secondary' => $contact_info['address_secondary']['value'] ?? '456 Sample Avenue, Example Town, ET 67890, United Kingdom'
            ],
            'business' => [
                'hours' => $contact_info['business_hours']['value'] ?? '24/7 Emergency Support Available',
                'emergency_note' => $contact_info['emergency_note']['value'] ?? 'Available 24/7'
            ],
            'social' => [
                'facebook' => $contact_info['social_facebook']['value'] ?? '#',
                'twitter' => $contact_info['social_twitter']['value'] ?? '#',
                'linkedin' => $contact_info['social_linkedin']['value'] ?? '#',
                'instagram' => $contact_info['social_instagram']['value'] ?? '#'
            ]
        ],
        'debug' => [
            'environment' => $environment ?? 'unknown',
            'database' => $db_name ?? 'unknown',
            'site_settings_table_exists' => ($table_check && $table_check->num_rows > 0) ? 'yes' : 'no',
            'contact_settings_count' => count($contact_info),
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch contact information: ' . $e->getMessage()
    ]);
}

$mysqli->close();
?>
