<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sticky Header Test</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        .content {
            height: 200vh;
            padding: 20px;
            background: linear-gradient(to bottom, #f0f0f0, #e0e0e0);
        }
        .section {
            height: 50vh;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <div class="content">
        <h1>Sticky Header Test Page</h1>
        <p>Scroll down to test the sticky header functionality.</p>
        
        <div class="section">
            <h2>Section 1</h2>
            <p>This is the first section. The header should stick to the top when you scroll past it.</p>
        </div>
        
        <div class="section">
            <h2>Section 2</h2>
            <p>Keep scrolling to see the sticky header effect.</p>
        </div>
        
        <div class="section">
            <h2>Section 3</h2>
            <p>The header should remain visible at the top of the viewport.</p>
        </div>
        
        <div class="section">
            <h2>Section 4</h2>
            <p>Final section to test the sticky behavior.</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    
    <script>
        console.log('Test page loaded');
        
        // Test scroll detection
        window.addEventListener('scroll', function() {
            console.log('Page scroll detected:', window.scrollY);
        });
    </script>
</body>
</html>
