<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Apply for Services - Forensic Involve | Detailed Service Application</title>
  <meta name="description" content="Apply for our fraud recovery services with our detailed application form. Provide comprehensive case information to help us assess your situation and develop a recovery strategy.">

  <!-- Favicon -->
  <link rel="icon" href="assets/svg/logo.svg" type="image/svg+xml">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/modern.css?v=2025">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- Header -->
  <header class="header">
    <div class="container">
      <div class="header-container">
        <a href="index.html" class="logo">
          <img src="demo-data/logo.png" alt="Forensic Involve" class="logo-image">
        </a>

        <nav>
          <ul class="nav-menu">
            <li><a href="index.html" class="nav-link">Home</a></li>
            <li><a href="about.html" class="nav-link">About</a></li>
            <li><a href="services.html" class="nav-link">Services</a></li>
            <li><a href="process.html" class="nav-link">Process</a></li>
            <li><a href="success-stories.html" class="nav-link">Success Stories</a></li>
            <li><a href="apply.html" class="nav-link active">Apply</a></li>
            <li><a href="contact.html" class="nav-link">Contact</a></li>
          </ul>
        </nav>
      </div>
    </div>
  </header>

  <main>
    <!-- Hero Section -->
    <section class="hero-section" style="background: linear-gradient(rgba(30, 64, 175, 0.8), rgba(59, 130, 246, 0.8)), url('images/apply-hero.webp'); background-size: cover; background-position: center; background-attachment: fixed;">
      <div class="container" style="padding-top: 90px;">
        <div class="row">
          <div class="col-6">
            <div class="hero-content">
              <div class="hero-badge">Service Application</div>
              <h1>Apply for <span class="highlight">Recovery Services</span></h1>
              <p class="hero-description">Complete our detailed application form to help us understand your case and develop the most effective recovery strategy. The more information you provide, the better we can assist you in recovering your funds.</p>
              <div class="hero-cta">
                <a href="#application-form" class="btn btn-primary btn-lg">
                  <i class="fas fa-file-alt"></i>
                  Start Application
                </a>
                <a href="contact.html" class="btn btn-secondary btn-lg">
                  <i class="fas fa-phone"></i>
                  Call First
                </a>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="hero-image">
              <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background elements -->
                <circle cx="400" cy="80" r="60" fill="#dbeafe" opacity="0.6"/>
                <circle cx="100" cy="320" r="40" fill="#f0fdf4" opacity="0.8"/>

                <!-- Application form illustration -->
                <g transform="translate(250, 200)">
                  <!-- Application form -->
                  <rect x="-60" y="-80" width="120" height="160" rx="8" fill="#ffffff" stroke="#e5e7eb" stroke-width="2"/>

                  <!-- Form header -->
                  <rect x="-50" y="-70" width="100" height="20" fill="#1e40af" opacity="0.1"/>
                  <text x="0" y="-57" text-anchor="middle" fill="#1e40af" font-size="10" font-weight="bold">SERVICE APPLICATION</text>

                  <!-- Form fields -->
                  <rect x="-50" y="-45" width="100" height="8" fill="#f3f4f6"/>
                  <rect x="-50" y="-35" width="80" height="8" fill="#f3f4f6"/>
                  <rect x="-50" y="-25" width="90" height="8" fill="#f3f4f6"/>

                  <rect x="-50" y="-10" width="100" height="8" fill="#f3f4f6"/>
                  <rect x="-50" y="0" width="70" height="8" fill="#f3f4f6"/>
                  <rect x="-50" y="10" width="85" height="8" fill="#f3f4f6"/>

                  <rect x="-50" y="25" width="100" height="20" fill="#f3f4f6"/>

                  <!-- Submit button -->
                  <rect x="-30" y="55" width="60" height="15" fill="#1e40af" rx="4"/>
                  <text x="0" y="65" text-anchor="middle" fill="white" font-size="8" font-weight="bold">SUBMIT</text>

                  <text x="0" y="-95" text-anchor="middle" fill="#64748b" font-size="12">Detailed Application</text>
                </g>

                <!-- Client information -->
                <g transform="translate(120, 120)">
                  <circle cx="0" cy="0" r="25" fill="#dbeafe" opacity="0.9"/>
                  <circle cx="0" cy="-5" r="8" fill="#1e40af"/>
                  <path d="M-15 15 C-15 10 -8 8 0 8 C8 8 15 10 15 15" fill="#1e40af"/>
                  <text x="0" y="-35" text-anchor="middle" fill="#64748b" font-size="10">Client Info</text>
                </g>

                <!-- Case details -->
                <g transform="translate(380, 120)">
                  <circle cx="0" cy="0" r="25" fill="#f0fdf4" opacity="0.9"/>
                  <rect x="-8" y="-8" width="16" height="16" rx="2" fill="#10b981"/>
                  <rect x="-6" y="-6" width="12" height="3" fill="white"/>
                  <rect x="-6" y="-2" width="12" height="3" fill="white"/>
                  <rect x="-6" y="2" width="12" height="3" fill="white"/>
                  <text x="0" y="-35" text-anchor="middle" fill="#64748b" font-size="10">Case Details</text>
                </g>

                <!-- Financial information -->
                <g transform="translate(120, 280)">
                  <circle cx="0" cy="0" r="25" fill="#fef3c7" opacity="0.9"/>
                  <rect x="-8" y="-8" width="16" height="16" rx="2" fill="#f59e0b"/>
                  <text x="0" y="-2" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
                  <text x="0" y="40" text-anchor="middle" fill="#64748b" font-size="10">Financial Info</text>
                </g>

                <!-- Evidence -->
                <g transform="translate(380, 280)">
                  <circle cx="0" cy="0" r="25" fill="#f3e8ff" opacity="0.9"/>
                  <rect x="-8" y="-8" width="16" height="16" rx="2" fill="#8b5cf6"/>
                  <path d="M-4 -4 L4 -4 L4 4 L-4 4 Z" stroke="white" stroke-width="1" fill="none"/>
                  <circle cx="0" cy="0" r="2" fill="white"/>
                  <text x="0" y="40" text-anchor="middle" fill="#64748b" font-size="10">Evidence</text>
                </g>

                <!-- Connection lines -->
                <path d="M145 135 Q200 160 215 185" stroke="#1e40af" stroke-width="2" fill="none" opacity="0.6"/>
                <path d="M355 135 Q300 160 285 185" stroke="#10b981" stroke-width="2" fill="none" opacity="0.6"/>
                <path d="M145 265 Q200 240 225 215" stroke="#f59e0b" stroke-width="2" fill="none" opacity="0.6"/>
                <path d="M355 265 Q300 240 275 215" stroke="#8b5cf6" stroke-width="2" fill="none" opacity="0.6"/>

                <!-- Success indicators -->
                <g transform="translate(50, 80)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>

                <g transform="translate(450, 320)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>

                <!-- Decorative elements -->
                <circle cx="30" cy="30" r="2" fill="#1e40af" opacity="0.4"/>
                <circle cx="470" cy="50" r="3" fill="#10b981" opacity="0.5"/>
                <circle cx="20" cy="380" r="2" fill="#f59e0b" opacity="0.6"/>
                <circle cx="480" cy="370" r="2" fill="#8b5cf6" opacity="0.4"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- Application Form Section -->
    <section class="section" id="application-form" style="background: #f8fafc;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Service Application Form</h2>
          <p class="section-subtitle" style="margin: 0;">Please provide as much detail as possible to help us assess your case and develop an effective recovery strategy.</p>
        </div>

        <form action="process_application.php" method="post" id="applicationForm" style="margin-top: 3rem; background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
          <!-- Personal Information Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-user" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Personal Information
            </h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">First Name *</label>
                <input type="text" name="first_name" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Last Name *</label>
                <input type="text" name="last_name" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Email Address *</label>
                <input type="email" name="email" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Phone Number *</label>
                <input type="tel" name="phone" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Country *</label>
                <input type="text" name="country" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Time Zone</label>
                <select name="timezone" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                  <option value="">Select your time zone...</option>
                  <option value="UTC-12">UTC-12 (Baker Island)</option>
                  <option value="UTC-11">UTC-11 (American Samoa)</option>
                  <option value="UTC-10">UTC-10 (Hawaii)</option>
                  <option value="UTC-9">UTC-9 (Alaska)</option>
                  <option value="UTC-8">UTC-8 (Pacific Time)</option>
                  <option value="UTC-7">UTC-7 (Mountain Time)</option>
                  <option value="UTC-6">UTC-6 (Central Time)</option>
                  <option value="UTC-5">UTC-5 (Eastern Time)</option>
                  <option value="UTC-4">UTC-4 (Atlantic Time)</option>
                  <option value="UTC-3">UTC-3 (Argentina)</option>
                  <option value="UTC-2">UTC-2 (South Georgia)</option>
                  <option value="UTC-1">UTC-1 (Azores)</option>
                  <option value="UTC+0">UTC+0 (London, Dublin)</option>
                  <option value="UTC+1">UTC+1 (Central Europe)</option>
                  <option value="UTC+2">UTC+2 (Eastern Europe)</option>
                  <option value="UTC+3">UTC+3 (Moscow)</option>
                  <option value="UTC+4">UTC+4 (Dubai)</option>
                  <option value="UTC+5">UTC+5 (Pakistan)</option>
                  <option value="UTC+6">UTC+6 (Bangladesh)</option>
                  <option value="UTC+7">UTC+7 (Thailand)</option>
                  <option value="UTC+8">UTC+8 (China, Singapore)</option>
                  <option value="UTC+9">UTC+9 (Japan, Korea)</option>
                  <option value="UTC+10">UTC+10 (Australia East)</option>
                  <option value="UTC+11">UTC+11 (Solomon Islands)</option>
                  <option value="UTC+12">UTC+12 (New Zealand)</option>
                </select>
              </div>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Preferred Contact Method *</label>
              <select name="contact_method" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select preferred contact method...</option>
                <option value="email">Email</option>
                <option value="phone">Phone Call</option>
                <option value="whatsapp">WhatsApp</option>
                <option value="telegram">Telegram</option>
                <option value="video_call">Video Call (Zoom/Teams)</option>
              </select>
            </div>
          </div>

          <!-- Service Selection Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-cogs" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Service Selection
            </h3>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Type of Service Needed *</label>
              <select name="service_type" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select the service you need...</option>
                <option value="digital_forensics">Digital Forensics Investigation</option>
                <option value="cryptocurrency">Cryptocurrency Recovery</option>
                <option value="romance_scam">Romance Scam Recovery</option>
                <option value="investment_fraud">Investment Fraud Recovery</option>
                <option value="phishing">Phishing & Online Fraud</option>
                <option value="identity_theft">Identity Theft Recovery</option>
                <option value="bec_scam">Business Email Compromise</option>
                <option value="wire_fraud">Wire Transfer Fraud</option>
                <option value="social_media">Social Media Scams</option>
                <option value="multiple">Multiple Services</option>
                <option value="unsure">Not Sure - Need Assessment</option>
              </select>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Urgency Level *</label>
              <select name="urgency" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select urgency level...</option>
                <option value="emergency">Emergency - Active fraud in progress</option>
                <option value="urgent">Urgent - Recent loss (within 48 hours)</option>
                <option value="high">High - Recent loss (within 1 week)</option>
                <option value="normal">Normal - Loss occurred over 1 week ago</option>
                <option value="consultation">Consultation - Preventive measures</option>
              </select>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">How did you hear about us?</label>
              <select name="referral_source" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select source...</option>
                <option value="google_search">Google Search</option>
                <option value="social_media">Social Media</option>
                <option value="referral">Referral from friend/family</option>
                <option value="lawyer">Lawyer/Legal Professional</option>
                <option value="law_enforcement">Law Enforcement</option>
                <option value="news_article">News Article</option>
                <option value="online_forum">Online Forum/Community</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <!-- Incident Details Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-exclamation-triangle" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Incident Details
            </h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Date of Incident *</label>
                <input type="date" name="incident_date" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Date Discovered</label>
                <input type="date" name="discovery_date" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Location/Country Where Incident Occurred</label>
              <input type="text" name="incident_location" placeholder="e.g., United States, United Kingdom, etc." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Brief Description of What Happened *</label>
              <textarea name="incident_description" required rows="4" placeholder="Please provide a brief overview of the incident..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">How were you initially contacted by the scammer?</label>
              <select name="initial_contact" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select contact method...</option>
                <option value="email">Email</option>
                <option value="phone_call">Phone Call</option>
                <option value="text_message">Text Message/SMS</option>
                <option value="social_media">Social Media (Facebook, Instagram, etc.)</option>
                <option value="dating_app">Dating App/Website</option>
                <option value="investment_website">Investment Website/Platform</option>
                <option value="online_ad">Online Advertisement</option>
                <option value="whatsapp">WhatsApp</option>
                <option value="telegram">Telegram</option>
                <option value="linkedin">LinkedIn</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Detailed Incident Description *</label>
              <textarea name="detailed_description" required rows="6" placeholder="Please provide a detailed description of the entire incident, including timeline, communications, and any red flags you noticed..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>
          </div>

          <!-- Financial Information Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-dollar-sign" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Financial Information
            </h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Total Amount Lost (USD) *</label>
                <input type="number" name="amount_lost" required min="0" step="0.01" placeholder="0.00" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
              <div>
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Currency (if not USD)</label>
                <input type="text" name="currency" placeholder="e.g., EUR, GBP, CAD, etc." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Payment Methods Used *</label>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 0.5rem;">
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="bank_transfer" style="margin-right: 0.5rem;">
                  Bank Transfer/Wire Transfer
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="credit_card" style="margin-right: 0.5rem;">
                  Credit Card
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="debit_card" style="margin-right: 0.5rem;">
                  Debit Card
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="cryptocurrency" style="margin-right: 0.5rem;">
                  Cryptocurrency
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="paypal" style="margin-right: 0.5rem;">
                  PayPal
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="western_union" style="margin-right: 0.5rem;">
                  Western Union
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="moneygram" style="margin-right: 0.5rem;">
                  MoneyGram
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="gift_cards" style="margin-right: 0.5rem;">
                  Gift Cards
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="cash_app" style="margin-right: 0.5rem;">
                  Cash App
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="venmo" style="margin-right: 0.5rem;">
                  Venmo
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="zelle" style="margin-right: 0.5rem;">
                  Zelle
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="payment_methods" value="other" style="margin-right: 0.5rem;">
                  Other
                </label>
              </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Bank/Financial Institution Details</label>
              <textarea name="bank_details" rows="3" placeholder="Please provide details about your bank, account information (last 4 digits only), and any relevant financial institution details..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Destination Account Information (if known)</label>
              <textarea name="destination_account" rows="3" placeholder="If you know where your money was sent (account numbers, wallet addresses, recipient names, etc.), please provide those details here..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>
          </div>

          <!-- Evidence & Documentation Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-folder-open" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Evidence & Documentation
            </h3>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">What evidence do you have? (Check all that apply)</label>
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 0.5rem;">
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="emails" style="margin-right: 0.5rem;">
                  Email communications
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="text_messages" style="margin-right: 0.5rem;">
                  Text messages/SMS
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="social_media" style="margin-right: 0.5rem;">
                  Social media conversations
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="bank_statements" style="margin-right: 0.5rem;">
                  Bank statements
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="transaction_receipts" style="margin-right: 0.5rem;">
                  Transaction receipts
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="screenshots" style="margin-right: 0.5rem;">
                  Screenshots
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="phone_records" style="margin-right: 0.5rem;">
                  Phone call records
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="contracts" style="margin-right: 0.5rem;">
                  Contracts/Agreements
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="website_urls" style="margin-right: 0.5rem;">
                  Website URLs/Links
                </label>
                <label style="display: flex; align-items: center; color: #374151;">
                  <input type="checkbox" name="evidence_types" value="other" style="margin-right: 0.5rem;">
                  Other documentation
                </label>
              </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Scammer Contact Information (if known)</label>
              <textarea name="scammer_info" rows="4" placeholder="Please provide any information you have about the scammer(s): names, email addresses, phone numbers, social media profiles, website URLs, etc." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Additional Evidence Details</label>
              <textarea name="evidence_details" rows="4" placeholder="Please describe any additional evidence you have and how we can access it. Note: We will provide secure methods for sharing sensitive documents after initial consultation." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>
          </div>

          <!-- Previous Actions Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-history" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Previous Actions Taken
            </h3>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Have you reported this to law enforcement?</label>
              <select name="law_enforcement" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select...</option>
                <option value="yes_local">Yes - Local Police</option>
                <option value="yes_fbi">Yes - FBI/Federal Authorities</option>
                <option value="yes_ic3">Yes - IC3 (Internet Crime Complaint Center)</option>
                <option value="yes_international">Yes - International Authorities</option>
                <option value="yes_multiple">Yes - Multiple Agencies</option>
                <option value="no">No</option>
                <option value="planning">Planning to report</option>
              </select>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Have you contacted your bank/financial institution?</label>
              <select name="bank_contact" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select...</option>
                <option value="yes_immediately">Yes - Immediately after discovering fraud</option>
                <option value="yes_within_24h">Yes - Within 24 hours</option>
                <option value="yes_within_week">Yes - Within a week</option>
                <option value="yes_later">Yes - More than a week later</option>
                <option value="no">No</option>
                <option value="planning">Planning to contact</option>
              </select>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Have you worked with other recovery services?</label>
              <select name="other_services" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                <option value="">Select...</option>
                <option value="no">No</option>
                <option value="yes_successful">Yes - With some success</option>
                <option value="yes_unsuccessful">Yes - Without success</option>
                <option value="yes_ongoing">Yes - Currently working with others</option>
                <option value="yes_scammed">Yes - But was scammed by fake recovery service</option>
              </select>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Other Actions Taken</label>
              <textarea name="other_actions" rows="3" placeholder="Please describe any other actions you've taken to recover your funds or investigate the fraud..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>
          </div>

          <!-- Additional Information Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-info-circle" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Additional Information
            </h3>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Are there any time-sensitive factors we should know about?</label>
              <textarea name="time_sensitive" rows="3" placeholder="e.g., pending legal deadlines, account closures, ongoing communications with scammers, etc." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div style="margin-bottom: 1.5rem;">
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">What outcome are you hoping for?</label>
              <textarea name="desired_outcome" rows="3" placeholder="e.g., full fund recovery, partial recovery, identifying the scammer, preventing others from being scammed, etc." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>

            <div>
              <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Any additional information you'd like to share?</label>
              <textarea name="additional_info" rows="4" placeholder="Please share any other relevant information that might help us understand your case better..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
            </div>
          </div>

          <!-- Consent & Agreement Section -->
          <div style="margin-bottom: 3rem;">
            <h3 style="color: #111827; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid #e5e7eb;">
              <i class="fas fa-check-circle" style="color: #1e40af; margin-right: 0.5rem;"></i>
              Consent & Agreement
            </h3>

            <div style="background: #f8fafc; padding: 2rem; border-radius: 0.5rem; margin-bottom: 2rem;">
              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: flex-start; color: #374151;">
                  <input type="checkbox" name="consent_contact" required style="margin-right: 0.5rem; margin-top: 0.25rem;">
                  <span>I consent to being contacted by Forensic Involve regarding my case via the contact methods I've provided above. *</span>
                </label>
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: flex-start; color: #374151;">
                  <input type="checkbox" name="consent_information" required style="margin-right: 0.5rem; margin-top: 0.25rem;">
                  <span>I understand that the information I've provided will be used to assess my case and develop a recovery strategy. All information will be kept confidential. *</span>
                </label>
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: flex; align-items: flex-start; color: #374151;">
                  <input type="checkbox" name="consent_accuracy" required style="margin-right: 0.5rem; margin-top: 0.25rem;">
                  <span>I confirm that all information provided in this application is accurate and complete to the best of my knowledge. *</span>
                </label>
              </div>

              <div>
                <label style="display: flex; align-items: flex-start; color: #374151;">
                  <input type="checkbox" name="consent_consultation" required style="margin-right: 0.5rem; margin-top: 0.25rem;">
                  <span>I understand that this application is for an initial consultation and does not guarantee service acceptance or fund recovery. *</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Submit Section -->
          <div style="text-align: center; padding: 2rem; background: #f8fafc; border-radius: 1rem;">
            <h3 style="color: #111827; margin-bottom: 1rem;">Ready to Submit Your Application?</h3>
            <p style="color: #6b7280; margin-bottom: 2rem;">Our team will review your application and contact you within 24 hours to discuss your case and next steps.</p>

            <button type="submit" class="btn btn-primary btn-lg" style="margin-right: 1rem;">
              <i class="fas fa-paper-plane"></i>
              Submit Application
            </button>

            <a href="contact.html" class="btn btn-secondary btn-lg">
              <i class="fas fa-phone"></i>
              Call Instead
            </a>

            <div style="margin-top: 1.5rem; font-size: 0.875rem; color: #6b7280;">
              <i class="fas fa-shield-alt" style="color: #10b981; margin-right: 0.5rem;"></i>
              Your information is encrypted and secure
            </div>
          </div>
        </form>
      </div>
    </section>
  </main>

  <!-- Footer Placeholder -->
  <div id="footer-placeholder"></div>

  <!-- JavaScript -->
  <script src="js/main.js"></script>
<!-- Form Enhancement Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('applicationForm');
      const submitButton = form.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.innerHTML;

      // Add loading state to submit button
      function setLoadingState(loading) {
        if (loading) {
          submitButton.disabled = true;
          submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting Application...';
          submitButton.style.opacity = '0.7';
        } else {
          submitButton.disabled = false;
          submitButton.innerHTML = originalButtonText;
          submitButton.style.opacity = '1';
        }
      }

      // Show error message
      function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
          background: #fee2e2;
          border: 1px solid #fecaca;
          color: #dc2626;
          padding: 1rem;
          border-radius: 0.5rem;
          margin: 1rem 0;
          font-weight: 500;
        `;
        errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ' + message;

        // Remove any existing error messages
        const existingError = form.querySelector('.error-message');
        if (existingError) {
          existingError.remove();
        }

        errorDiv.className = 'error-message';
        form.insertBefore(errorDiv, form.firstChild);

        // Scroll to error message
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Show success message
      function showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.style.cssText = `
          background: #d1fae5;
          border: 1px solid #a7f3d0;
          color: #065f46;
          padding: 1rem;
          border-radius: 0.5rem;
          margin: 1rem 0;
          font-weight: 500;
        `;
        successDiv.innerHTML = '<i class="fas fa-check-circle"></i> ' + message;
        successDiv.className = 'success-message';
        form.insertBefore(successDiv, form.firstChild);

        // Scroll to success message
        successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      // Form submission handler
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Remove any existing messages
        const existingMessages = form.querySelectorAll('.error-message, .success-message');
        existingMessages.forEach(msg => msg.remove());

        // Basic client-side validation
        const requiredFields = form.querySelectorAll('[required]');
        let hasErrors = false;

        requiredFields.forEach(field => {
          if (!field.value.trim()) {
            field.style.borderColor = '#dc2626';
            hasErrors = true;
          } else {
            field.style.borderColor = '#d1d5db';
          }
        });

        // Check consent checkboxes
        const consentCheckboxes = form.querySelectorAll('input[name^="consent_"]');
        consentCheckboxes.forEach(checkbox => {
          if (!checkbox.checked) {
            checkbox.parentElement.style.color = '#dc2626';
            hasErrors = true;
          } else {
            checkbox.parentElement.style.color = '#374151';
          }
        });

        if (hasErrors) {
          showError('Please fill in all required fields and accept all consent agreements.');
          return;
        }

        // Set loading state
        setLoadingState(true);

        // Create FormData object
        const formData = new FormData(form);

        // Submit form via AJAX
        fetch('process_application.php', {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          setLoadingState(false);

          if (data.success) {
            showSuccess(data.message);

            // Redirect after 3 seconds
            setTimeout(() => {
              window.location.href = data.redirect || 'application-success.html';
            }, 3000);
          } else {
            showError(data.message || 'An error occurred while submitting your application. Please try again.');
          }
        })
        .catch(error => {
          setLoadingState(false);
          console.error('Error:', error);
          showError('A network error occurred. Please check your connection and try again.');
        });
      });

      // Real-time validation for email field
      const emailField = form.querySelector('input[type="email"]');
      if (emailField) {
        emailField.addEventListener('blur', function() {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (this.value && !emailRegex.test(this.value)) {
            this.style.borderColor = '#dc2626';
          } else {
            this.style.borderColor = '#d1d5db';
          }
        });
      }

      // Auto-format phone number
      const phoneField = form.querySelector('input[type="tel"]');
      if (phoneField) {
        phoneField.addEventListener('input', function() {
          // Remove all non-digit characters
          let value = this.value.replace(/\D/g, '');

          // Format based on length
          if (value.length >= 10) {
            // Format as: ******-123-4567
            value = value.replace(/(\d{1})(\d{3})(\d{3})(\d{4})/, '+$1-$2-$3-$4');
          }

          this.value = value;
        });
      }
    });
  </script>
</body>
</html>
