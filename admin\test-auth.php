<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

echo "<h2>Testing Admin Auth</h2>";
echo "<p>Session status: " . (session_status() === PHP_SESSION_ACTIVE ? 'ACTIVE' : 'INACTIVE') . "</p>";
echo "<p>Session data: <pre>" . print_r($_SESSION, true) . "</pre></p>";
echo "<p>isLoggedIn(): " . (isLoggedIn() ? 'TRUE' : 'FALSE') . "</p>";

// Check if user is logged in
if (!isLoggedIn()) {
    echo "<p>❌ User is NOT logged in. This is why admin pages are blank - they redirect to login.php</p>";
    echo "<p><a href='login.php'>Go to Login Page</a></p>";
    
    // Let's try to simulate a login
    echo "<h3>Simulating Login...</h3>";
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_full_name'] = 'Administrator';
    $_SESSION['admin_email'] = '<EMAIL>';
    
    echo "<p>✅ Session variables set</p>";
    echo "<p>isLoggedIn() after simulation: " . (isLoggedIn() ? 'TRUE' : 'FALSE') . "</p>";
    
    if (isLoggedIn()) {
        echo "<p>✅ Now you should be able to access admin pages</p>";
        echo "<ul>";
        echo "<li><a href='index.php'>Dashboard</a></li>";
        echo "<li><a href='posts.php'>Manage Posts</a></li>";
        echo "<li><a href='add-post.php'>Add New Post</a></li>";
        echo "<li><a href='edit-post.php'>Edit Post</a></li>";
        echo "</ul>";
    }
} else {
    echo "<p>✅ User IS logged in</p>";
    echo "<p>Admin pages should work fine</p>";
}
?>
