<?php
require_once 'admin/includes/db_config.php';

echo "<h2>Database Setup Verification</h2>";

try {
    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ admin_users table exists</p>";
        
        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT username, created_at FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "<p>✅ Admin user exists: " . htmlspecialchars($admin['username']) . " (created: " . $admin['created_at'] . ")</p>";
        } else {
            echo "<p>❌ Admin user does not exist. Creating now...</p>";
            
            // Create admin user
            $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (username, password, full_name, email, created_at) 
                VALUES ('admin', ?, 'Administrator', '<EMAIL>', NOW())
            ");
            $stmt->execute([$password_hash]);
            
            echo "<p>✅ Admin user created successfully!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> admin123</p>";
        }
        
    } else {
        echo "<p>❌ admin_users table does not exist. This should have been created automatically.</p>";
    }
    
    // Test session functionality
    session_start();
    echo "<p>✅ Session started successfully</p>";
    
    // Test CSRF token generation
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    echo "<p>✅ CSRF token generated: " . substr($_SESSION['csrf_token'], 0, 20) . "...</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h3>Ready to Test Login</h3>";
echo "<p><a href='admin/login.php' style='padding: 10px 20px; background: #1e40af; color: white; text-decoration: none; border-radius: 4px;'>Go to Admin Login</a></p>";
?>
