<?php
session_start();
require_once 'admin/includes/db_config.php';
require_once 'admin/includes/auth.php';

echo "<h2>🔐 Complete Login System Test</h2>";
echo "<style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f8fafc; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .success { color: #059669; }
    .error { color: #dc2626; }
    .info { color: #1d4ed8; }
    .form-test { background: #f1f5f9; padding: 15px; border-radius: 6px; margin: 15px 0; }
    button { padding: 10px 20px; background: #1e40af; color: white; border: none; border-radius: 4px; cursor: pointer; }
    input { padding: 8px; margin: 5px; border: 1px solid #d1d5db; border-radius: 4px; }
    a { color: #1e40af; text-decoration: none; }
    a:hover { text-decoration: underline; }
</style>";

// Test 1: Database Connection
echo "<div class='test-section'>";
echo "<h3>1. Database Connection Test</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
    $count = $stmt->fetch()['count'];
    echo "<p class='success'>✅ Database connected successfully</p>";
    echo "<p class='info'>📊 Found {$count} admin user(s) in database</p>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Admin User Verification
echo "<div class='test-section'>";
echo "<h3>2. Admin User Verification</h3>";
try {
    $stmt = $pdo->prepare("SELECT username, full_name, email, created_at FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user exists</p>";
        echo "<p class='info'>👤 Username: " . htmlspecialchars($admin['username']) . "</p>";
        echo "<p class='info'>📧 Email: " . htmlspecialchars($admin['email']) . "</p>";
        echo "<p class='info'>📅 Created: " . $admin['created_at'] . "</p>";
        
        // Test password
        $stmt = $pdo->prepare("SELECT password FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $user = $stmt->fetch();
        
        if ($user && password_verify('admin123', $user['password'])) {
            echo "<p class='success'>✅ Password verification successful</p>";
        } else {
            echo "<p class='error'>❌ Password verification failed</p>";
        }
    } else {
        echo "<p class='error'>❌ Admin user not found</p>";
        echo "<p class='info'>🔧 Creating admin user...</p>";
        
        $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, password, full_name, email, created_at) 
            VALUES ('admin', ?, 'Administrator', '<EMAIL>', NOW())
        ");
        $stmt->execute([$password_hash]);
        echo "<p class='success'>✅ Admin user created successfully!</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Admin user verification failed: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: Session Management
echo "<div class='test-section'>";
echo "<h3>3. Session Management Test</h3>";
echo "<p class='success'>✅ Session started</p>";
echo "<p class='info'>🆔 Session ID: " . session_id() . "</p>";

// Generate CSRF token
$csrf_token = generateCSRFToken();
echo "<p class='success'>✅ CSRF token generated</p>";
echo "<p class='info'>🔑 Token (first 20 chars): " . substr($csrf_token, 0, 20) . "...</p>";

// Test CSRF verification
$csrf_valid = verifyCSRFToken($csrf_token);
echo "<p class='" . ($csrf_valid ? 'success' : 'error') . "'>";
echo ($csrf_valid ? '✅' : '❌') . " CSRF token verification: " . ($csrf_valid ? 'VALID' : 'INVALID');
echo "</p>";
echo "</div>";

// Test 4: Authentication Function
echo "<div class='test-section'>";
echo "<h3>4. Authentication Function Test</h3>";

// Clear any existing login session for clean test
if (isset($_SESSION['admin_logged_in'])) {
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
}

$auth_result = authenticate('admin', 'admin123');
if ($auth_result) {
    echo "<p class='success'>✅ Authentication successful</p>";
    echo "<p class='info'>👤 Logged in as: " . $_SESSION['admin_username'] . "</p>";
    echo "<p class='info'>🆔 Admin ID: " . $_SESSION['admin_id'] . "</p>";
} else {
    echo "<p class='error'>❌ Authentication failed</p>";
}

$is_logged_in = isLoggedIn();
echo "<p class='" . ($is_logged_in ? 'success' : 'error') . "'>";
echo ($is_logged_in ? '✅' : '❌') . " Login status check: " . ($is_logged_in ? 'LOGGED IN' : 'NOT LOGGED IN');
echo "</p>";
echo "</div>";

// Test 5: Login Form Test
echo "<div class='test-section'>";
echo "<h3>5. Login Form Test</h3>";
echo "<p class='info'>📝 Test the actual login form below:</p>";
echo "<div class='form-test'>";
echo "<form method='POST' action='admin/login.php'>";
echo "<input type='hidden' name='csrf_token' value='" . $csrf_token . "'>";
echo "<p><label>Username: <input type='text' name='username' value='admin' required></label></p>";
echo "<p><label>Password: <input type='password' name='password' value='admin123' required></label></p>";
echo "<p><button type='submit'>🔐 Test Login</button></p>";
echo "</form>";
echo "</div>";
echo "</div>";

// Test 6: Dashboard Access Test
echo "<div class='test-section'>";
echo "<h3>6. Quick Access Links</h3>";
echo "<p><a href='admin/login.php' target='_blank'>🔐 Admin Login Page</a> (Username: admin, Password: admin123)</p>";
echo "<p><a href='admin/index.php' target='_blank'>📊 Admin Dashboard</a> (requires login)</p>";
echo "<p><a href='admin/posts.php' target='_blank'>📝 Manage Posts</a> (requires login)</p>";
echo "<p><a href='?logout=1'>🚪 Clear Session</a></p>";
echo "</div>";

// Logout functionality
if (isset($_GET['logout'])) {
    session_unset();
    session_destroy();
    echo "<script>window.location.reload();</script>";
}

echo "<div class='test-section'>";
echo "<h3>🎯 Summary</h3>";
echo "<p>The login system should now be working correctly. Use the credentials:</p>";
echo "<ul>";
echo "<li><strong>Username:</strong> admin</li>";
echo "<li><strong>Password:</strong> admin123</li>";
echo "</ul>";
echo "<p>If you're still getting the 'Invalid security token' error, try clearing your browser cookies and cache.</p>";
echo "</div>";
?>
