<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Path Test - Forensic Involve</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .result {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-weight: bold;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #10b981; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #ef4444; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #3b82f6; }
        .debug {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .btn {
            background: #1e40af;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .btn:hover { background: #1d4ed8; }
        h1 { color: #1e40af; text-align: center; }
    </style>
</head>
<body>
    <h1>🔧 API Path Test</h1>
    <p>Testing different API paths to find the correct one for your environment.</p>

    <div class="test-section">
        <h2>📍 Current Page Information</h2>
        <div id="page-info" class="info">Loading page information...</div>
    </div>

    <div class="test-section">
        <h2>🔌 API Path Tests</h2>
        <button onclick="testAllPaths()" class="btn">Test All API Paths</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>🔄 Contact Loader Test</h2>
        <button onclick="testContactLoader()" class="btn">Test Contact Loader</button>
        <div id="contact-loader-results"></div>
    </div>

    <script>
        // Display current page information
        function displayPageInfo() {
            const info = document.getElementById('page-info');
            const pageInfo = {
                'Full URL': window.location.href,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Pathname': window.location.pathname,
                'Origin': window.location.origin,
                'Base Directory': window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1)
            };

            let infoHtml = '';
            Object.keys(pageInfo).forEach(key => {
                infoHtml += `${key}: ${pageInfo[key]}\n`;
            });

            info.innerHTML = `<div class="debug">${infoHtml}</div>`;
        }

        // Test different API paths
        async function testAllPaths() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<div class="info">Testing API paths...</div>';

            const pathsToTest = [
                'api/get-contact-info.php',
                '../api/get-contact-info.php',
                './api/get-contact-info.php',
                '/api/get-contact-info.php',
                '/foresensic/api/get-contact-info.php',
                window.location.origin + '/api/get-contact-info.php',
                window.location.origin + '/foresensic/api/get-contact-info.php'
            ];

            let results = [];
            
            for (const path of pathsToTest) {
                try {
                    console.log(`Testing path: ${path}`);
                    const response = await fetch(path);
                    
                    if (response.ok) {
                        const text = await response.text();
                        
                        try {
                            const json = JSON.parse(text);
                            if (json.success) {
                                results.push(`✅ SUCCESS: ${path} - Valid JSON response`);
                            } else {
                                results.push(`⚠️ PARTIAL: ${path} - JSON but success=false: ${json.error || 'Unknown error'}`);
                            }
                        } catch (parseError) {
                            results.push(`❌ INVALID: ${path} - Not valid JSON (Status: ${response.status})`);
                        }
                    } else {
                        results.push(`❌ FAILED: ${path} - HTTP ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    results.push(`❌ ERROR: ${path} - ${error.message}`);
                }
            }

            resultsDiv.innerHTML = `<div class="debug">${results.join('\n')}</div>`;
        }

        // Test the contact loader with enhanced debugging
        async function testContactLoader() {
            const resultsDiv = document.getElementById('contact-loader-results');
            resultsDiv.innerHTML = '<div class="info">Testing contact loader...</div>';

            // Simulate the contact loader path resolution
            const currentPath = window.location.pathname;
            const isInSubdirectory = currentPath.includes('/admin/') || 
                                    currentPath.includes('/includes/') || 
                                    currentPath.includes('/js/') || 
                                    currentPath.includes('/css/');
            
            const apiPath = isInSubdirectory ? '../api/get-contact-info.php' : 'api/get-contact-info.php';

            let debugInfo = `Current Path: ${currentPath}\n`;
            debugInfo += `Is in subdirectory: ${isInSubdirectory}\n`;
            debugInfo += `Resolved API path: ${apiPath}\n\n`;

            try {
                console.log('Testing contact loader API path:', apiPath);
                const response = await fetch(apiPath);
                
                debugInfo += `Response Status: ${response.status} ${response.statusText}\n`;
                debugInfo += `Response OK: ${response.ok}\n`;
                debugInfo += `Response URL: ${response.url}\n\n`;

                if (response.ok) {
                    const responseText = await response.text();
                    debugInfo += `Response Length: ${responseText.length} characters\n`;
                    debugInfo += `Response Preview: ${responseText.substring(0, 200)}...\n\n`;

                    try {
                        const json = JSON.parse(responseText);
                        debugInfo += `JSON Parse: SUCCESS\n`;
                        debugInfo += `API Success: ${json.success}\n`;
                        
                        if (json.success) {
                            debugInfo += `Contact Data Available: ${!!json.data}\n`;
                            if (json.data) {
                                debugInfo += `Phones: ${JSON.stringify(json.data.phones)}\n`;
                                debugInfo += `Emails: ${JSON.stringify(json.data.emails)}\n`;
                                debugInfo += `Addresses: ${JSON.stringify(json.data.addresses)}\n`;
                            }
                            resultsDiv.innerHTML = `<div class="success">✅ Contact Loader API Working!</div><div class="debug">${debugInfo}</div>`;
                        } else {
                            debugInfo += `API Error: ${json.error || 'Unknown error'}\n`;
                            resultsDiv.innerHTML = `<div class="error">❌ API returned error</div><div class="debug">${debugInfo}</div>`;
                        }
                    } catch (parseError) {
                        debugInfo += `JSON Parse Error: ${parseError.message}\n`;
                        resultsDiv.innerHTML = `<div class="error">❌ Invalid JSON response</div><div class="debug">${debugInfo}</div>`;
                    }
                } else {
                    debugInfo += `HTTP Error: ${response.status} ${response.statusText}\n`;
                    resultsDiv.innerHTML = `<div class="error">❌ HTTP Error</div><div class="debug">${debugInfo}</div>`;
                }
            } catch (error) {
                debugInfo += `Network Error: ${error.message}\n`;
                resultsDiv.innerHTML = `<div class="error">❌ Network Error</div><div class="debug">${debugInfo}</div>`;
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            displayPageInfo();
        });
    </script>
</body>
</html>
