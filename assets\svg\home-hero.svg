<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Home Hero</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0A74DA" offset="0%"></stop>
            <stop stop-color="#003366" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFC107" offset="0%"></stop>
            <stop stop-color="#FD7E14" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Home-Hero" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="#F8F9FA" cx="400" cy="300" r="280"></circle>
        
        <!-- Central Shield -->
        <g id="Central-Shield" transform="translate(300.000000, 200.000000)">
            <path d="M100,0 C100,0 180,30 180,70 L180,140 C180,190 100,200 100,200 C100,200 20,190 20,140 L20,70 C20,30 100,0 100,0 Z" id="Shield-Base" fill="url(#linearGradient-1)"></path>
            <path d="M100,20 C100,20 160,40 160,70 L160,140 C160,180 100,185 100,185 C100,185 40,180 40,140 L40,70 C40,40 100,20 100,20 Z" id="Shield-Inner" fill="#FFFFFF"></path>
            <path d="M100,40 C100,40 140,55 140,75 L140,125 C140,155 100,165 100,165 C100,165 60,155 60,125 L60,75 C60,55 100,40 100,40 Z" id="Shield-Core" fill="url(#linearGradient-1)" opacity="0.3"></path>
            
            <!-- Checkmark -->
            <path d="M75,100 L90,115 L125,80" id="Checkmark" stroke="#FFFFFF" stroke-width="8" stroke-linecap="round" stroke-linejoin="round"></path>
        </g>
        
        <!-- Money Recovery Icons -->
        <g id="Money-Recovery" transform="translate(150.000000, 100.000000)">
            <!-- Dollar Sign 1 -->
            <g id="Dollar-1" transform="translate(0.000000, 50.000000)">
                <circle fill="url(#linearGradient-2)" cx="25" cy="25" r="25"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="28" font-weight="bold" fill="#FFFFFF">
                    <tspan x="15" y="33">$</tspan>
                </text>
            </g>
            
            <!-- Dollar Sign 2 -->
            <g id="Dollar-2" transform="translate(450.000000, 100.000000)">
                <circle fill="url(#linearGradient-2)" cx="25" cy="25" r="25"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="28" font-weight="bold" fill="#FFFFFF">
                    <tspan x="15" y="33">$</tspan>
                </text>
            </g>
            
            <!-- Dollar Sign 3 -->
            <g id="Dollar-3" transform="translate(100.000000, 250.000000)">
                <circle fill="url(#linearGradient-2)" cx="25" cy="25" r="25"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="28" font-weight="bold" fill="#FFFFFF">
                    <tspan x="15" y="33">$</tspan>
                </text>
            </g>
            
            <!-- Dollar Sign 4 -->
            <g id="Dollar-4" transform="translate(350.000000, 280.000000)">
                <circle fill="url(#linearGradient-2)" cx="25" cy="25" r="25"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="28" font-weight="bold" fill="#FFFFFF">
                    <tspan x="15" y="33">$</tspan>
                </text>
            </g>
        </g>
        
        <!-- Recovery Arrows -->
        <g id="Recovery-Arrows" transform="translate(200.000000, 200.000000)" stroke="url(#linearGradient-2)" stroke-width="3" fill="none">
            <path d="M0,100 C50,50 100,150 150,100 C200,50 250,150 300,100" id="Recovery-Path-1"></path>
            <path d="M50,200 C100,150 150,250 200,200 C250,150 300,250 350,200" id="Recovery-Path-2"></path>
            
            <!-- Arrow heads -->
            <polygon fill="url(#linearGradient-2)" points="290,95 300,100 290,105"></polygon>
            <polygon fill="url(#linearGradient-2)" points="340,195 350,200 340,205"></polygon>
        </g>
        
        <!-- Security Elements -->
        <g id="Security-Elements" transform="translate(100.000000, 400.000000)">
            <!-- Lock 1 -->
            <g id="Lock-1" transform="translate(0.000000, 0.000000)">
                <rect fill="url(#linearGradient-1)" x="0" y="20" width="40" height="30" rx="5"></rect>
                <path d="M10,20 L10,15 C10,8.92893219 14.9289322,4 21,4 L19,4 C25.0710678,4 30,8.92893219 30,15 L30,20" stroke="#343A40" stroke-width="4" stroke-linecap="round"></path>
                <circle fill="#FFFFFF" cx="20" cy="35" r="5"></circle>
            </g>
            
            <!-- Lock 2 -->
            <g id="Lock-2" transform="translate(500.000000, 0.000000)">
                <rect fill="url(#linearGradient-1)" x="0" y="20" width="40" height="30" rx="5"></rect>
                <path d="M10,20 L10,15 C10,8.92893219 14.9289322,4 21,4 L19,4 C25.0710678,4 30,8.92893219 30,15 L30,20" stroke="#343A40" stroke-width="4" stroke-linecap="round"></path>
                <circle fill="#FFFFFF" cx="20" cy="35" r="5"></circle>
            </g>
        </g>
        
        <!-- Network Connections -->
        <g id="Network" transform="translate(150.000000, 150.000000)" stroke="url(#linearGradient-1)" stroke-width="2" opacity="0.3">
            <line x1="0" y1="0" x2="100" y2="50"></line>
            <line x1="100" y1="50" x2="200" y2="0"></line>
            <line x1="200" y1="0" x2="300" y2="100"></line>
            <line x1="300" y1="100" x2="400" y2="50"></line>
            <line x1="400" y1="50" x2="500" y2="150"></line>
            
            <!-- Network Nodes -->
            <circle fill="url(#linearGradient-1)" cx="0" cy="0" r="5"></circle>
            <circle fill="url(#linearGradient-1)" cx="100" cy="50" r="5"></circle>
            <circle fill="url(#linearGradient-1)" cx="200" cy="0" r="5"></circle>
            <circle fill="url(#linearGradient-1)" cx="300" cy="100" r="5"></circle>
            <circle fill="url(#linearGradient-1)" cx="400" cy="50" r="5"></circle>
            <circle fill="url(#linearGradient-1)" cx="500" cy="150" r="5"></circle>
        </g>
        
        <!-- Decorative Elements -->
        <g id="Decorative" transform="translate(50.000000, 50.000000)" fill="url(#linearGradient-1)" opacity="0.1">
            <circle cx="50" cy="50" r="15"></circle>
            <circle cx="650" cy="100" r="20"></circle>
            <circle cx="100" cy="450" r="12"></circle>
            <circle cx="600" cy="400" r="18"></circle>
        </g>
    </g>
</svg>