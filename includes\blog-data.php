<?php
/**
 * Direct PHP Blog Data Provider
 * Alternative to API for more consistent data delivery
 */

// Prevent direct access
if (!isset($_GET['type'])) {
    http_response_code(403);
    exit('Direct access not allowed');
}

require_once '../admin/includes/db_config.php';

try {
    // Get parameters
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $category = isset($_GET['category']) ? $_GET['category'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : 'homepage';
    
    $offset = ($page - 1) * $limit;
    
    // Build query with proper featured image handling
    $query = "
        SELECT 
            id, 
            title, 
            slug,
            content, 
            excerpt, 
            featured_image,
            author, 
            category, 
            category_id,
            status,
            is_featured,
            created_at, 
            updated_at
        FROM blog_posts 
        WHERE status = 'published'
    ";
    
    $params = [];
    
    if ($category) {
        $query .= " AND category = ?";
        $params[] = $category;
    }
    
    if ($type === 'homepage') {
        $query .= " ORDER BY created_at DESC";
    } else {
        $query .= " ORDER BY created_at DESC";
    }
    
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    // Execute query
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $posts = $stmt->fetchAll();
    
    // Format posts for frontend with proper image path handling
    $formatted_posts = [];
    foreach ($posts as $post) {
        // Handle featured image path consistency
        $featured_image = $post['featured_image'];
        if ($featured_image) {
            // Ensure consistent path format (compatible with older PHP versions)
            if (strpos($featured_image, 'admin/') !== 0 && strpos($featured_image, 'http') !== 0) {
                $featured_image = 'admin/' . ltrim($featured_image, '/');
            }
            
            // Verify file exists
            $full_path = '../' . $featured_image;
            if (!file_exists($full_path)) {
                // Try alternative paths
                $alt_paths = [
                    '../admin/uploads/' . basename($featured_image),
                    '../uploads/' . basename($featured_image),
                    '../images/' . basename($featured_image)
                ];
                
                $featured_image = null; // Default to null if not found
                foreach ($alt_paths as $alt_path) {
                    if (file_exists($alt_path)) {
                        $featured_image = str_replace('../', '', $alt_path);
                        break;
                    }
                }
            }
        }
        
        $formatted_posts[] = [
            'id' => $post['id'],
            'title' => $post['title'],
            'slug' => $post['slug'] ?: 'post-' . $post['id'],
            'excerpt' => $post['excerpt'] ?: substr(strip_tags($post['content']), 0, 150) . '...',
            'content' => $post['content'],
            'author' => $post['author'] ?: 'Admin',
            'category' => $post['category'] ?: 'News',
            'featured_image' => $featured_image,
            'created_at' => $post['created_at'],
            'updated_at' => $post['updated_at'],
            'formatted_date' => date('F j, Y', strtotime($post['created_at'])),
            'is_featured' => (bool)$post['is_featured']
        ];
    }
    
    // Get total count for pagination
    $countQuery = "
        SELECT COUNT(*) as total 
        FROM blog_posts 
        WHERE status = 'published'
    ";
    
    $countParams = [];
    if ($category) {
        $countQuery .= " AND category = ?";
        $countParams[] = $category;
    }
    
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($countParams);
    $total_posts = $stmt->fetchColumn();
    
    $total_pages = ceil($total_posts / $limit);
    
    // Prepare response
    $response = [
        'success' => true,
        'posts' => $formatted_posts,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_posts' => $total_posts,
            'per_page' => $limit,
            'has_next' => $page < $total_pages,
            'has_prev' => $page > 1
        ],
        'environment' => $environment ?? 'unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage(),
        'posts' => [],
        'pagination' => [
            'current_page' => 1,
            'total_pages' => 0,
            'total_posts' => 0,
            'per_page' => $limit,
            'has_next' => false,
            'has_prev' => false
        ]
    ];
}

// Close database connection
if (isset($mysqli)) {
    $mysqli->close();
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Blog Data</title>
</head>
<body>
    <div id="blog-data" style="display: none;"><?php echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></div>
    
    <!-- Debug information (only visible in development) -->
    <?php if (($environment ?? 'development') === 'development'): ?>
    <div style="font-family: monospace; font-size: 12px; color: #666; margin: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px;">
        <strong>Debug Info:</strong><br>
        Environment: <?php echo $environment ?? 'unknown'; ?><br>
        Database: <?php echo $db_name; ?><br>
        Query Limit: <?php echo $limit; ?><br>
        Posts Found: <?php echo count($formatted_posts ?? []); ?><br>
        Total Posts: <?php echo $total_posts ?? 0; ?><br>
        Category Filter: <?php echo $category ?: 'None'; ?><br>
        Page Type: <?php echo $type; ?><br>
        Timestamp: <?php echo date('Y-m-d H:i:s'); ?>
    </div>
    <?php endif; ?>
</body>
</html>
