<?php
/**
 * Database Connection Debug Script
 * Use this to test and debug database connection issues
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Database Connection Debug</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .success { color: #10b981; background: #d1fae5; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #ef4444; background: #fee2e2; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #1e40af; background: #dbeafe; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .debug { background: #f3f4f6; padding: 15px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
        h1, h2 { color: #1e40af; }
    </style>
</head>
<body>";

echo "<h1>🔧 Database Connection Debug</h1>";

// Step 1: Environment Detection
echo "<h2>1. Environment Detection</h2>";
$server_name = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';
echo "<div class='info'>Server Name: <strong>$server_name</strong></div>";

// Detect environment using the same logic as db_config.php
function detectEnvironment() {
    $server_name = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    if (strpos($server_name, 'localhost') !== false || 
        strpos($server_name, '127.0.0.1') !== false ||
        strpos($server_name, '192.168.') !== false ||
        strpos($server_name, '10.0.') !== false ||
        strpos($server_name, '.local') !== false ||
        strpos($server_name, ':8080') !== false ||
        strpos($server_name, ':3000') !== false ||
        strpos($server_name, 'dev.') !== false ||
        strpos($server_name, 'test.') !== false ||
        strpos($server_name, 'staging.') !== false) {
        return 'development';
    }
    
    return 'production';
}

$environment = detectEnvironment();
echo "<div class='success'>Detected Environment: <strong>$environment</strong></div>";

// Step 2: Database Configuration
echo "<h2>2. Database Configuration</h2>";

if ($environment === 'production') {
    $db_host = 'localhost';
    $db_username = 'u659553769_news';
    $db_password = 'Money2025@Demo#';
    $db_name = 'u659553769_news';
    echo "<div class='info'>Using <strong>Production</strong> database settings</div>";
} else {
    $db_host = 'localhost';
    $db_username = 'root';
    $db_password = 'root';
    $db_name = 'forensics_involve';
    echo "<div class='info'>Using <strong>Development</strong> database settings</div>";
}

echo "<div class='debug'>
Host: $db_host
Username: $db_username
Password: " . str_repeat('*', strlen($db_password)) . "
Database: $db_name
</div>";

// Step 3: Test Connection
echo "<h2>3. Connection Test</h2>";

try {
    $mysqli = new mysqli($db_host, $db_username, $db_password);
    
    if ($mysqli->connect_error) {
        echo "<div class='error'>❌ Connection Failed: " . $mysqli->connect_error . "</div>";
        echo "<div class='info'>Error Code: " . $mysqli->connect_errno . "</div>";
        
        // Common error solutions
        echo "<h3>💡 Common Solutions:</h3>";
        echo "<ul>";
        echo "<li><strong>Error 1045:</strong> Wrong username/password</li>";
        echo "<li><strong>Error 2002:</strong> MySQL server not running</li>";
        echo "<li><strong>Error 1049:</strong> Database doesn't exist</li>";
        echo "<li><strong>Error 1044:</strong> User doesn't have permission</li>";
        echo "</ul>";
        
    } else {
        echo "<div class='success'>✅ Connection Successful!</div>";
        echo "<div class='info'>MySQL Version: " . $mysqli->server_info . "</div>";
        
        // Test database selection
        echo "<h3>4. Database Selection Test</h3>";
        if ($mysqli->select_db($db_name)) {
            echo "<div class='success'>✅ Database '$db_name' selected successfully</div>";
            
            // Test table existence
            echo "<h3>5. Table Check</h3>";
            $tables = ['admin_users', 'blog_posts', 'categories'];
            foreach ($tables as $table) {
                $result = $mysqli->query("SHOW TABLES LIKE '$table'");
                if ($result && $result->num_rows > 0) {
                    echo "<div class='success'>✅ Table '$table' exists</div>";
                    
                    // Count records
                    $count_result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
                    if ($count_result) {
                        $count = $count_result->fetch_assoc()['count'];
                        echo "<div class='info'>   Records: $count</div>";
                    }
                } else {
                    echo "<div class='error'>❌ Table '$table' missing</div>";
                }
            }
            
        } else {
            echo "<div class='error'>❌ Cannot select database '$db_name': " . $mysqli->error . "</div>";
            
            // Show available databases
            echo "<h3>Available Databases:</h3>";
            $result = $mysqli->query("SHOW DATABASES");
            if ($result) {
                echo "<ul>";
                while ($row = $result->fetch_assoc()) {
                    echo "<li>" . $row['Database'] . "</li>";
                }
                echo "</ul>";
            }
        }
        
        $mysqli->close();
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Exception: " . $e->getMessage() . "</div>";
}

// Step 4: API Test
echo "<h2>6. API Test</h2>";
$api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/get-posts.php';
echo "<div class='info'>Testing API: <a href='$api_url' target='_blank'>$api_url</a></div>";

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$response = @file_get_contents($api_url, false, $context);

if ($response !== false) {
    $json_data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<div class='success'>✅ API responding with valid JSON</div>";
        if (isset($json_data['success'])) {
            if ($json_data['success']) {
                echo "<div class='success'>✅ API success: true</div>";
                if (isset($json_data['posts'])) {
                    echo "<div class='info'>Posts returned: " . count($json_data['posts']) . "</div>";
                }
                if (isset($json_data['debug'])) {
                    echo "<div class='debug'>API Debug Info:\n" . json_encode($json_data['debug'], JSON_PRETTY_PRINT) . "</div>";
                }
            } else {
                echo "<div class='error'>❌ API success: false</div>";
                if (isset($json_data['error'])) {
                    echo "<div class='error'>Error: " . $json_data['error'] . "</div>";
                }
            }
        }
    } else {
        echo "<div class='error'>❌ API returned invalid JSON</div>";
        echo "<div class='debug'>Response (first 500 chars):\n" . htmlspecialchars(substr($response, 0, 500)) . "</div>";
    }
} else {
    echo "<div class='error'>❌ API request failed</div>";
    $error = error_get_last();
    if ($error) {
        echo "<div class='error'>Error: " . $error['message'] . "</div>";
    }
}

// Step 5: File Permissions
echo "<h2>7. File Permissions Check</h2>";
$files_to_check = [
    'admin/includes/db_config.php',
    'api/get-posts.php',
    'api/get-contact-info.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (is_readable($file)) {
            echo "<div class='success'>✅ $file is readable</div>";
        } else {
            echo "<div class='error'>❌ $file is not readable</div>";
        }
    } else {
        echo "<div class='error'>❌ $file does not exist</div>";
    }
}

echo "<h2>8. Recommendations</h2>";
echo "<div class='info'>";
echo "<h3>If you're seeing errors:</h3>";
echo "<ol>";
echo "<li><strong>Database Connection Issues:</strong> Verify your hosting provider's database credentials</li>";
echo "<li><strong>API 404 Errors:</strong> Check if the API files exist and have correct permissions</li>";
echo "<li><strong>API 500 Errors:</strong> Check the database connection and table structure</li>";
echo "<li><strong>JSON Parse Errors:</strong> The server is returning HTML instead of JSON (usually due to PHP errors)</li>";
echo "</ol>";
echo "</div>";

echo "</body></html>";
?>
