<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Function to generate URL-friendly slug from title
function generateSlug($title) {
    // Convert to lowercase and remove special characters
    $slug = strtolower(trim($title));
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    $slug = preg_replace('/[\s_-]+/', '-', $slug);
    $slug = trim($slug, '-');

    // Ensure slug is not empty
    if (empty($slug)) {
        $slug = 'post-' . time();
    }

    return $slug;
}

$message = '';
$error = '';
$post = null;
$is_edit = false;

// Check if editing existing post
if (isset($_GET['id'])) {
    $post_id = (int)$_GET['id'];
    try {        $stmt = $pdo->prepare("SELECT * FROM blog_posts WHERE id = ?");
        $stmt->execute([$post_id]);
        $post = $stmt->fetch();

        if ($post) {
            $is_edit = true;
        } else {
            $error = 'Post not found.';
        }
    } catch (PDOException $e) {
        $error = 'Database error: ' . $e->getMessage();
    }
}

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// Handle form submission
if ($_POST) {    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $title = trim($_POST['title'] ?? '');
        $content = trim($_POST['content'] ?? '');
        $excerpt = trim($_POST['excerpt'] ?? '');
        $category_id = (int)($_POST['category_id'] ?? 0);
        $status = $_POST['status'] ?? 'draft';
        $featured_image = trim($_POST['featured_image'] ?? '');
        $current_admin = getCurrentAdmin();

        // Generate slug from title
        $slug = generateSlug($title);

        // Get category name from category_id or set default
        $category = 'General';
        if ($category_id > 0) {
            $stmt = $pdo->prepare("SELECT name FROM categories WHERE id = ?");
            $stmt->execute([$category_id]);
            $cat_result = $stmt->fetch();
            if ($cat_result) {
                $category = $cat_result['name'];
            }
        }

        // Enhanced input validation and sanitization
        if (empty($title)) {
            $error = 'Title is required.';
        } elseif (strlen($title) > 255) {
            $error = 'Title must be 255 characters or less.';
        } elseif (empty($content)) {
            $error = 'Content is required.';
        } elseif (strlen($content) > 65535) {
            $error = 'Content is too long. Please reduce the length.';
        } elseif (strlen($excerpt) > 500) {
            $error = 'Excerpt must be 500 characters or less.';
        } elseif (!in_array($status, ['published', 'draft', 'archived'])) {            $error = 'Invalid status.';
        } elseif ($category_id < 0) {
            $error = 'Invalid category ID.';
        } else {
            // Sanitize inputs
            $title = htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
            $content = htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
            $excerpt = htmlspecialchars($excerpt, ENT_QUOTES, 'UTF-8');
            $status = htmlspecialchars($status, ENT_QUOTES, 'UTF-8');

            try {
                if ($is_edit) {
                    // Update existing post
                    $stmt = $pdo->prepare("
                        UPDATE blog_posts
                        SET title = ?, slug = ?, content = ?, excerpt = ?, category_id = ?, category = ?, status = ?, featured_image = ?, author = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $result = $stmt->execute([$title, $slug, $content, $excerpt, $category_id ?: null, $category, $status, $featured_image ?: null, $current_admin['username'], $post['id']]);

                    if ($result) {
                        $message = 'Post updated successfully.';                        // Refresh post data
                        $stmt = $pdo->prepare("SELECT * FROM blog_posts WHERE id = ?");
                        $stmt->execute([$post['id']]);
                        $post = $stmt->fetch();
                    } else {
                        $error = 'Failed to update post.';
                    }                } else {
                    // Create new post
                    $stmt = $pdo->prepare("
                        INSERT INTO blog_posts (title, slug, content, excerpt, category_id, category, status, featured_image, author, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                    ");
                    $result = $stmt->execute([$title, $slug, $content, $excerpt, $category_id ?: null, $category, $status, $featured_image ?: null, $current_admin['username']]);

                    if ($result) {
                        $new_post_id = $pdo->lastInsertId();
                        $message = 'Post created successfully.';

                        // Redirect to edit page for the new post
                        header("Location: edit-post.php?id=$new_post_id&message=" . urlencode($message));
                        exit();
                    } else {
                        $error = 'Failed to create post.';
                    }
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Handle message from redirect
if (isset($_GET['message'])) {
    $message = $_GET['message'];
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $is_edit ? 'Edit Post' : 'Add New Post'; ?> - Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars(getCurrentAdmin()['username']); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item <?php echo !$is_edit ? 'active' : ''; ?>">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item">
                <span class="icon">📁</span>
                Categories
            </a>
            <a href="media.php" class="menu-item">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1><?php echo $is_edit ? 'Edit Post' : 'Add New Post'; ?></h1>
            <p><?php echo $is_edit ? 'Update your blog post or news article' : 'Create a new blog post or news article'; ?></p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <div class="post-editor">
            <form method="POST" class="post-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                <div class="form-row">
                    <div class="form-group">
                        <label for="title">Post Title *</label>
                        <input type="text" id="title" name="title" required
                               value="<?php echo htmlspecialchars($post['title'] ?? ''); ?>"
                               placeholder="Enter a compelling title for your post">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group form-group-half">
                        <label for="category_id">Category</label>
                        <select id="category_id" name="category_id">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"
                                        <?php echo ($post['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group form-group-half">
                        <label for="status">Status</label>
                        <select id="status" name="status">
                            <option value="draft" <?php echo ($post['status'] ?? 'draft') === 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="published" <?php echo ($post['status'] ?? '') === 'published' ? 'selected' : ''; ?>>Published</option>
                            <option value="archived" <?php echo ($post['status'] ?? '') === 'archived' ? 'selected' : ''; ?>>Archived</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="excerpt">Excerpt</label>
                        <textarea id="excerpt" name="excerpt" rows="3"
                                  placeholder="Brief summary of the post (optional)"><?php echo htmlspecialchars($post['excerpt'] ?? ''); ?></textarea>
                        <small>This will be shown in post previews and search results.</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="featured_image">Featured Image</label>
                        <div class="media-picker">
                            <input type="hidden" id="featured_image" name="featured_image" value="<?php echo htmlspecialchars($post['featured_image'] ?? ''); ?>">
                            <div class="media-preview" id="mediaPreview">
                                <?php if (!empty($post['featured_image'])): ?>
                                    <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" alt="Featured Image" style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 8px;">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="removeImage()" style="margin-top: 10px;">Remove Image</button>
                                <?php else: ?>
                                    <div class="no-image-placeholder">
                                        <div style="padding: 40px; text-align: center; border: 2px dashed #cbd5e1; border-radius: 8px; background: #f8fafc;">
                                            <div style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;">🖼️</div>
                                            <p style="margin: 0; color: #64748b;">No image selected</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div style="margin-top: 10px;">
                                <button type="button" class="btn btn-outline" onclick="openMediaPicker()">Choose Image</button>
                                <a href="media.php" target="_blank" class="btn btn-outline" style="margin-left: 10px;">Upload New</a>
                            </div>
                        </div>
                        <small>Select a featured image for this post. This will be displayed in post previews and the single post page.</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="content">Content *</label>
                        <div class="editor-toolbar">
                            <button type="button" class="toolbar-btn" onclick="insertFormatting('**', '**')" title="Bold">
                                <strong>B</strong>
                            </button>
                            <button type="button" class="toolbar-btn" onclick="insertFormatting('*', '*')" title="Italic">
                                <em>I</em>
                            </button>
                            <button type="button" class="toolbar-btn" onclick="insertFormatting('[', '](url)')" title="Link">
                                🔗
                            </button>
                            <button type="button" class="toolbar-btn" onclick="insertFormatting('## ', '')" title="Heading">
                                H2
                            </button>
                            <button type="button" class="toolbar-btn" onclick="insertFormatting('- ', '')" title="List">
                                📋
                            </button>
                        </div>
                        <textarea id="content" name="content" rows="20" required
                                  placeholder="Write your post content here. You can use Markdown formatting."><?php echo htmlspecialchars($post['content'] ?? ''); ?></textarea>
                        <small>Supports Markdown formatting. Use the toolbar buttons for quick formatting.</small>
                    </div>
                </div>

                <div class="form-actions">
                    <a href="posts.php" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <?php echo $is_edit ? 'Update Post' : 'Create Post'; ?>
                    </button>
                </div>
            </form>
        </div>

        <?php if ($is_edit): ?>
            <div class="post-actions">
                <h3>Post Actions</h3>
                <div class="action-buttons">
                    <a href="posts.php" class="btn btn-outline">← Back to Posts</a>
                    <?php if ($post['status'] === 'published'): ?>
                        <a href="../news.html" target="_blank" class="btn btn-outline">View on Site</a>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </main>

    <script src="js/admin.js"></script>
    <script>
        function insertFormatting(before, after) {
            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const selectedText = textarea.value.substring(start, end);

            const newText = before + selectedText + after;
            textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);

            // Set cursor position
            const newCursorPos = start + before.length + selectedText.length;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            textarea.focus();
        }

        // Auto-save draft functionality
        let autoSaveTimer;
        const form = document.querySelector('.post-form');
        const inputs = form.querySelectorAll('input, textarea, select');

        function autoSave() {
            // Only auto-save if this is an edit and content exists
            <?php if ($is_edit): ?>
                const title = document.getElementById('title').value.trim();
                if (title) {
                    console.log('Auto-saving draft...');
                    // Could implement AJAX auto-save here
                }
            <?php endif; ?>
        }

        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(autoSaveTimer);
                autoSaveTimer = setTimeout(autoSave, 10000); // Auto-save after 10 seconds of inactivity
            });
        });

        // Media picker functionality
        function openMediaPicker() {
            // Open media picker modal
            const modal = document.createElement('div');
            modal.id = 'mediaPicker';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 8px; width: 90%; max-width: 800px; max-height: 80%; overflow: hidden;">
                    <div style="padding: 20px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0;">Select Featured Image</h3>
                        <button onclick="closeMediaPicker()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer;">&times;</button>
                    </div>
                    <div style="padding: 20px; max-height: 500px; overflow-y: auto;">
                        <div id="mediaGrid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px;">
                            <div style="text-align: center; padding: 40px;">Loading images...</div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            loadMediaFiles();
        }

        function closeMediaPicker() {
            const modal = document.getElementById('mediaPicker');
            if (modal) {
                modal.remove();
            }
        }

        function loadMediaFiles() {
            fetch('api/get-media.php')
                .then(response => response.json())
                .then(data => {
                    const grid = document.getElementById('mediaGrid');
                    if (data.success && data.files.length > 0) {
                        grid.innerHTML = data.files.map(file => `
                            <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden; cursor: pointer; transition: transform 0.2s;"
                                 onclick="selectImage('${file.file_path}', '${file.original_name}')">
                                <img src="${file.file_path}" alt="${file.original_name}"
                                     style="width: 100%; height: 120px; object-fit: cover;">
                                <div style="padding: 8px; font-size: 0.8rem; text-align: center;">
                                    ${file.original_name}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        grid.innerHTML = '<div style="text-align: center; padding: 40px; color: #64748b;">No images found. <a href="media.php" target="_blank">Upload some images</a> first.</div>';
                    }
                })
                .catch(error => {
                    console.error('Error loading media files:', error);
                    document.getElementById('mediaGrid').innerHTML = '<div style="text-align: center; padding: 40px; color: #ef4444;">Error loading images</div>';
                });
        }

        function selectImage(imagePath, imageName) {
            document.getElementById('featured_image').value = imagePath;
            updateMediaPreview(imagePath, imageName);
            closeMediaPicker();
        }

        function updateMediaPreview(imagePath, imageName) {
            const preview = document.getElementById('mediaPreview');
            preview.innerHTML = `
                <img src="${imagePath}" alt="${imageName}" style="max-width: 200px; max-height: 150px; object-fit: cover; border-radius: 8px;">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeImage()" style="margin-top: 10px;">Remove Image</button>
            `;
        }

        function removeImage() {
            document.getElementById('featured_image').value = '';
            const preview = document.getElementById('mediaPreview');
            preview.innerHTML = `
                <div class="no-image-placeholder">
                    <div style="padding: 40px; text-align: center; border: 2px dashed #cbd5e1; border-radius: 8px; background: #f8fafc;">
                        <div style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;">🖼️</div>
                        <p style="margin: 0; color: #64748b;">No image selected</p>
                    </div>
                </div>
            `;
        }
    </script>

    <style>
        .post-editor {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .post-form {
            max-width: none;
        }

        .form-row {
            margin-bottom: 1.5rem;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-group {
            width: 100%;
        }

        .form-group-half {
            width: calc(50% - 0.75rem);
            display: inline-block;
        }

        .form-group-half:first-child {
            margin-right: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-family: inherit;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .form-group small {
            display: block;
            margin-top: 0.5rem;
            color: var(--gray-500);
            font-size: 0.875rem;
        }

        .editor-toolbar {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: var(--gray-50);
            border: 1px solid var(--gray-300);
            border-bottom: none;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .toolbar-btn {
            padding: 0.5rem;
            border: none;
            background: var(--white);
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: background-color 0.2s;
        }

        .toolbar-btn:hover {
            background: var(--gray-100);
        }

        #content {
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            border-top: none;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--gray-200);
        }

        .post-actions {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 1.5rem;
        }

        .post-actions h3 {
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .action-buttons {
            display: flex;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .form-group-half {
                width: 100%;
                display: block;
            }

            .form-group-half:first-child {
                margin-right: 0;
                margin-bottom: 1.5rem;
            }

            .form-actions {
                flex-direction: column;
            }

            .editor-toolbar {
                flex-wrap: wrap;
            }
        }
    </style>
</body>
</html>
