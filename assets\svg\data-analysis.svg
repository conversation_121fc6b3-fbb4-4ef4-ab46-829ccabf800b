<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Data Analysis</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0A74DA" offset="0%"></stop>
            <stop stop-color="#003366" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFC107" offset="0%"></stop>
            <stop stop-color="#FD7E14" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Data-Analysis" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="#F8F9FA" cx="400" cy="300" r="250"></circle>
        
        <!-- Dashboard -->
        <g id="Dashboard" transform="translate(250.000000, 150.000000)">
            <rect id="Dashboard-Base" fill="#FFFFFF" x="0" y="0" width="300" height="200" rx="10"></rect>
            <rect id="Dashboard-Header" fill="url(#linearGradient-1)" x="0" y="0" width="300" height="40" rx="10"></rect>
            <rect id="Dashboard-Header-Bottom" fill="url(#linearGradient-1)" x="0" y="30" width="300" height="10"></rect>
            
            <!-- Charts -->
            <g id="Charts" transform="translate(20.000000, 60.000000)">
                <!-- Bar Chart -->
                <g id="Bar-Chart" transform="translate(0.000000, 0.000000)">
                    <rect fill="url(#linearGradient-1)" opacity="0.3" x="0" y="60" width="10" height="20"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.5" x="15" y="40" width="10" height="40"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.7" x="30" y="20" width="10" height="60"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.9" x="45" y="0" width="10" height="80"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.7" x="60" y="30" width="10" height="50"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.5" x="75" y="50" width="10" height="30"></rect>
                    <rect fill="url(#linearGradient-1)" opacity="0.3" x="90" y="65" width="10" height="15"></rect>
                </g>
                
                <!-- Pie Chart -->
                <g id="Pie-Chart" transform="translate(130.000000, 40.000000)">
                    <circle fill="#FFFFFF" cx="40" cy="40" r="40"></circle>
                    <path d="M40,40 L40,0 A40,40 0 0,1 80,40 Z" fill="url(#linearGradient-1)" opacity="0.9"></path>
                    <path d="M40,40 L80,40 A40,40 0 0,1 40,80 Z" fill="url(#linearGradient-1)" opacity="0.7"></path>
                    <path d="M40,40 L40,80 A40,40 0 0,1 0,40 Z" fill="url(#linearGradient-1)" opacity="0.5"></path>
                    <path d="M40,40 L0,40 A40,40 0 0,1 40,0 Z" fill="url(#linearGradient-2)" opacity="0.7"></path>
                </g>
                
                <!-- Line Chart -->
                <g id="Line-Chart" transform="translate(0.000000, 120.000000)">
                    <polyline stroke="url(#linearGradient-1)" stroke-width="3" points="0,15 40,5 80,20 120,0 160,10 200,5 240,15"></polyline>
                    <circle fill="url(#linearGradient-1)" cx="0" cy="15" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="40" cy="5" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="80" cy="20" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="120" cy="0" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="160" cy="10" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="200" cy="5" r="3"></circle>
                    <circle fill="url(#linearGradient-1)" cx="240" cy="15" r="3"></circle>
                </g>
            </g>
        </g>
        
        <!-- Magnifying Glass -->
        <g id="Magnifying-Glass" transform="translate(450.000000, 100.000000)">
            <circle id="Glass" stroke="#343A40" stroke-width="10" fill="#FFFFFF" opacity="0.7" cx="50" cy="50" r="50"></circle>
            <line x1="85" y1="85" x2="120" y2="120" id="Handle" stroke="#343A40" stroke-width="15" stroke-linecap="round"></line>
            <circle id="Reflection" fill="#FFFFFF" opacity="0.5" cx="35" cy="35" r="10"></circle>
        </g>
        
        <!-- Data Points -->
        <g id="Data-Points" transform="translate(200.000000, 100.000000)" fill="#0A74DA" opacity="0.2">
            <circle cx="50" cy="50" r="5"></circle>
            <circle cx="100" cy="75" r="5"></circle>
            <circle cx="150" cy="25" r="5"></circle>
            <circle cx="200" cy="100" r="5"></circle>
            <circle cx="250" cy="50" r="5"></circle>
            <circle cx="300" cy="75" r="5"></circle>
            <circle cx="350" cy="25" r="5"></circle>
            <circle cx="400" cy="100" r="5"></circle>
        </g>
        
        <!-- Connection Lines -->
        <g id="Connection-Lines" transform="translate(200.000000, 100.000000)" stroke="#0A74DA" stroke-width="1" opacity="0.2">
            <line x1="50" y1="50" x2="100" y2="75"></line>
            <line x1="100" y1="75" x2="150" y2="25"></line>
            <line x1="150" y1="25" x2="200" y2="100"></line>
            <line x1="200" y1="100" x2="250" y2="50"></line>
            <line x1="250" y1="50" x2="300" y2="75"></line>
            <line x1="300" y1="75" x2="350" y2="25"></line>
            <line x1="350" y1="25" x2="400" y2="100"></line>
        </g>
    </g>
</svg>