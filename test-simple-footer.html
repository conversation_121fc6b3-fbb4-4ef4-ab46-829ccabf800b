<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Footer Test - Forensic Involve</title>
    <link rel="stylesheet" href="css/modern.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .test-content {
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
        }
        
        .test-header {
            background: #1e40af;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .test-info {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .status-check {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
        }
        
        .status-check.success {
            background: #f0fdf4;
            border-left-color: #10b981;
        }
        
        .status-check i {
            font-size: 1.2rem;
        }
        
        .success {
            color: #10b981;
        }
        
        .info {
            color: #0ea5e9;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🧪 Simple Footer Test</h1>
        <p>Testing the new static footer without dynamic loading</p>
    </div>
    
    <div class="test-content">
        <div class="test-info">
            <h2>✅ Footer Status Checks</h2>
            
            <div class="status-check success">
                <i class="fas fa-check-circle success"></i>
                <div>
                    <strong>Static Contact Information</strong><br>
                    Footer now shows contact details immediately without API calls
                </div>
            </div>
            
            <div class="status-check success">
                <i class="fas fa-phone success"></i>
                <div>
                    <strong>Clickable Phone Numbers</strong><br>
                    Phone numbers are now clickable links with tel: protocol
                </div>
            </div>
            
            <div class="status-check success">
                <i class="fas fa-envelope success"></i>
                <div>
                    <strong>Clickable Email Addresses</strong><br>
                    Email addresses are now clickable links with mailto: protocol
                </div>
            </div>
            
            <div class="status-check success">
                <i class="fas fa-exclamation-triangle" style="color: #fbbf24;"></i>
                <div>
                    <strong>Emergency Line Highlighted</strong><br>
                    Emergency phone number is highlighted in yellow/orange
                </div>
            </div>
            
            <div class="status-check success">
                <i class="fas fa-clock success"></i>
                <div>
                    <strong>Business Hours Displayed</strong><br>
                    24/7 availability clearly shown with green highlighting
                </div>
            </div>
            
            <div class="status-check info">
                <i class="fas fa-info-circle info"></i>
                <div>
                    <strong>No JavaScript Dependencies</strong><br>
                    Footer works immediately without waiting for API responses
                </div>
            </div>
        </div>
        
        <div class="test-info">
            <h3>📋 What's New in This Footer:</h3>
            <ul>
                <li><strong>Immediate Display:</strong> No more "Loading..." text</li>
                <li><strong>Complete Contact Info:</strong> Two addresses, two phone numbers, two emails</li>
                <li><strong>Emergency Highlighting:</strong> Emergency phone number stands out</li>
                <li><strong>Clickable Links:</strong> All contact methods are interactive</li>
                <li><strong>Business Hours:</strong> Clear 24/7 availability message</li>
                <li><strong>Social Media:</strong> All social links visible (placeholder links)</li>
                <li><strong>Responsive Design:</strong> Works perfectly on mobile and desktop</li>
            </ul>
        </div>
        
        <div class="test-info">
            <h3>🎯 Contact Information Displayed:</h3>
            <ul>
                <li><strong>Primary Address:</strong> 123 Demo Street, Demo City, DC 12345, United States</li>
                <li><strong>Secondary Address:</strong> 456 Sample Avenue, Example Town, ET 67890, United Kingdom</li>
                <li><strong>General Phone:</strong> +1 (555) 123-DEMO</li>
                <li><strong>Emergency Phone:</strong> (************* (highlighted)</li>
                <li><strong>General Email:</strong> <EMAIL></li>
                <li><strong>Emergency Email:</strong> <EMAIL></li>
                <li><strong>Business Hours:</strong> 24/7 Emergency Support Available</li>
            </ul>
        </div>
    </div>
    
    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>
    
    <!-- Load the footer -->
    <script src="js/main.js"></script>
    <script>
        // Simple test to verify footer loads
        setTimeout(() => {
            const footer = document.querySelector('footer');
            if (footer) {
                console.log('✅ Footer loaded successfully');
                
                // Check for contact elements
                const contactItems = footer.querySelectorAll('.contact-item');
                console.log(`📞 Contact items found: ${contactItems.length}`);
                
                // Check for clickable links
                const contactLinks = footer.querySelectorAll('.contact-link');
                console.log(`🔗 Clickable contact links: ${contactLinks.length}`);
                
                // Check for emergency phone
                const emergencyPhone = footer.querySelector('.emergency-phone');
                if (emergencyPhone) {
                    console.log('🚨 Emergency phone number found and highlighted');
                }
                
                // Check for business hours
                const businessHours = footer.querySelector('.business-hours');
                if (businessHours) {
                    console.log('🕒 Business hours section found');
                }
            } else {
                console.log('❌ Footer not found');
            }
        }, 1000);
    </script>
</body>
</html>
