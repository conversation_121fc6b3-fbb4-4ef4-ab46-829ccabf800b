  <!-- <PERSON> Header -->
  <header class="header sticky-header" style="background: rgba(255, 255, 255, 0.95) !important; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important; position: sticky !important; position: -webkit-sticky !important; top: 0 !important; z-index: 9999 !important; backdrop-filter: blur(10px) !important; transition: all 0.3s ease !important; width: 100% !important;">
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 1rem;">
      <div style="display: flex; align-items: center; justify-content: space-between; padding: 1rem 0; transition: padding 0.3s ease;">
        <a href="index.html" style="text-decoration: none; display: flex; align-items: center;">
          <img src="demo-data/logo.png" alt="Forensic Involve" style="height: 50px; width: auto; transition: transform 0.3s ease;">
        </a>

        <!-- Desktop Navigation -->
        <nav id="desktop-nav" style="display: block;">
          <ul style="display: flex; list-style: none; margin: 0; padding: 0; gap: 1rem;">
            <li><a href="index.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">Home</a></li>
            <li><a href="about.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">About</a></li>
            <li><a href="services.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">Services</a></li>
            <li><a href="process.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">Process</a></li>
            <li><a href="success-stories.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">Success Stories</a></li>
            <li><a href="news.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">News</a></li>
            <li><a href="apply.html" class="nav-link" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease;">Apply</a></li>
            <li><a href="contact.html" class="nav-link contact-btn" style="text-decoration: none; color: #374151; font-weight: 600; font-size: 0.95rem; padding: 0.5rem 1rem; border-radius: 0.5rem; transition: all 0.3s ease; border: 2px solid #1e40af;">Contact</a></li>
          </ul>
        </nav>

        <!-- Mobile Menu Button -->
        <button id="mobile-menu-btn" style="display: none; background: none; border: none; font-size: 1.5rem; color: #374151; cursor: pointer;">
          <i class="fas fa-bars"></i>
        </button>

        <!-- Mobile Navigation -->
        <nav id="mobile-nav" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: white; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border-radius: 0 0 1rem 1rem; padding: 1rem;">
          <ul style="list-style: none; margin: 0; padding: 0;">
            <li style="margin-bottom: 0.5rem;"><a href="index.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Home</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="about.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">About</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="services.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Services</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="process.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Process</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="success-stories.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Success Stories</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="news.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">News</a></li>
            <li style="margin-bottom: 0.5rem;"><a href="apply.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Apply</a></li>
            <li><a href="contact.html" style="display: block; text-decoration: none; color: #6b7280; font-weight: 600; padding: 0.75rem; border-radius: 0.5rem;">Contact</a></li>
          </ul>
        </nav>
      </div>
    </div>
  </header>

  <style>
    /* Universal Header Styles - Completely remove all underlines and borders */
    header * {
      text-decoration: none !important;
      border-bottom: none !important;
      box-shadow: none !important;
    }

    /* Ensure sticky positioning works across browsers */
    .header {
      position: -webkit-sticky !important;
      position: sticky !important;
      top: 0 !important;
      z-index: 9999 !important;
      width: 100% !important;
    }

    header a,
    header a:hover,
    header a:focus,
    header a:active,
    header a:visited,
    header .nav-link,
    header .nav-link:hover,
    header .nav-link:focus,
    header .nav-link:active,
    header .nav-link.active {
      text-decoration: none !important;
      border-bottom: none !important;
      border: none !important;
      box-shadow: none !important;
      outline: none !important;
    }

    @media (max-width: 768px) {
      #mobile-menu-btn {
        display: block !important;
      }

      #desktop-nav {
        display: none !important;
      }

      /* Mobile logo size */
      header img {
        height: 42px !important;
      }
    }

    /* Desktop hover and active effects - Background only, absolutely no lines */
    @media (min-width: 769px) {
      .nav-link {
        text-decoration: none !important;
        border-bottom: none !important;
        box-shadow: none !important;
        position: relative !important;
      }

      .nav-link::before,
      .nav-link::after {
        display: none !important;
        content: none !important;
      }

      /* Regular nav links hover */
      .nav-link:hover:not(.contact-btn) {
        background: #f3f4f6 !important;
        color: #1e40af !important;
        text-decoration: none !important;
        border: none !important;
      }

      /* Contact button specific styling */
      .nav-link.contact-btn {
        border: 2px solid #1e40af !important;
        color: #1e40af !important;
        background: transparent !important;
      }

      .nav-link.contact-btn:hover {
        background: #1e40af !important;
        color: white !important;
        text-decoration: none !important;
      }

      /* Active page styling */
      .nav-link.active:not(.contact-btn) {
        background: #1e40af !important;
        color: white !important;
        font-weight: 700 !important;
        text-decoration: none !important;
        border: none !important;
      }

      /* Active contact button */
      .nav-link.contact-btn.active {
        background: #1e40af !important;
        color: white !important;
        border: 2px solid #1e40af !important;
      }

      .nav-link.active::before,
      .nav-link.active::after {
        display: none !important;
        content: none !important;
      }
    }

    /* Enhanced sticky header */
    .header {
      position: sticky !important;
      top: 0 !important;
      z-index: 9999 !important;
      background: rgba(255, 255, 255, 0.95) !important;
      backdrop-filter: blur(10px) !important;
      transition: all 0.3s ease !important;
      border-bottom: 1px solid transparent !important;
      width: 100% !important;
      left: 0 !important;
      right: 0 !important;
    }

    .header.scrolled {
      background: rgba(255, 255, 255, 0.98) !important;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    }

    .header.scrolled .container > div {
      padding: 0.75rem 0 !important;
    }

    .header.scrolled img {
      height: 45px !important;
    }

    /* Mobile menu hover effects - Background only */
    #mobile-nav a {
      text-decoration: none !important;
      border: none !important;
    }

    #mobile-nav a:hover {
      background: #f3f4f6 !important;
      color: #1e40af !important;
      text-decoration: none !important;
      border: none !important;
    }
  </style>

  <script>
    // Mobile menu toggle functionality and active page detection
    document.addEventListener('DOMContentLoaded', function() {
      const mobileMenuBtn = document.getElementById('mobile-menu-btn');
      const mobileNav = document.getElementById('mobile-nav');

      // Set active page styling
      setActivePage();

      if (mobileMenuBtn && mobileNav) {
        mobileMenuBtn.addEventListener('click', function() {
          const isVisible = mobileNav.style.display === 'block';
          mobileNav.style.display = isVisible ? 'none' : 'block';

          // Update button icon
          const icon = mobileMenuBtn.querySelector('i');
          icon.className = isVisible ? 'fas fa-bars' : 'fas fa-times';
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
          if (!mobileMenuBtn.contains(e.target) && !mobileNav.contains(e.target)) {
            mobileNav.style.display = 'none';
            const icon = mobileMenuBtn.querySelector('i');
            icon.className = 'fas fa-bars';
          }
        });

        // Close mobile menu when clicking on a link
        const mobileLinks = mobileNav.querySelectorAll('a');
        mobileLinks.forEach(link => {
          link.addEventListener('click', function() {
            mobileNav.style.display = 'none';
            const icon = mobileMenuBtn.querySelector('i');
            icon.className = 'fas fa-bars';
          });
        });
      }
    });

    function setActivePage() {
      // Get current page name from URL
      const currentPage = window.location.pathname.split('/').pop() || 'index.html';

      // Remove active class from all nav links and reset styling
      const navLinks = document.querySelectorAll('.nav-link');
      navLinks.forEach(link => {
        link.classList.remove('active');
        
        // Reset contact button to default styling
        if (link.classList.contains('contact-btn')) {
          link.style.background = 'transparent';
          link.style.color = '#1e40af';
          link.style.border = '2px solid #1e40af';
        }
      });

      // Add active class to current page link
      const activeLink = document.querySelector(`a[href="${currentPage}"], a[href="./${currentPage}"]`);
      if (activeLink && activeLink.classList.contains('nav-link')) {
        activeLink.classList.add('active');

        // Special handling for active contact button
        if (activeLink.classList.contains('contact-btn')) {
          activeLink.style.background = '#1e40af';
          activeLink.style.color = 'white';
          activeLink.style.border = '2px solid #1e40af';
        }
      }

      // Handle mobile nav active states
      const mobileLinks = document.querySelectorAll('#mobile-nav a');
      mobileLinks.forEach(link => {
        if (link.href.includes(currentPage)) {
          link.style.background = '#f0f8ff';
          link.style.color = '#1e40af';
        } else {
          link.style.background = 'transparent';
          link.style.color = '#6b7280';
        }
      });
    }

    // Enhanced sticky header effect
    function handleStickyHeader() {
      const header = document.querySelector('.header');
      if (header) {
        const scrolled = window.scrollY > 20;
        if (scrolled) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }
        console.log('Scroll position:', window.scrollY, 'Scrolled class:', scrolled);
      }
    }

    // Initialize sticky header effect immediately
    setTimeout(function() {
      handleStickyHeader();
      
      // Add scroll listener for enhanced sticky header with throttling for performance
      let scrollTimer = null;
      window.addEventListener('scroll', function() {
        if (scrollTimer !== null) {
          clearTimeout(scrollTimer);
        }
        scrollTimer = setTimeout(handleStickyHeader, 10);
      });

      // Also handle resize events
      window.addEventListener('resize', handleStickyHeader);
      
      // Force initial check
      console.log('Sticky header initialized');
    }, 100);
  </script>
