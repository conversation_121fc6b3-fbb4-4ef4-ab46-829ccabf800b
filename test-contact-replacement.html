<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Replacement Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .old-contact { background: #fee; }
        .new-contact { background: #efe; }
        button { background: #1e40af; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #1d4ed8; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🧪 Contact Information Replacement Test</h1>
    
    <div class="test-section">
        <h2>API Test</h2>
        <button onclick="testAPI()">Test Contact API</button>
        <div id="api-result" class="log"></div>
    </div>
    
    <div class="test-section old-contact">
        <h2>Old Contact Information (Before Replacement)</h2>
        <p>Phone: <span id="phone1">(*************</span></p>
        <p>Phone: <span id="phone2">+1 (555) 123-DEMO</span></p>
        <p>Email: <span id="email1"><EMAIL></span></p>
        <p>Email: <span id="email2"><EMAIL></span></p>
        <p>Address: <span id="address1">123 Demo Street, Demo City, DC 12345, United States</span></p>
        <p>Address: <span id="address2">456 Sample Avenue, Example Town, ET 67890, United Kingdom</span></p>
        
        <p>Links:</p>
        <a href="tel:8001234567">Call (*************</a><br>
        <a href="mailto:<EMAIL>">Email <EMAIL></a>
    </div>
    
    <div class="test-section">
        <h2>Manual Replacement Test</h2>
        <button onclick="testReplacement()">Replace Contact Info</button>
        <div id="replacement-log" class="log"></div>
    </div>
    
    <div class="test-section new-contact">
        <h2>Expected New Contact Information (After Replacement)</h2>
        <p>This section will show what the contact info should look like after replacement.</p>
        <div id="expected-contact"></div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = 'Testing API...';
            
            try {
                const response = await fetch('/foresensic/api/get-contact-info.php');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <strong>✅ API Success!</strong><br>
                        Emergency Phone: ${data.data.phones.emergency}<br>
                        General Phone: ${data.data.phones.general}<br>
                        General Email: ${data.data.emails.general}<br>
                        Primary Address: ${data.data.addresses.primary}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // Show expected contact info
                    document.getElementById('expected-contact').innerHTML = `
                        <p>Phone: ${data.data.phones.emergency}</p>
                        <p>Phone: ${data.data.phones.general}</p>
                        <p>Email: ${data.data.emails.general}</p>
                        <p>Email: ${data.data.emails.help}</p>
                        <p>Address: ${data.data.addresses.primary}</p>
                        <p>Address: ${data.data.addresses.secondary}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `❌ API Error: ${data.error}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }
        
        async function testReplacement() {
            const logDiv = document.getElementById('replacement-log');
            logDiv.innerHTML = 'Starting replacement test...<br>';
            
            try {
                // Fetch contact data
                const response = await fetch('/foresensic/api/get-contact-info.php');
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error);
                }
                
                const contactData = result.data;
                logDiv.innerHTML += `✅ Contact data loaded<br>`;
                
                // Test text replacement
                replaceTextContent('(*************', contactData.phones.emergency);
                replaceTextContent('+1 (555) 123-DEMO', contactData.phones.general);
                replaceTextContent('<EMAIL>', contactData.emails.general);
                replaceTextContent('<EMAIL>', contactData.emails.help);
                replaceTextContent('123 Demo Street, Demo City, DC 12345, United States', contactData.addresses.primary);
                replaceTextContent('456 Sample Avenue, Example Town, ET 67890, United Kingdom', contactData.addresses.secondary);
                
                logDiv.innerHTML += `✅ Text content replaced<br>`;
                
                // Test link replacement
                document.querySelectorAll('a[href="tel:8001234567"]').forEach(link => {
                    link.setAttribute('href', `tel:${contactData.phones.emergency.replace(/\D/g, '')}`);
                    link.textContent = `Call ${contactData.phones.emergency}`;
                });
                
                document.querySelectorAll('a[href="mailto:<EMAIL>"]').forEach(link => {
                    link.setAttribute('href', `mailto:${contactData.emails.general}`);
                    link.textContent = `Email ${contactData.emails.general}`;
                });
                
                logDiv.innerHTML += `✅ Links updated<br>`;
                logDiv.innerHTML += `🎉 Replacement test completed successfully!<br>`;
                
            } catch (error) {
                logDiv.innerHTML += `❌ Error: ${error.message}<br>`;
            }
        }
        
        function replaceTextContent(oldText, newText) {
            if (!newText || newText === oldText) return;
            
            // Find all text nodes containing the old text
            const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                if (node.textContent.includes(oldText)) {
                    textNodes.push(node);
                }
            }

            // Replace text in found nodes
            textNodes.forEach(textNode => {
                textNode.textContent = textNode.textContent.replace(new RegExp(escapeRegex(oldText), 'g'), newText);
            });
        }
        
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }
        
        // Auto-test API on page load
        document.addEventListener('DOMContentLoaded', function() {
            testAPI();
        });
    </script>
</body>
</html>
