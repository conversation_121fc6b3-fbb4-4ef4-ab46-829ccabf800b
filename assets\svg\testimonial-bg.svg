<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200px" height="400px" viewBox="0 0 1200 400" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Testimonial Background</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0A74DA" offset="0%"></stop>
            <stop stop-color="#003366" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Testimonial-Background" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="#F8F9FA" x="0" y="0" width="1200" height="400"></rect>
        
        <!-- Quote Marks -->
        <text id="Left-Quote" font-family="Georgia, serif" font-size="200" font-weight="bold" fill="url(#linearGradient-1)" opacity="0.05" x="100" y="200">
            "
        </text>
        <text id="Right-Quote" font-family="Georgia, serif" font-size="200" font-weight="bold" fill="url(#linearGradient-1)" opacity="0.05" x="1000" y="300">
            "
        </text>
        
        <!-- Decorative Elements -->
        <circle id="Circle-1" fill="url(#linearGradient-1)" opacity="0.05" cx="200" cy="100" r="50"></circle>
        <circle id="Circle-2" fill="url(#linearGradient-1)" opacity="0.05" cx="1000" cy="150" r="30"></circle>
        <circle id="Circle-3" fill="url(#linearGradient-1)" opacity="0.05" cx="300" cy="300" r="40"></circle>
        <circle id="Circle-4" fill="url(#linearGradient-1)" opacity="0.05" cx="900" cy="250" r="25"></circle>
        
        <!-- Wave Pattern -->
        <path d="M0,350 C100,320 200,380 300,350 C400,320 500,380 600,350 C700,320 800,380 900,350 C1000,320 1100,380 1200,350 L1200,400 L0,400 Z" id="Wave" fill="url(#linearGradient-1)" opacity="0.05"></path>
        
        <!-- Stars -->
        <g id="Stars" opacity="0.1" transform="translate(100, 50)" fill="url(#linearGradient-1)">
            <polygon points="0,0 2,5 7,5 3,8 5,13 0,10 -5,13 -3,8 -7,5 -2,5"></polygon>
        </g>
        <g id="Stars-2" opacity="0.1" transform="translate(400, 80)" fill="url(#linearGradient-1)">
            <polygon points="0,0 3,7 10,7 4,12 7,19 0,15 -7,19 -4,12 -10,7 -3,7"></polygon>
        </g>
        <g id="Stars-3" opacity="0.1" transform="translate(700, 60)" fill="url(#linearGradient-1)">
            <polygon points="0,0 2,5 7,5 3,8 5,13 0,10 -5,13 -3,8 -7,5 -2,5"></polygon>
        </g>
        <g id="Stars-4" opacity="0.1" transform="translate(1000, 90)" fill="url(#linearGradient-1)">
            <polygon points="0,0 3,7 10,7 4,12 7,19 0,15 -7,19 -4,12 -10,7 -3,7"></polygon>
        </g>
        
        <!-- Dots Pattern -->
        <g id="Dots" opacity="0.1" fill="url(#linearGradient-1)">
            <circle cx="150" cy="150" r="3"></circle>
            <circle cx="170" cy="150" r="3"></circle>
            <circle cx="190" cy="150" r="3"></circle>
            <circle cx="150" cy="170" r="3"></circle>
            <circle cx="170" cy="170" r="3"></circle>
            <circle cx="190" cy="170" r="3"></circle>
            <circle cx="150" cy="190" r="3"></circle>
            <circle cx="170" cy="190" r="3"></circle>
            <circle cx="190" cy="190" r="3"></circle>
            
            <circle cx="850" cy="150" r="3"></circle>
            <circle cx="870" cy="150" r="3"></circle>
            <circle cx="890" cy="150" r="3"></circle>
            <circle cx="850" cy="170" r="3"></circle>
            <circle cx="870" cy="170" r="3"></circle>
            <circle cx="890" cy="170" r="3"></circle>
            <circle cx="850" cy="190" r="3"></circle>
            <circle cx="870" cy="190" r="3"></circle>
            <circle cx="890" cy="190" r="3"></circle>
        </g>
    </g>
</svg>