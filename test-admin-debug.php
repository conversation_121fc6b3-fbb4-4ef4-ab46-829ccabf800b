<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 Admin Index.php Debug Test</h2>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .test-box { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #1e40af; }
    .success { border-left-color: #10b981; }
    .error { border-left-color: #ef4444; background: #fef2f2; }
    .info { border-left-color: #3b82f6; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>";

// Test 1: Session check
echo "<div class='test-box'>";
echo "<h3>1. Session Test</h3>";
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_full_name'] = 'Administrator';
$_SESSION['admin_email'] = '<EMAIL>';
echo "<p>✅ Session variables set for testing</p>";
echo "</div>";

// Test 2: Database connection
echo "<div class='test-box'>";
echo "<h3>2. Database Connection Test</h3>";
try {
    require_once 'admin/includes/db_config.php';
    echo "<p>✅ Database config loaded successfully</p>";
    echo "<p>Database connection established</p>";
} catch (Exception $e) {
    echo "<div class='error'><p>❌ Database connection failed: " . $e->getMessage() . "</p></div>";
}
echo "</div>";

// Test 3: Auth functions
echo "<div class='test-box'>";
echo "<h3>3. Auth Functions Test</h3>";
try {
    require_once 'admin/includes/auth.php';
    echo "<p>✅ Auth functions loaded successfully</p>";
    
    if (function_exists('isLoggedIn')) {
        echo "<p>✅ isLoggedIn function exists</p>";
        $logged_in = isLoggedIn();
        echo "<p>Login status: " . ($logged_in ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";
    }
    
    if (function_exists('getCurrentAdmin')) {
        echo "<p>✅ getCurrentAdmin function exists</p>";
        $current_user = getCurrentAdmin();
        if ($current_user) {
            echo "<p>Current user: " . $current_user['username'] . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<div class='error'><p>❌ Auth functions error: " . $e->getMessage() . "</p></div>";
}
echo "</div>";

// Test 4: Database queries
echo "<div class='test-box'>";
echo "<h3>4. Database Queries Test</h3>";
try {
    // Test blog_posts table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM blog_posts");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>✅ Blog posts table accessible: {$count} posts found</p>";
    
    // Test recent posts query
    $stmt = $pdo->query("SELECT id, title, status, created_at FROM blog_posts ORDER BY created_at DESC LIMIT 5");
    $recent_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>✅ Recent posts query successful: " . count($recent_posts) . " posts retrieved</p>";
    
} catch (Exception $e) {
    echo "<div class='error'><p>❌ Database query error: " . $e->getMessage() . "</p></div>";
}
echo "</div>";

// Test 5: Admin index.php inclusion
echo "<div class='test-box'>";
echo "<h3>5. Admin Index.php Test</h3>";
echo "<p>Testing if admin/index.php loads without errors...</p>";

ob_start();
try {
    include 'admin/index.php';
    $output = ob_get_contents();
    echo "<p>✅ Admin index.php loaded successfully!</p>";
    echo "<p><a href='admin/index.php' target='_blank'>🚀 Open Admin Dashboard</a></p>";
} catch (Exception $e) {
    echo "<div class='error'><p>❌ Admin index.php error: " . $e->getMessage() . "</p></div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (ParseError $e) {
    echo "<div class='error'><p>❌ PHP Parse Error in admin/index.php: " . $e->getMessage() . "</p></div>";
} catch (Error $e) {
    echo "<div class='error'><p>❌ PHP Fatal Error in admin/index.php: " . $e->getMessage() . "</p></div>";
}
ob_end_clean();
echo "</div>";

echo "<div class='test-box info'>";
echo "<h3>🔗 Quick Links</h3>";
echo "<p><a href='admin/login.php'>🔐 Login Page</a></p>";
echo "<p><a href='admin/index.php'>📊 Admin Dashboard</a></p>";
echo "<p><a href='admin/posts.php'>📝 Manage Posts</a></p>";
echo "</div>";
?>
