<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Define upload directory and allowed file types
$upload_dir = __DIR__ . '/uploads/';
$allowed_types = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain', 'text/csv'
];
$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'txt', 'csv'];
$max_file_size = 5 * 1024 * 1024; // 5MB

// Create uploads directory if it doesn't exist
if (!file_exists($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        $error = 'Failed to create upload directory.';
    } else {
        // Create .htaccess for security
        file_put_contents($upload_dir . '.htaccess', "Options -Indexes\nAllowOverride None\n");
        // Create index.php to prevent directory listing
        file_put_contents($upload_dir . 'index.php', '<?php header("Location: ../"); exit(); ?>');
    }
}

// Handle file upload
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'upload') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } elseif (!isset($_FILES['media_files']) || empty($_FILES['media_files']['name'][0])) {
        $error = 'No files selected for upload.';
    } else {
        $files = $_FILES['media_files'];
        $description = trim($_POST['description'] ?? '');
        $uploaded_count = 0;
        $errors = [];

        // Sanitize description
        $description = htmlspecialchars($description, ENT_QUOTES, 'UTF-8');
        if (strlen($description) > 500) {
            $description = substr($description, 0, 500);
        }

        for ($i = 0; $i < count($files['name']); $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                $errors[] = "Upload error for file: " . htmlspecialchars($files['name'][$i], ENT_QUOTES, 'UTF-8');
                continue;
            }

            $original_name = $files['name'][$i];
            $file_size = $files['size'][$i];
            $file_tmp = $files['tmp_name'][$i];

            // Validate file size
            if ($file_size > $max_file_size) {
                $errors[] = "File too large: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8') . " (max 5MB)";
                continue;
            }

            // Get and validate file extension
            $file_ext = strtolower(pathinfo($original_name, PATHINFO_EXTENSION));
            if (!in_array($file_ext, $allowed_extensions)) {
                $errors[] = "Invalid file type: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8');
                continue;
            }

            // Validate MIME type
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime_type = finfo_file($finfo, $file_tmp);
            finfo_close($finfo);

            if (!in_array($mime_type, $allowed_types)) {
                $errors[] = "Invalid file format: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8');
                continue;
            }

            // Generate secure filename
            $safe_name = preg_replace('/[^a-zA-Z0-9_.-]/', '_', pathinfo($original_name, PATHINFO_FILENAME));
            $safe_name = substr($safe_name, 0, 100); // Limit length
            $unique_name = $safe_name . '_' . time() . '_' . uniqid() . '.' . $file_ext;
            $file_path = $upload_dir . $unique_name;

            // Move uploaded file
            if (move_uploaded_file($file_tmp, $file_path)) {
                try {
                    // Insert file record into database
                    $stmt = $pdo->prepare("INSERT INTO media_files (original_name, file_name, file_path, file_size, mime_type, description, uploaded_by, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
                    if ($stmt->execute([
                        htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8'),
                        htmlspecialchars($unique_name, ENT_QUOTES, 'UTF-8'),
                        htmlspecialchars('admin/uploads/' . $unique_name, ENT_QUOTES, 'UTF-8'),
                        (int)$file_size,
                        htmlspecialchars($mime_type, ENT_QUOTES, 'UTF-8'),
                        $description,
                        (int)getCurrentAdmin()['id']
                    ])) {
                        $uploaded_count++;
                    } else {
                        unlink($file_path); // Remove file if DB insert fails
                        $errors[] = "Database error for: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8');
                    }
                } catch (Exception $e) {
                    unlink($file_path); // Remove file if DB insert fails
                    $errors[] = "Database error for: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8');
                }
            } else {
                $errors[] = "Failed to save: " . htmlspecialchars($original_name, ENT_QUOTES, 'UTF-8');
            }
        }

        if ($uploaded_count > 0) {
            $message = "Successfully uploaded {$uploaded_count} file(s).";
        }
        if (!empty($errors)) {
            $error = implode('<br>', $errors);
        }
    }
}

// Handle file deletion
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $file_id = (int)($_POST['file_id'] ?? 0);
        if ($file_id > 0) {
            try {
                // Get file info
                $stmt = $pdo->prepare("SELECT file_name, file_path FROM media_files WHERE id = ?");
                $stmt->execute([$file_id]);
                $file = $stmt->fetch();

                if ($file) {
                    // Delete file from filesystem
                    $full_path = $upload_dir . $file['file_name'];
                    if (file_exists($full_path)) {
                        unlink($full_path);
                    }

                    // Delete from database
                    $stmt = $pdo->prepare("DELETE FROM media_files WHERE id = ?");
                    if ($stmt->execute([$file_id])) {
                        $message = 'File deleted successfully.';
                    } else {
                        $error = 'Failed to delete file record.';
                    }
                } else {
                    $error = 'File not found.';
                }
            } catch (Exception $e) {
                $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
            }
        }
    }
}

// Get files for display
$search = trim($_GET['search'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

try {
    // Build query with search
    $where_clause = "";
    $params = [];

    if (!empty($search)) {
        $search_term = htmlspecialchars($search, ENT_QUOTES, 'UTF-8');
        $where_clause = "WHERE original_name LIKE ? OR description LIKE ?";
        $params = ["%{$search_term}%", "%{$search_term}%"];
    }

    // Get total count
    $count_sql = "SELECT COUNT(*) FROM media_files {$where_clause}";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_files = $stmt->fetchColumn();

    // Get files
    $sql = "SELECT * FROM media_files {$where_clause} ORDER BY created_at DESC LIMIT {$per_page} OFFSET {$offset}";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $files = $stmt->fetchAll();

    $total_pages = ceil($total_files / $per_page);
} catch (Exception $e) {
    $files = [];
    $total_files = 0;
    $total_pages = 0;
    $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
}

$current_user = getCurrentAdmin();
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Library - Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .media-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .media-card { background: white; border-radius: 8px; padding: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #e2e8f0; }
        .media-preview { width: 100%; height: 150px; background: #f8fafc; border-radius: 6px; display: flex; align-items: center; justify-content: center; margin-bottom: 10px; overflow: hidden; }
        .media-preview img { max-width: 100%; max-height: 100%; object-fit: cover; border-radius: 6px; }
        .media-icon { font-size: 48px; color: #64748b; }
        .file-info { font-size: 0.875rem; color: #64748b; margin-bottom: 5px; }
        .file-name { font-weight: 600; color: #1e293b; margin-bottom: 5px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .upload-form { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .file-drop-zone { border: 2px dashed #cbd5e1; border-radius: 8px; padding: 40px; text-align: center; margin-bottom: 20px; background: #f8fafc; transition: all 0.3s ease; }
        .file-drop-zone.dragover { border-color: #1e40af; background: #eff6ff; }
        .file-drop-zone input[type="file"] { display: none; }
        .search-controls { display: flex; gap: 15px; align-items: center; margin-bottom: 20px; flex-wrap: wrap; }
        .search-box { flex: 1; min-width: 200px; }
        .pagination { display: flex; justify-content: center; align-items: center; gap: 10px; margin-top: 30px; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }
        .empty-state { text-align: center; padding: 60px 20px; background: white; border-radius: 8px; }
        .media-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .stat-item { background: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 1.5rem; font-weight: 700; color: #1e40af; }
        .stat-label { font-size: 0.875rem; color: #64748b; }

        /* Media Page Specific Styles */
        .upload-section {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .upload-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        }

        .upload-header h2 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .upload-header p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .upload-form {
            padding: 1.5rem;
        }

        .file-drop-zone {
            border: 2px dashed var(--gray-300);
            border-radius: var(--border-radius);
            padding: 3rem 2rem;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .file-drop-zone:hover,
        .file-drop-zone.dragover {
            border-color: var(--primary);
            background: #f0f7ff;
            transform: translateY(-2px);
        }

        .drop-content .drop-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .drop-content h3 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .drop-content p {
            margin: 0.25rem 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.875rem;
            resize: vertical;
            min-height: 80px;
        }

        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-hint {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }

        .file-preview {
            background: var(--gray-50);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .file-preview h4 {
            margin: 0 0 1rem 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .selected-files-grid {
            display: grid;
            gap: 0.5rem;
        }

        .selected-file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: var(--white);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
        }

        .selected-file-info {
            font-size: 0.875rem;
            color: var(--gray-700);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-large {
            padding: 0.875rem 1.5rem;
            font-size: 1rem;
            font-weight: 600;
        }

        .btn-icon {
            margin-right: 0.5rem;
        }

        .media-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            background: var(--white);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .search-controls {
            background: var(--white);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 1.5rem;
        }

        .search-box {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-box input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
        }

        .search-box input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        @media (max-width: 768px) {
            .upload-form {
                padding: 1rem;
            }

            .file-drop-zone {
                padding: 2rem 1rem;
            }

            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars($current_user['username'], ENT_QUOTES, 'UTF-8'); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item">
                <span class="icon">📁</span>
                Categories
            </a>
            <a href="media.php" class="menu-item active">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1>Media Library</h1>
            <p>Upload and manage images, documents, and other media files</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
            </div>
        <?php endif; ?>

        <!-- Media Statistics -->
        <div class="media-stats">
            <div class="stat-item">
                <div class="stat-number"><?php echo (int)$total_files; ?></div>
                <div class="stat-label">Total Files</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo number_format(array_sum(array_column($files, 'file_size')) / 1024 / 1024, 1); ?>MB</div>
                <div class="stat-label">Storage Used</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo date('M j'); ?></div>
                <div class="stat-label">Today</div>
            </div>
        </div>        <!-- Upload Form -->
        <div class="upload-section">
            <div class="upload-header">
                <h2>Upload New Files</h2>
                <p>Drag and drop files or click to browse</p>
            </div>

            <form method="POST" enctype="multipart/form-data" id="uploadForm" class="upload-form">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" name="action" value="upload">

                <div class="file-drop-zone" id="dropZone">
                    <div class="drop-content">
                        <div class="drop-icon">📁</div>
                        <h3>Drop files here or click to browse</h3>
                        <p>Supported formats: JPG, PNG, GIF, WebP, PDF, DOC, DOCX, TXT, CSV</p>
                        <p><strong>Maximum size: 5MB per file</strong></p>
                        <input type="file" id="fileInput" name="media_files[]" multiple accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.doc,.docx,.txt,.csv" style="display: none;">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            Choose Files
                        </button>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="description">Description (Optional)</label>
                        <textarea id="description" name="description" rows="3" maxlength="500"
                                  placeholder="Add a brief description for these files..."></textarea>
                        <div class="form-hint">Maximum 500 characters</div>
                    </div>
                </div>

                <div id="fileList" class="file-preview" style="display: none;">
                    <h4>Selected Files</h4>
                    <div id="selectedFiles" class="selected-files-grid"></div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large" id="uploadBtn" disabled>
                        <span class="btn-icon">⬆️</span>
                        Upload Files
                    </button>
                    <button type="button" class="btn btn-outline" onclick="clearSelection()">
                        Clear Selection
                    </button>
                </div>
            </form>
        </div>

        <!-- Search and Controls -->
        <div class="search-controls">
            <div class="search-box">
                <form method="GET" style="display: flex; gap: 10px;">
                    <input type="text" name="search" placeholder="Search files..." value="<?php echo htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>" style="flex: 1;">
                    <button type="submit" class="btn btn-outline">Search</button>
                    <?php if (!empty($search)): ?>
                        <a href="media.php" class="btn btn-outline">Clear</a>
                    <?php endif; ?>
                </form>
            </div>
        </div>

        <!-- Media Grid -->
        <?php if (empty($files)): ?>
            <div class="empty-state">
                <h3>No files found</h3>
                <p><?php echo !empty($search) ? 'Try a different search term.' : 'Upload your first file using the form above.'; ?></p>
            </div>
        <?php else: ?>
            <div class="media-grid">
                <?php foreach ($files as $file): ?>
                    <div class="media-card">
                        <div class="media-preview">
                            <?php if (strpos($file['mime_type'], 'image/') === 0): ?>
                                <?php
                                // Fix path - remove 'admin/' prefix if present since we're already in admin directory
                                $display_path = $file['file_path'];
                                if (strpos($display_path, 'admin/') === 0) {
                                    $display_path = substr($display_path, 6); // Remove 'admin/' prefix
                                }
                                ?>
                                <img src="<?php echo htmlspecialchars($display_path, ENT_QUOTES, 'UTF-8'); ?>" alt="<?php echo htmlspecialchars($file['original_name'], ENT_QUOTES, 'UTF-8'); ?>" loading="lazy">
                            <?php else: ?>
                                <span class="media-icon">
                                    <?php
                                    $ext = strtolower(pathinfo($file['original_name'], PATHINFO_EXTENSION));
                                    echo match($ext) {
                                        'pdf' => '📄',
                                        'doc', 'docx' => '📝',
                                        'txt' => '📄',
                                        'csv' => '📊',
                                        default => '📁'
                                    };
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="file-name" title="<?php echo htmlspecialchars($file['original_name'], ENT_QUOTES, 'UTF-8'); ?>">
                            <?php echo htmlspecialchars($file['original_name'], ENT_QUOTES, 'UTF-8'); ?>
                        </div>

                        <div class="file-info">
                            Size: <?php echo number_format($file['file_size'] / 1024, 1); ?> KB
                        </div>

                        <div class="file-info">
                            <?php echo date('M j, Y', strtotime($file['created_at'])); ?>
                        </div>

                        <?php if (!empty($file['description'])): ?>
                            <div class="file-info" style="margin-top: 8px; font-style: italic;">
                                <?php echo htmlspecialchars($file['description'], ENT_QUOTES, 'UTF-8'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="action-buttons" style="margin-top: 10px; display: flex; gap: 5px;">
                            <?php
                            // Fix path for view link - remove 'admin/' prefix if present since we're already in admin directory
                            $view_path = $file['file_path'];
                            if (strpos($view_path, 'admin/') === 0) {
                                $view_path = substr($view_path, 6); // Remove 'admin/' prefix
                            }
                            ?>
                            <button type="button" class="btn btn-outline" style="font-size: 0.8rem;" onclick="openImageModal('<?php echo htmlspecialchars($view_path, ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($file['original_name'], ENT_QUOTES, 'UTF-8'); ?>')">View</button>
                            <button type="button" class="btn btn-danger" style="font-size: 0.8rem;" onclick="confirmDelete(<?php echo (int)$file['id']; ?>, '<?php echo htmlspecialchars($file['original_name'], ENT_QUOTES, 'UTF-8'); ?>')">Delete</button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline">Previous</a>
                    <?php endif; ?>

                    <span>Page <?php echo (int)$page; ?> of <?php echo (int)$total_pages; ?></span>

                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" class="btn btn-outline">Next</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 400px;">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete "<span id="deleteFileName"></span>"?</p>
            <p><strong>This action cannot be undone.</strong></p>

            <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: flex-end;">
                <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="file_id" id="deleteFileId">
                    <button type="submit" class="btn btn-danger">Delete File</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // File upload handling
        const fileInput = document.getElementById('fileInput');
        const dropZone = document.getElementById('dropZone');
        const fileList = document.getElementById('fileList');
        const selectedFiles = document.getElementById('selectedFiles');
        const uploadBtn = document.getElementById('uploadBtn');

        // Drag and drop functionality
        dropZone.addEventListener('click', (e) => {
            if (e.target === dropZone || e.target.closest('.drop-content')) {
                fileInput.click();
            }
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            fileInput.files = e.dataTransfer.files;
            updateFileList();
        });

        fileInput.addEventListener('change', updateFileList);

        function updateFileList() {
            const files = fileInput.files;
            if (files.length > 0) {
                fileList.style.display = 'block';
                selectedFiles.innerHTML = '';
                uploadBtn.disabled = false;

                Array.from(files).forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.style.cssText = 'display: flex; justify-content: space-between; align-items: center; padding: 8px; background: #f8fafc; border-radius: 4px; margin-bottom: 5px;';
                    fileItem.innerHTML = `
                        <span style="font-size: 0.875rem;">${file.name} (${(file.size / 1024).toFixed(1)} KB)</span>
                        <button type="button" onclick="removeFile(${index})" style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 2px 8px; font-size: 0.75rem;">Remove</button>
                    `;
                    selectedFiles.appendChild(fileItem);
                });
            } else {
                fileList.style.display = 'none';
                uploadBtn.disabled = true;
            }
        }

        function removeFile(index) {
            const dt = new DataTransfer();
            const files = fileInput.files;

            for (let i = 0; i < files.length; i++) {
                if (i !== index) {
                    dt.items.add(files[i]);
                }
            }

            fileInput.files = dt.files;
            updateFileList();
        }        function clearSelection() {
            fileInput.value = '';
            updateFileList();
        }

        // Delete confirmation
        function confirmDelete(fileId, fileName) {
            document.getElementById('deleteFileId').value = fileId;
            document.getElementById('deleteFileName').textContent = fileName;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Close modal on outside click
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // Image preview modal
        function openImageModal(imagePath, imageName) {
            // Create modal
            const modal = document.createElement('div');
            modal.id = 'imageModal';
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 1000; display: flex;
                align-items: center; justify-content: center; cursor: pointer;
            `;

            modal.innerHTML = `
                <div style="position: relative; max-width: 90%; max-height: 90%; background: white; border-radius: 8px; overflow: hidden;">
                    <div style="padding: 15px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center; background: #f8fafc;">
                        <h3 style="margin: 0; color: #1e293b;">${imageName}</h3>
                        <button onclick="closeImageModal()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #64748b;">&times;</button>
                    </div>
                    <div style="padding: 20px; text-align: center;">
                        <img src="${imagePath}" alt="${imageName}" style="max-width: 100%; max-height: 70vh; object-fit: contain; border-radius: 4px;">
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Close on outside click
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeImageModal();
                }
            });
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.remove();
            }
        }
    </script>
</body>
</html>
