// Dynamic Blog Content Loader for Forensic Recovery Services

document.addEventListener('DOMContentLoaded', function() {
    loadDynamicContent();
});

function loadDynamicContent() {
    // Load blog posts on homepage
    if (window.location.pathname.includes('index.html') || window.location.pathname === '/' || window.location.pathname.endsWith('/')) {
        loadHomepageBlogPosts();
    }

    // Load blog posts on news page
    if (window.location.pathname.includes('news.html')) {
        loadNewsPagePosts();
    }
}

function loadHomepageBlogPosts() {
    const blogContainer = document.querySelector('#blog-grid');
    if (!blogContainer) return;

    // Show loading state
    blogContainer.innerHTML = `
        <div class="loading-state" style="grid-column: 1 / -1; text-align: center; padding: 2rem;">
            <div class="loading-spinner" style="border: 4px solid #f3f4f6; border-top: 4px solid #1e40af; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
            <p style="color: #6b7280;">Loading latest posts...</p>
        </div>
    `;

    fetch('api/get-posts.php?limit=3')
        .then(response => response.json())
        .then(data => {
            console.log('API Response:', data); // Debug API response
            if (data.success && data.posts.length > 0) {
                console.log('Posts with featured images:', data.posts.map(p => ({
                    title: p.title,
                    featured_image: p.featured_image
                })));
                displayHomepagePosts(data.posts);
            } else {
                console.log('No posts found or API error, showing fallback');
                displayFallbackPosts();
            }
        })
        .catch(error => {
            console.error('Error loading blog posts:', error);
            displayFallbackPosts();
        });
}

function displayHomepagePosts(posts) {
    const blogContainer = document.querySelector('#blog-grid');

    const postsHtml = posts.map(post => {        // Handle featured image path based on actual file structure
        let featuredImagePath = '';
        if (post.featured_image && post.featured_image.trim() !== '') {
            let originalPath = post.featured_image.trim();
            console.log('Original featured_image:', originalPath);
            
            // Extract just the filename from any path format
            let filename = originalPath.split('/').pop();
            console.log('Extracted filename:', filename);
            
            // Since uploads are actually in admin/uploads/, always use that path
            featuredImagePath = 'admin/uploads/' + filename;
            console.log('Final featured_image path:', featuredImagePath);
        }

        return `
            <div class="card">
                ${featuredImagePath ? `
                    <div style="height: 200px; background-image: url('${escapeHtml(featuredImagePath)}'); background-size: cover; background-position: center; border-radius: 0.5rem 0.5rem 0 0; margin: -1.5rem -1.5rem 1.5rem -1.5rem;" data-image-src="${escapeHtml(featuredImagePath)}"></div>
                ` : `
                    <div style="height: 200px; background: linear-gradient(135deg, #f3f4f6, #e5e7eb); border-radius: 0.5rem 0.5rem 0 0; margin: -1.5rem -1.5rem 1.5rem -1.5rem; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                        <i class="fas fa-image" style="font-size: 2rem;"></i>
                    </div>
                `}
                <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                    ${post.formatted_date} • ${post.author}
                </div>
                <h3 class="card-title">${escapeHtml(post.title)}</h3>
                <p class="card-description">${escapeHtml(post.excerpt)}</p>
                <a href="single-news.html?id=${post.id}" class="btn btn-primary">Read More</a>
            </div>
        `;
    }).join('');

    blogContainer.innerHTML = postsHtml;
      // Check if images are loading properly and provide fallbacks
    setTimeout(() => {
        const imageElements = blogContainer.querySelectorAll('[data-image-src]');
        imageElements.forEach(el => {
            const src = el.getAttribute('data-image-src');
            const img = new Image();
            img.onload = () => {
                console.log('✅ Image loaded successfully:', src);
            };
            img.onerror = () => {
                console.log('❌ Image failed to load:', src);
                
                // Try fallback to demo-data
                const filename = src.split('/').pop();
                const fallbackPath = 'demo-data/' + filename;
                console.log('Trying fallback path:', fallbackPath);
                
                const fallbackImg = new Image();
                fallbackImg.onload = () => {
                    console.log('✅ Fallback image loaded:', fallbackPath);
                    el.style.backgroundImage = `url('${fallbackPath}')`;
                };
                fallbackImg.onerror = () => {
                    console.log('❌ Fallback image also failed:', fallbackPath);
                    // Use a generic placeholder
                    el.style.background = 'linear-gradient(135deg, #f3f4f6, #e5e7eb)';
                    el.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #9ca3af;"><i class="fas fa-image" style="font-size: 2rem;"></i></div>';
                };
                fallbackImg.src = fallbackPath;
            };
            img.src = src;
        });
    }, 100);
}

function displayFallbackPosts() {
    // If API fails, show the original static content
    const blogContainer = document.querySelector('#blog-grid');

    blogContainer.innerHTML = `
        <div class="card">
            <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                December 15, 2023 • Expert Team
            </div>
            <h3 class="card-title">Cryptocurrency Fraud Reaches Record Highs in 2023</h3>
            <p class="card-description">New data reveals that cryptocurrency-related fraud has increased by 65% this year, with investment scams being the most common type. Learn how to protect yourself and what to do if you've been victimized.</p>
            <a href="single-news.html?id=1" class="btn btn-primary">Read More</a>
        </div>

        <div class="card">
            <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                December 10, 2023 • Sarah Mitchell
            </div>
            <h3 class="card-title">Romance Scams: How to Spot Red Flags Early</h3>
            <p class="card-description">Romance scams cost victims over $300 million annually. Our comprehensive guide helps you identify warning signs and protect yourself from emotional manipulation and financial loss.</p>
            <a href="single-news.html?id=2" class="btn btn-primary">Read More</a>
        </div>

        <div class="card">
            <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                December 5, 2023 • Michael Chen
            </div>
            <h3 class="card-title">5 Essential Steps After Discovering You've Been Scammed</h3>
            <p class="card-description">Time is critical when you discover you've been scammed. Follow these immediate steps to maximize your chances of recovery and minimize further damage to your finances.</p>
            <a href="single-news.html?id=3" class="btn btn-primary">Read More</a>
        </div>
    `;
}

function loadNewsPagePosts() {
    const newsContainer = document.querySelector('.news-grid');
    if (!newsContainer) return;

    // Show loading state
    newsContainer.innerHTML = `
        <div class="loading-state" style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
            <div class="loading-spinner" style="border: 4px solid #f3f4f6; border-top: 4px solid #1e40af; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; margin: 0 auto 1.5rem;"></div>
            <h3 style="color: #374151; margin-bottom: 0.5rem;">Loading Articles</h3>
            <p style="color: #6b7280;">Please wait while we fetch the latest content...</p>
        </div>
    `;

    const page = getUrlParameter('page') || 1;
    const category = getUrlParameter('category') || '';

    let apiUrl = `api/get-posts.php?limit=12&page=${page}`;
    if (category) {
        apiUrl += `&category=${category}`;
    }

    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNewsPagePosts(data.posts, data.pagination);
                updatePagination(data.pagination);
            } else {
                displayNoPostsMessage();
            }
        })
        .catch(error => {
            console.error('Error loading news posts:', error);
            displayErrorMessage();
        });
}

function displayNewsPagePosts(posts, pagination) {
    const newsContainer = document.querySelector('.news-grid');

    if (posts.length === 0) {
        displayNoPostsMessage();
        return;
    }    const postsHtml = posts.map(post => {
        // Handle featured image path based on actual file structure (same as homepage)
        let featuredImagePath = '';
        if (post.featured_image && post.featured_image.trim() !== '') {
            let originalPath = post.featured_image.trim();
            console.log('Original featured_image:', originalPath);
            
            // Extract just the filename from any path format
            let filename = originalPath.split('/').pop();
            console.log('Extracted filename:', filename);
            
            // Since uploads are actually in admin/uploads/, always use that path
            featuredImagePath = 'admin/uploads/' + filename;
            console.log('Final featured_image path:', featuredImagePath);
        }

        return `
            <article class="news-card" id="post-${post.id}">
                ${featuredImagePath ? `
                    <div class="news-image">
                        <img src="${escapeHtml(featuredImagePath)}" alt="${escapeHtml(post.title)}" loading="lazy">
                    </div>
                ` : ''}
                <div class="news-content">
                    <div class="news-meta">
                        <span class="news-date">${post.formatted_date}</span>
                        <span class="news-category">${escapeHtml(post.category)}</span>
                    </div>
                    <h3 class="news-title">${escapeHtml(post.title)}</h3>
                    <p class="news-excerpt">${escapeHtml(post.excerpt)}</p>
                    <div class="news-footer">
                        <span class="news-author">By ${escapeHtml(post.author)}</span>
                        <a href="single-news.html?id=${post.id}" class="btn btn-outline">
                            Read More
                        </a>
                    </div>
                </div>
            </article>
        `;
    }).join('');

    newsContainer.innerHTML = postsHtml;
}

function displayNoPostsMessage() {
    const newsContainer = document.querySelector('.news-grid');
    newsContainer.innerHTML = `
        <div class="empty-state" style="grid-column: 1 / -1; text-align: center; padding: 4rem 2rem;">
            <div style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;">📰</div>
            <h3 style="color: #374151; margin-bottom: 1rem;">No Articles Found</h3>
            <p style="color: #6b7280; margin-bottom: 2rem;">We haven't published any articles yet, but we're working on bringing you valuable content soon.</p>
            <a href="index.html" class="btn btn-primary">Return to Homepage</a>
        </div>
    `;
}

function displayErrorMessage() {
    const newsContainer = document.querySelector('.news-grid');
    newsContainer.innerHTML = `
        <div class="error-state" style="grid-column: 1 / -1; text-align: center; padding: 4rem 2rem;">
            <div style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.3;">⚠️</div>
            <h3 style="color: #ef4444; margin-bottom: 1rem;">Unable to Load Content</h3>
            <p style="color: #6b7280; margin-bottom: 2rem;">We're experiencing technical difficulties. Please try refreshing the page.</p>
            <button class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
        </div>
    `;
}



function updatePagination(pagination) {
    const paginationContainer = document.querySelector('.pagination-container');
    if (!paginationContainer || pagination.total_pages <= 1) {
        if (paginationContainer) paginationContainer.style.display = 'none';
        return;
    }

    let paginationHtml = '<div class="pagination">';

    // Previous button
    if (pagination.current_page > 1) {
        paginationHtml += `
            <a href="?page=${pagination.current_page - 1}" class="pagination-btn pagination-prev">
                ← Previous
            </a>
        `;
    }

    // Page numbers
    const maxPages = 5;
    let startPage = Math.max(1, pagination.current_page - 2);
    let endPage = Math.min(pagination.total_pages, startPage + maxPages - 1);

    if (endPage - startPage < maxPages - 1) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === pagination.current_page;
        paginationHtml += `
            <a href="?page=${i}" class="pagination-btn ${isActive ? 'active' : ''}">
                ${i}
            </a>
        `;
    }

    // Next button
    if (pagination.current_page < pagination.total_pages) {
        paginationHtml += `
            <a href="?page=${pagination.current_page + 1}" class="pagination-btn pagination-next">
                Next →
            </a>
        `;
    }

    paginationHtml += '</div>';

    paginationContainer.innerHTML = paginationHtml;
    paginationContainer.style.display = 'block';
}

function formatPostContent(content) {
    // Basic Markdown-like formatting
    return content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^(.)/gm, '<p>$1')
        .replace(/(.)/gm, '$1</p>')
        .replace(/<\/p><p>/g, '</p>\n<p>')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// Add loading animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .news-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .news-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .news-image {
        overflow: hidden;
        border-radius: 12px 12px 0 0;
    }

    .news-image img {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .news-card:hover .news-image img {
        transform: scale(1.05);
    }

    .news-content {
        padding: 1.5rem;
    }

    .news-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .news-category {
        background: #eff6ff;
        color: #1e40af;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-weight: 500;
    }

    .news-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .news-excerpt {
        color: #4b5563;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .news-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .news-author {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
    }

    .news-full-content {
        border-top: 1px solid #e5e7eb;
        margin-top: 1rem;
        padding-top: 1.5rem;
    }

    .full-content-wrapper {
        line-height: 1.7;
        color: #374151;
        padding: 0 1.5rem 1.5rem;
    }

    .full-content-wrapper h2,
    .full-content-wrapper h3 {
        color: #1e40af;
        margin: 1.5rem 0 1rem 0;
    }

    .full-content-wrapper p {
        margin-bottom: 1rem;
    }

    .full-content-wrapper a {
        color: #1e40af;
        text-decoration: underline;
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
        margin: 2rem 0;
    }

    .pagination-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #d1d5db;
        background: white;
        color: #374151;
        text-decoration: none;
        border-radius: 0.375rem;
        transition: all 0.2s;
    }

    .pagination-btn:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
    }

    .pagination-btn.active {
        background: #1e40af;
        color: white;
        border-color: #1e40af;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .news-grid {
            grid-template-columns: 1fr !important;
            gap: 1.5rem !important;
        }

        .news-card {
            margin: 0 1rem;
        }

        .news-image img {
            height: 180px;
        }

        .news-content {
            padding: 1rem;
        }

        .news-title {
            font-size: 1.1rem !important;
        }

        .news-footer {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start !important;
        }

        .pagination {
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .pagination-btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    }
`;
document.head.appendChild(style);
