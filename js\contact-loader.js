/**
 * Dynamic Contact Information Loader
 * Fetches contact info from API and replaces hardcoded values across the website
 */

class ContactLoader {
    constructor() {
        this.contactData = null;
        this.apiUrl = this.getApiUrl();
        this.init();
    }

    getApiUrl() {
        // Always use relative path without domain
        // This will work from any page location
        const apiPath = 'api/get-contact-info.php';

        console.log('Current page URL:', window.location.href);
        console.log('Current pathname:', window.location.pathname);
        console.log('API path resolved to:', apiPath);
        console.log('Full API URL will be:', new URL(apiPath, window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1)).href);

        return apiPath;
    }

    async init() {
        try {
            await this.loadContactData();
            this.replaceContactInfo();
        } catch (error) {
            console.warn('Contact loader failed:', error);
            console.warn('Using fallback contact information');
            this.useFallbackContactData();
            this.replaceContactInfo();
        }
    }

    useFallbackContactData() {
        // Provide fallback contact data if API fails
        this.contactData = {
            phones: {
                general: '+1 (555) 123-DEMO',
                emergency: '(*************'
            },
            emails: {
                general: '<EMAIL>',
                help: '<EMAIL>',
                emergency: '<EMAIL>'
            },
            addresses: {
                primary: '123 Demo Street, Demo City, DC 12345, United States',
                secondary: '456 Sample Avenue, Example Town, ET 67890, United Kingdom'
            },
            social: {
                facebook: '#',
                twitter: '#',
                linkedin: '#',
                instagram: '#'
            },
            business: {
                hours: '24/7 Emergency Support Available',
                emergency_note: 'Available 24/7'
            }
        };
        console.log('Fallback contact data loaded');
    }

    async loadContactData() {
        try {
            console.log('Attempting to load contact data from:', this.apiUrl);

            const response = await fetch(this.apiUrl);
            console.log('API Response status:', response.status, response.statusText);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            console.log('Raw API response:', responseText.substring(0, 200) + '...');

            let result;
            try {
                result = JSON.parse(responseText);
            } catch (parseError) {
                console.error('JSON Parse Error:', parseError);
                console.error('Response was not valid JSON:', responseText.substring(0, 500));
                throw new Error('API returned invalid JSON response');
            }

            if (result.success) {
                this.contactData = result.data;
                console.log('Contact data loaded successfully:', this.contactData);
            } else {
                throw new Error(result.error || 'Failed to load contact data');
            }
        } catch (error) {
            console.error('Error loading contact data:', error);
            console.error('API URL was:', this.apiUrl);
            console.error('Current page URL:', window.location.href);
            throw error;
        }
    }

    replaceContactInfo() {
        if (!this.contactData) return;

        // Replace phone numbers
        this.replacePhoneNumbers();

        // Replace email addresses
        this.replaceEmailAddresses();

        // Replace physical addresses
        this.replaceAddresses();

        // Replace business info
        this.replaceBusinessInfo();

        // Replace social media links
        this.replaceSocialLinks();

        // Replace footer-specific contact information
        this.replaceFooterContactInfo();

        console.log('Contact information replaced successfully');
    }

    replacePhoneNumbers() {
        const { phones } = this.contactData;

        // Replace emergency phone number (*************
        this.replaceTextContent('(*************', phones.emergency);
        this.replaceHrefAttribute('tel:8001234567', `tel:${phones.emergency.replace(/\D/g, '')}`);

        // Replace general phone number +1 (555) 123-DEMO
        this.replaceTextContent('+1 (555) 123-DEMO', phones.general);
        this.replaceTextContent('(555) 123-DEMO', phones.general);

        // Update phone links
        document.querySelectorAll('a[href*="tel:"]').forEach(link => {
            const href = link.getAttribute('href');
            if (href.includes('8001234567')) {
                link.setAttribute('href', `tel:${phones.emergency.replace(/\D/g, '')}`);
            }
        });
    }

    replaceEmailAddresses() {
        const { emails } = this.contactData;

        // Replace email addresses
        this.replaceTextContent('<EMAIL>', emails.general);
        this.replaceTextContent('<EMAIL>', emails.help);
        this.replaceTextContent('<EMAIL>', emails.emergency);
        this.replaceTextContent('<EMAIL>', emails.general);

        // Update email links
        this.replaceHrefAttribute('mailto:<EMAIL>', `mailto:${emails.general}`);
        this.replaceHrefAttribute('mailto:<EMAIL>', `mailto:${emails.help}`);
        this.replaceHrefAttribute('mailto:<EMAIL>', `mailto:${emails.emergency}`);
        this.replaceHrefAttribute('mailto:<EMAIL>', `mailto:${emails.general}`);
    }

    replaceAddresses() {
        const { addresses } = this.contactData;

        // Replace primary address
        this.replaceTextContent('123 Demo Street, Demo City, DC 12345, United States', addresses.primary);

        // Replace secondary address
        this.replaceTextContent('456 Sample Avenue, Example Town, ET 67890, United Kingdom', addresses.secondary);

        // Handle multi-line addresses in footer
        document.querySelectorAll('.contact-item span, .contact-info span').forEach(span => {
            if (span.textContent.includes('123 Demo Street')) {
                span.textContent = addresses.primary;
            }
            if (span.textContent.includes('456 Sample Avenue')) {
                span.textContent = addresses.secondary;
            }
        });
    }

    replaceBusinessInfo() {
        const { business } = this.contactData;

        // Replace business hours and availability notes
        this.replaceTextContent('Available 24/7', business.emergency_note);
        this.replaceTextContent('24/7 Emergency Support Available', business.hours);
    }

    replaceSocialLinks() {
        const { social } = this.contactData;

        // Update footer social media links specifically
        const socialLinks = {
            'social-facebook': social.facebook,
            'social-twitter': social.twitter,
            'social-linkedin': social.linkedin,
            'social-instagram': social.instagram
        };

        Object.keys(socialLinks).forEach(id => {
            const link = document.getElementById(id);
            if (link && socialLinks[id] && socialLinks[id] !== '#') {
                link.setAttribute('href', socialLinks[id]);
                link.style.display = 'inline-block';
            } else if (link) {
                link.style.display = 'none';
            }
        });

        // Also update general social links (legacy support)
        Object.keys(social).forEach(platform => {
            if (social[platform] && social[platform] !== '#') {
                const selector = `a[href="#"][aria-label*="${platform}"], .social-links a[aria-label*="${platform}"]`;
                document.querySelectorAll(selector).forEach(link => {
                    link.setAttribute('href', social[platform]);
                });
            }
        });
    }

    replaceFooterContactInfo() {
        const { phones, emails, addresses } = this.contactData;

        console.log('🔄 Starting footer contact info replacement...');
        console.log('Contact data:', { phones, emails, addresses });

        // Update footer contact elements by ID with aggressive replacement
        const contactElements = {
            'contact-address-primary': addresses.primary,
            'contact-address-secondary': addresses.secondary,
            'contact-phone': phones.general,
            'contact-email': emails.general
        };

        let replacements = 0;
        Object.keys(contactElements).forEach(id => {
            const element = document.getElementById(id);
            console.log(`Checking element ${id}:`, element ? `Found - "${element.textContent}"` : 'Not found');

            if (element && contactElements[id]) {
                // Force replace content regardless of current content
                if (id === 'contact-phone') {
                    const phoneNumber = contactElements[id].replace(/\D/g, '');
                    element.innerHTML = `<a href="tel:${phoneNumber}" style="color: inherit; text-decoration: none;">${contactElements[id]}</a>`;
                } else if (id === 'contact-email') {
                    element.innerHTML = `<a href="mailto:${contactElements[id]}" style="color: inherit; text-decoration: none;">${contactElements[id]}</a>`;
                } else {
                    element.textContent = contactElements[id];
                }

                console.log(`✅ Replaced ${id} with: ${contactElements[id]}`);
                replacements++;

                // Show secondary address if it exists and is different from primary
                if (id === 'contact-address-secondary' && addresses.secondary && addresses.secondary !== addresses.primary) {
                    const wrapper = document.getElementById('contact-address-secondary-wrapper');
                    if (wrapper) {
                        wrapper.style.display = 'block';
                        console.log('✅ Secondary address wrapper shown');
                    }
                }
            } else if (element) {
                // If no data available, show a fallback message
                element.textContent = 'Contact information unavailable';
                console.log(`⚠️ Set fallback message for ${id}`);
            } else {
                console.log(`❌ Element ${id} not found in DOM`);
            }
        });

        // Also replace any "Loading..." text in footer using text walker
        this.replaceTextContent('Loading address...', addresses.primary);
        this.replaceTextContent('Loading phone...', phones.general);
        this.replaceTextContent('Loading email...', emails.general);

        console.log(`✅ Footer contact information replacement complete: ${replacements} elements updated`);

        // Verify replacement worked
        setTimeout(() => {
            this.verifyFooterReplacement();
        }, 100);
    }

    verifyFooterReplacement() {
        console.log('🔍 Verifying footer contact replacement...');

        const elements = ['contact-address-primary', 'contact-phone', 'contact-email'];
        let stillLoading = [];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element && element.textContent.includes('Loading')) {
                stillLoading.push(id);
                console.log(`❌ ${id} still shows: "${element.textContent}"`);
            } else if (element) {
                console.log(`✅ ${id} shows: "${element.textContent}"`);
            }
        });

        if (stillLoading.length > 0) {
            console.log(`⚠️ ${stillLoading.length} elements still showing "Loading..." - forcing replacement`);
            this.forceReplaceLoadingText();
        } else {
            console.log('✅ All footer elements successfully replaced');
        }
    }

    forceReplaceLoadingText() {
        console.log('🔧 Force replacing any remaining "Loading..." text');

        const fallbackData = {
            'contact-address-primary': '123 Demo Street, Demo City, DC 12345, United States',
            'contact-phone': '+1 (555) 123-DEMO',
            'contact-email': '<EMAIL>'
        };

        Object.keys(fallbackData).forEach(id => {
            const element = document.getElementById(id);
            if (element && element.textContent.includes('Loading')) {
                if (id === 'contact-phone') {
                    const phoneNumber = fallbackData[id].replace(/\D/g, '');
                    element.innerHTML = `<a href="tel:${phoneNumber}" style="color: inherit; text-decoration: none;">${fallbackData[id]}</a>`;
                } else if (id === 'contact-email') {
                    element.innerHTML = `<a href="mailto:${fallbackData[id]}" style="color: inherit; text-decoration: none;">${fallbackData[id]}</a>`;
                } else {
                    element.textContent = fallbackData[id];
                }
                console.log(`🔧 Force replaced ${id} with fallback data`);
            }
        });
    }

    replaceTextContent(oldText, newText) {
        if (!newText || newText === oldText) return;

        // Find all text nodes containing the old text
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.includes(oldText)) {
                textNodes.push(node);
            }
        }

        // Replace text in found nodes
        textNodes.forEach(textNode => {
            textNode.textContent = textNode.textContent.replace(new RegExp(this.escapeRegex(oldText), 'g'), newText);
        });
    }

    replaceHrefAttribute(oldHref, newHref) {
        if (!newHref || newHref === oldHref) return;

        document.querySelectorAll(`a[href="${oldHref}"]`).forEach(link => {
            link.setAttribute('href', newHref);
        });
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Public method to manually refresh contact data
    async refresh() {
        await this.init();
    }

    // Get current contact data
    getContactData() {
        return this.contactData;
    }
}

// Make ContactLoader available globally with error handling
try {
    window.ContactLoader = ContactLoader;
    console.log('✅ ContactLoader class exported to global scope');
} catch (error) {
    console.error('❌ Error exporting ContactLoader class:', error);
}

// Immediate availability check
setTimeout(() => {
    if (window.ContactLoader) {
        console.log('✅ ContactLoader confirmed available globally');
    } else {
        console.error('❌ ContactLoader not available in global scope');
        // Try to re-export
        try {
            window.ContactLoader = ContactLoader;
            console.log('🔄 ContactLoader re-exported successfully');
        } catch (error) {
            console.error('❌ Failed to re-export ContactLoader:', error);
        }
    }
}, 100);

// Enhanced initialization sequence with multiple fallbacks
document.addEventListener('DOMContentLoaded', function() {
    console.log('Contact loader script loaded');
    console.log('ContactLoader available:', !!window.ContactLoader);

    // First try: When main.js signals footer is loaded
    window.addEventListener('footerLoaded', function() {
        console.log('Footer loaded event received');
        initializeContactLoader();
    });

    // Second try: If footerLoaded event doesn't fire, check every 500ms
    const footerCheckInterval = setInterval(() => {
        const footer = document.querySelector('footer');
        if (footer && document.getElementById('contact-phone')) {
            console.log('Footer elements detected via interval check');
            clearInterval(footerCheckInterval);
            initializeContactLoader();
        }
    }, 500);

    // Final backup: Initialize after 3 seconds
    setTimeout(() => {
        if (!window.contactLoader) {
            console.log('3s backup initialization');
            initializeContactLoader();
        }
        clearInterval(footerCheckInterval); // Clean up interval
    }, 3000);
});

function initializeContactLoader() {
    // Don't initialize if already exists
    if (window.contactLoader) {
        console.log('Contact loader already initialized');
        return;
    }

    // Enhanced element detection - check for all required elements
    const requiredElements = [
        'contact-address-primary',
        'contact-phone',
        'contact-email'
    ];

    const elementsExist = requiredElements.every(id => document.getElementById(id));
    const elementsFound = requiredElements.filter(id => document.getElementById(id)).length;

    console.log(`Footer contact elements found: ${elementsFound}/${requiredElements.length}`);
    console.log('Required elements status:', requiredElements.map(id => ({
        id,
        exists: !!document.getElementById(id),
        content: document.getElementById(id)?.textContent?.substring(0, 30) + '...'
    })));

    if (elementsExist) {
        console.log('All footer contact elements detected, initializing contact loader...');

        try {
            // Try using global ContactLoader first
            if (window.ContactLoader) {
                window.contactLoader = new window.ContactLoader();
                console.log('✅ Contact loader initialized using global class');
            } else {
                // Fallback: use local ContactLoader class directly
                window.contactLoader = new ContactLoader();
                console.log('✅ Contact loader initialized using local class');
            }
        } catch (error) {
            console.error('❌ Error initializing contact loader:', error);
            console.log('🔄 Attempting fallback initialization...');

            // Last resort: create a simple contact replacer
            createFallbackContactLoader();
        }
    } else {
        console.log('Delaying initialization - elements not found');
        console.log('Missing elements:', requiredElements.filter(id => !document.getElementById(id)));

        // Recursive retry with delay
        setTimeout(() => {
            initializeContactLoader();
        }, 500);
    }
}

function createFallbackContactLoader() {
    console.log('Creating fallback contact loader...');

    // Simple fallback that just replaces the loading text
    const fallbackData = {
        phones: { general: '+1 (555) 123-DEMO', emergency: '(*************' },
        emails: { general: '<EMAIL>', help: '<EMAIL>', emergency: '<EMAIL>' },
        addresses: { primary: '123 Demo Street, Demo City, DC 12345, United States', secondary: '456 Sample Avenue, Example Town, ET 67890, United Kingdom' }
    };

    // Replace loading text directly
    const addressEl = document.getElementById('contact-address-primary');
    const phoneEl = document.getElementById('contact-phone');
    const emailEl = document.getElementById('contact-email');

    if (addressEl && addressEl.textContent.includes('Loading')) {
        addressEl.textContent = fallbackData.addresses.primary;
        console.log('✅ Address replaced with fallback data');
    }

    if (phoneEl && phoneEl.textContent.includes('Loading')) {
        phoneEl.innerHTML = `<a href="tel:${fallbackData.phones.general.replace(/\D/g, '')}" style="color: inherit; text-decoration: none;">${fallbackData.phones.general}</a>`;
        console.log('✅ Phone replaced with fallback data');
    }

    if (emailEl && emailEl.textContent.includes('Loading')) {
        emailEl.innerHTML = `<a href="mailto:${fallbackData.emails.general}" style="color: inherit; text-decoration: none;">${fallbackData.emails.general}</a>`;
        console.log('✅ Email replaced with fallback data');
    }

    // Create a minimal contact loader object
    window.contactLoader = {
        contactData: fallbackData,
        getContactData: () => fallbackData,
        refresh: () => console.log('Fallback contact loader refresh called')
    };

    console.log('✅ Fallback contact loader created and contact info replaced');
}

// Note: waitForFooter function removed - replaced with enhanced initialization sequence above

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ContactLoader;
}

// Global helper functions for manual debugging
window.debugContactLoader = function() {
    console.log('=== Contact Loader Debug Info ===');
    console.log('ContactLoader class available:', !!window.ContactLoader);
    console.log('contactLoader instance available:', !!window.contactLoader);
    console.log('Footer elements:');
    console.log('  - Address:', document.getElementById('contact-address-primary'));
    console.log('  - Phone:', document.getElementById('contact-phone'));
    console.log('  - Email:', document.getElementById('contact-email'));

    if (window.contactLoader && window.contactLoader.contactData) {
        console.log('Contact data:', window.contactLoader.contactData);
    }
};

window.forceContactReplace = function() {
    console.log('Forcing contact replacement...');

    // Direct replacement without waiting for contact loader
    const fallbackData = {
        'contact-address-primary': '123 Demo Street, Demo City, DC 12345, United States',
        'contact-phone': '+1 (555) 123-DEMO',
        'contact-email': '<EMAIL>'
    };

    Object.keys(fallbackData).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (id === 'contact-phone') {
                const phoneNumber = fallbackData[id].replace(/\D/g, '');
                element.innerHTML = `<a href="tel:${phoneNumber}" style="color: inherit; text-decoration: none;">${fallbackData[id]}</a>`;
            } else if (id === 'contact-email') {
                element.innerHTML = `<a href="mailto:${fallbackData[id]}" style="color: inherit; text-decoration: none;">${fallbackData[id]}</a>`;
            } else {
                element.textContent = fallbackData[id];
            }
            console.log(`✅ Force replaced ${id}: ${fallbackData[id]}`);
        } else {
            console.log(`❌ Element ${id} not found`);
        }
    });

    // Also create fallback contact loader if needed
    if (!window.contactLoader) {
        createFallbackContactLoader();
    }
};

window.manualContactInit = function() {
    console.log('Manual contact loader initialization...');
    initializeContactLoader();
};

// Emergency console commands for troubleshooting
window.emergencyFooterFix = function() {
    console.log('🚨 Emergency footer fix - dispatching footerLoaded event');
    window.dispatchEvent(new CustomEvent('footerLoaded'));

    setTimeout(() => {
        if (window.contactLoader && window.contactLoader.replaceFooterContactInfo) {
            console.log('🔧 Manually triggering footer contact replacement');
            window.contactLoader.replaceFooterContactInfo();
        } else {
            console.log('⚠️ Contact loader not available, using force replace');
            window.forceContactReplace();
        }
    }, 500);
};

console.log('🔧 Contact loader debugging functions available:');
console.log('  - debugContactLoader() - Show debug info');
console.log('  - forceContactReplace() - Force replace loading text');
console.log('  - manualContactInit() - Manual initialization');
console.log('  - emergencyFooterFix() - Emergency fix for persistent loading text');
