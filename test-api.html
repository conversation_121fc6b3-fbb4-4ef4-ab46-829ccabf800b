<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .post {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .image-preview {
            width: 200px;
            height: 150px;
            background-size: cover;
            background-position: center;
            border-radius: 5px;
            margin: 10px 0;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>API Test - Blog Posts</h1>
        <button onclick="testAPI()">Test API</button>
        <div id="results"></div>
    </div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="status">Testing API...</div>';

            try {
                console.log('Fetching from API...');
                const response = await fetch('api/get-posts.php?limit=5');
                console.log('Response status:', response.status);
                
                const data = await response.json();
                console.log('API Response:', data);

                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="status success">
                            ✅ API Working! Found ${data.posts.length} posts
                        </div>
                        <div>
                            <strong>Total posts in database:</strong> ${data.pagination.total_posts}
                        </div>
                    `;

                    data.posts.forEach((post, index) => {
                        const postDiv = document.createElement('div');
                        postDiv.className = 'post';
                        
                        let imageHtml = '';
                        if (post.featured_image) {
                            let imagePath = post.featured_image.trim();
                            
                            // Try different path variations
                            const paths = [
                                imagePath,
                                'admin/' + imagePath,
                                imagePath.replace('admin/', ''),
                                'admin/uploads/' + imagePath.replace(/^.*\//, ''),
                                'uploads/' + imagePath.replace(/^.*\//, '')
                            ];

                            imageHtml = `
                                <div><strong>Featured Image Tests:</strong></div>
                                <div style="margin: 10px 0;">
                                    <div>Original path: <code>${post.featured_image}</code></div>
                                    ${paths.map((path, i) => `
                                        <div style="margin: 5px 0;">
                                            <strong>Path ${i + 1}:</strong> <code>${path}</code>
                                            <div class="image-preview" style="background-image: url('${path}');" 
                                                 onload="this.style.border='2px solid green';" 
                                                 onerror="this.style.border='2px solid red';">
                                                Testing: ${path}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            `;
                        } else {
                            imageHtml = '<div style="color: orange;">❌ No featured image</div>';
                        }

                        postDiv.innerHTML = `
                            <h3>${post.title}</h3>
                            <div><strong>ID:</strong> ${post.id}</div>
                            <div><strong>Author:</strong> ${post.author}</div>
                            <div><strong>Date:</strong> ${post.formatted_date}</div>
                            <div><strong>Category:</strong> ${post.category}</div>
                            ${imageHtml}
                            <div><strong>Excerpt:</strong> ${post.excerpt}</div>
                        `;
                        
                        resultsDiv.appendChild(postDiv);
                    });

                } else {
                    resultsDiv.innerHTML = `
                        <div class="status error">
                            ❌ API Error: ${data.error || 'Unknown error'}
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <div class="status error">
                        ❌ Network Error: ${error.message}
                    </div>
                `;
            }
        }

        // Test immediately on load
        window.addEventListener('load', testAPI);
    </script>
</body>
</html>
