/**
 * Simple Contact Loader for Footer.html
 * Fetches contact information from admin/contact-settings.php via API
 * and updates footer.html elements with database values
 */

class SimpleContactLoader {
    constructor() {
        this.apiUrl = 'api/get-contact-info.php';
        this.fallbackData = {
            phones: {
                general: '+1 (555) 123-DEMO',
                emergency: '(*************'
            },
            emails: {
                general: '<EMAIL>',
                emergency: '<EMAIL>'
            },
            addresses: {
                primary: '123 Demo Street, Demo City, DC 12345, United States',
                secondary: '456 Sample Avenue, Example Town, ET 67890, United Kingdom'
            },
            business: {
                hours: '24/7 Emergency Support Available'
            },
            social: {
                facebook: '#',
                twitter: '#',
                linkedin: '#',
                instagram: '#'
            }
        };
        this.init();
    }

    async init() {
        console.log('🔄 Simple Contact Loader: Starting...');
        
        // Wait for footer to be loaded
        this.waitForFooter(() => {
            this.loadAndUpdateContact();
        });
    }

    waitForFooter(callback) {
        const checkFooter = () => {
            const footer = document.querySelector('footer');
            const contactItems = document.querySelectorAll('.contact-item');
            
            if (footer && contactItems.length > 0) {
                console.log('✅ Footer detected, loading contact info...');
                callback();
            } else {
                console.log('⏳ Waiting for footer...');
                setTimeout(checkFooter, 500);
            }
        };
        
        checkFooter();
    }

    async loadAndUpdateContact() {
        try {
            console.log('🌐 Fetching contact data from API...');
            const response = await fetch(this.apiUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.data) {
                console.log('✅ API data received:', data.data);
                this.updateFooterWithData(data.data);
            } else {
                throw new Error('API returned unsuccessful response');
            }
            
        } catch (error) {
            console.warn('⚠️ API failed, using fallback data:', error.message);
            this.updateFooterWithData(this.fallbackData);
        }
    }

    updateFooterWithData(contactData) {
        console.log('🔄 Updating footer with contact data...');
        
        // Update addresses
        this.updateElement('.contact-item:nth-child(1) span', contactData.addresses.primary);
        this.updateElement('.contact-item:nth-child(2) span', contactData.addresses.secondary);
        
        // Update phone numbers with clickable links
        this.updatePhoneElement('.contact-item:nth-child(3)', contactData.phones.general, false);
        this.updatePhoneElement('.contact-item:nth-child(4)', contactData.phones.emergency, true);
        
        // Update emails with clickable links
        this.updateEmailElement('.contact-item:nth-child(5)', contactData.emails.general);
        this.updateEmailElement('.contact-item:nth-child(6)', contactData.emails.emergency);
        
        // Update business hours
        this.updateElement('.contact-item.business-hours span', contactData.business.hours);
        
        // Update social media links
        this.updateSocialLinks(contactData.social);
        
        console.log('✅ Footer contact information updated successfully!');
    }

    updateElement(selector, content) {
        const element = document.querySelector(selector);
        if (element && content) {
            element.textContent = content;
            console.log(`📝 Updated ${selector}: ${content}`);
        }
    }

    updatePhoneElement(selector, phoneNumber, isEmergency = false) {
        const contactItem = document.querySelector(selector);
        if (!contactItem || !phoneNumber) return;
        
        const span = contactItem.querySelector('span') || contactItem.querySelector('a');
        if (!span) return;
        
        // Create clickable phone link
        const cleanPhone = phoneNumber.replace(/\D/g, '');
        const phoneLink = document.createElement('a');
        phoneLink.href = `tel:+${cleanPhone}`;
        phoneLink.textContent = phoneNumber;
        phoneLink.className = isEmergency ? 'contact-link emergency-phone' : 'contact-link';
        phoneLink.style.color = 'inherit';
        phoneLink.style.textDecoration = 'none';
        
        // Replace the span with the link
        span.parentNode.replaceChild(phoneLink, span);
        
        // Add emergency label if needed
        if (isEmergency) {
            let emergencyLabel = contactItem.querySelector('.emergency-label');
            if (!emergencyLabel) {
                emergencyLabel = document.createElement('small');
                emergencyLabel.className = 'emergency-label';
                emergencyLabel.textContent = 'Emergency Line';
                contactItem.appendChild(emergencyLabel);
            }
        }
        
        console.log(`📞 Updated phone ${selector}: ${phoneNumber} (Emergency: ${isEmergency})`);
    }

    updateEmailElement(selector, email) {
        const contactItem = document.querySelector(selector);
        if (!contactItem || !email) return;
        
        const span = contactItem.querySelector('span') || contactItem.querySelector('a');
        if (!span) return;
        
        // Create clickable email link
        const emailLink = document.createElement('a');
        emailLink.href = `mailto:${email}`;
        emailLink.textContent = email;
        emailLink.className = 'contact-link';
        emailLink.style.color = 'inherit';
        emailLink.style.textDecoration = 'none';
        
        // Replace the span with the link
        span.parentNode.replaceChild(emailLink, span);
        
        console.log(`📧 Updated email ${selector}: ${email}`);
    }

    updateSocialLinks(socialData) {
        const socialLinks = document.querySelector('.social-links');
        if (!socialLinks) return;
        
        const socialMap = {
            'facebook': 'fab fa-facebook-f',
            'twitter': 'fab fa-twitter', 
            'linkedin': 'fab fa-linkedin-in',
            'instagram': 'fab fa-instagram'
        };
        
        Object.keys(socialMap).forEach((platform, index) => {
            const link = socialLinks.children[index];
            if (link && socialData[platform] && socialData[platform] !== '#') {
                link.href = socialData[platform];
                link.style.display = 'flex';
            } else if (link) {
                link.style.display = 'none';
            }
        });
        
        console.log('🌐 Updated social media links');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Simple Contact Loader: DOM ready, initializing...');
    
    // Small delay to ensure footer is loaded by main.js
    setTimeout(() => {
        window.simpleContactLoader = new SimpleContactLoader();
    }, 1000);
});

// Export for global access
window.SimpleContactLoader = SimpleContactLoader;
