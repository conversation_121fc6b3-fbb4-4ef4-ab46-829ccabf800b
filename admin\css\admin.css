/* Admin Dashboard Styles - Forensic Recovery Services */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Manrope', sans-serif;
    background-color: #f8fafc;
    color: #334155;
    line-height: 1.6;
}

/* Variables matching the main site */
:root {
    --primary-blue: #1e40af;
    --primary-blue-hover: #1d4ed8;
    --light-blue: #eff6ff;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --border-radius: 8px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-hover) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-container {
    width: 100%;
    max-width: 400px;
}

.login-card {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    padding: 2.5rem;
    text-align: center;
}

.login-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.login-header p {
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.login-form {
    text-align: left;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.login-footer {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* Admin Dashboard Layout */
.admin-dashboard {
    display: grid;
    grid-template-areas: 
        "nav nav"
        "sidebar main";
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    min-height: 100vh;
}

/* Navigation */
.admin-nav {
    grid-area: nav;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
}

.nav-brand h2 {
    color: var(--primary-blue);
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-user span {
    color: var(--gray-600);
    font-weight: 500;
}

/* Sidebar */
.admin-sidebar {
    grid-area: sidebar;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    padding: 1.5rem 0;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: background-color 0.2s, color 0.2s;
    font-weight: 500;
}

.menu-item:hover {
    background-color: var(--gray-50);
    color: var(--gray-900);
}

.menu-item.active {
    background-color: var(--light-blue);
    color: var(--primary-blue);
    border-right: 3px solid var(--primary-blue);
}

.menu-item .icon {
    font-size: 1.125rem;
}

/* Main Content */
.admin-main {
    grid-area: main;
    padding: 2rem;
    overflow-y: auto;
}

.admin-header {
    margin-bottom: 2rem;
}

.admin-header h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.admin-header p {
    color: var(--gray-600);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-blue);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-icon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.3;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.dashboard-section h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.action-icon {
    font-size: 2rem;
    color: var(--primary-blue);
    flex-shrink: 0;
}

.action-content h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.action-content p {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Recent Posts */
.recent-posts {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.posts-table {
    width: 100%;
}

.table-header,
.table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 1rem;
    padding: 1rem 1.5rem;
    align-items: center;
}

.table-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table-row {
    border-bottom: 1px solid var(--gray-100);
    transition: background-color 0.2s;
}

.table-row:hover {
    background: var(--gray-50);
}

.table-row:last-child {
    border-bottom: none;
}

.col-title {
    font-weight: 500;
    color: var(--gray-900);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-published {
    background: #dcfce7;
    color: #166534;
}

.status-draft {
    background: #fef3c7;
    color: #92400e;
}

.status-archived {
    background: var(--gray-100);
    color: var(--gray-600);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--gray-500);
}

.empty-state a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 500;
}

.empty-state a:hover {
    text-decoration: underline;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-blue-hover);
}

.btn-outline {
    background: transparent;
    color: var(--primary-blue);
    border: 1px solid var(--primary-blue);
}

.btn-outline:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.alert-error {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #f0fdf4;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-warning {
    background: #fffbeb;
    color: #92400e;
    border: 1px solid #fed7aa;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-dashboard {
        grid-template-areas: 
            "nav"
            "main";
        grid-template-columns: 1fr;
    }
    
    .admin-sidebar {
        display: none;
    }
    
    .admin-nav {
        padding: 1rem;
    }
    
    .nav-user span {
        display: none;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
    }
    
    .col-actions {
        display: none;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        padding: 2rem;
    }
    
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
        gap: 0.25rem;
    }
    
    .col-status,
    .col-date {
        font-size: 0.8125rem;
    }
}
