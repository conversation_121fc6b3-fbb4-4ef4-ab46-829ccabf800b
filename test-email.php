<?php
/**
 * Email Test Script for Forensic Involve
 * Use this to test your email configuration before going live
 */

// Include PHPMailer
require_once 'vendor/autoload.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

// Load configuration from config.php
$config = require_once 'config.php';

echo "<!DOCTYPE html><html><head><title>Email Test</title>";
echo "<style>body{font-family:Arial,sans-serif;max-width:600px;margin:50px auto;padding:20px;}";
echo ".success{background:#d1fae5;border:1px solid #a7f3d0;color:#065f46;padding:15px;border-radius:5px;}";
echo ".error{background:#fee2e2;border:1px solid #fecaca;color:#dc2626;padding:15px;border-radius:5px;}";
echo ".info{background:#dbeafe;border:1px solid #93c5fd;color:#1e40af;padding:15px;border-radius:5px;}";
echo "</style></head><body>";

echo "<h1>📧 Email Configuration Test</h1>";

// Check if configuration is still default
if ($config['smtp']['username'] === '<EMAIL>') {
    echo "<div class='error'>";
    echo "<h3>⚠️ Configuration Required</h3>";
    echo "<p>Please update the configuration in this file with your actual email settings:</p>";
    echo "<ul>";
    echo "<li><strong>smtp_username:</strong> Your Gmail address</li>";
    echo "<li><strong>smtp_password:</strong> Your Gmail app password</li>";
    echo "<li><strong>from_email:</strong> Your Gmail address</li>";
    echo "<li><strong>to_email:</strong> Destination email (<EMAIL>)</li>";
    echo "</ul>";
    echo "</div>";
    exit;
}

try {
    echo "<div class='info'>";
    echo "<h3>🔧 Testing Configuration</h3>";
    echo "<p><strong>SMTP Host:</strong> {$config['smtp']['host']}</p>";
    echo "<p><strong>SMTP Port:</strong> {$config['smtp']['port']}</p>";
    echo "<p><strong>From:</strong> {$config['email']['from_address']}</p>";
    echo "<p><strong>To:</strong> {$config['email']['to_address']}</p>";
    echo "</div>";

    // Create PHPMailer instance
    $mail = new PHPMailer(true);

    // Server settings
    $mail->isSMTP();
    $mail->Host = $config['smtp']['host'];
    $mail->SMTPAuth = true;
    $mail->Username = $config['smtp']['username'];
    $mail->Password = $config['smtp']['password'];
    $mail->SMTPSecure = ($config['smtp']['encryption'] === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port = $config['smtp']['port'];

    // Enable verbose debug output (comment out for production)
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = function($str, $level) {
        echo "<p style='font-family:monospace;font-size:12px;color:#666;'>$str</p>";
    };

    // Recipients
    $mail->setFrom($config['email']['from_address'], $config['email']['from_name']);
    $mail->addAddress($config['email']['to_address'], $config['email']['to_name']);

    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Test Email - Forensic Involve Form Setup';

    $mail->Body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Test Email</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: #1e40af; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
                <h1>✅ Email Test Successful!</h1>
                <p>This is a test email from your Forensic Involve application form setup.</p>
            </div>

            <div style="background: #f8fafc; padding: 20px; border-radius: 0 0 8px 8px;">
                <h2>📋 Test Details</h2>
                <p><strong>Sent:</strong> ' . date('F j, Y \a\t g:i A T') . '</p>
                <p><strong>From:</strong> ' . $config['email']['from_address'] . '</p>
                <p><strong>To:</strong> ' . $config['email']['to_address'] . '</p>
                <p><strong>SMTP Server:</strong> ' . $config['smtp']['host'] . ':' . $config['smtp']['port'] . '</p>

                <div style="background: #d1fae5; border: 1px solid #a7f3d0; color: #065f46; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>🎉 Configuration Working!</h3>
                    <p>If you received this email, your PHPMailer configuration is working correctly.
                    You can now use the application form with confidence.</p>
                </div>

                <h3>🔧 Next Steps:</h3>
                <ol>
                    <li>Test the actual application form at <strong>apply.html</strong></li>
                    <li>Fill out the form with test data</li>
                    <li>Verify you receive the formatted application email</li>
                    <li>Remove or comment out the debug output in production</li>
                </ol>

                <div style="background: #fef3c7; border: 1px solid #fde68a; color: #92400e; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>⚠️ Security Reminder</h4>
                    <p>Remember to:</p>
                    <ul>
                        <li>Disable debug mode in production</li>
                        <li>Use environment variables for sensitive data</li>
                        <li>Enable HTTPS on your website</li>
                        <li>Regularly update your app passwords</li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
    </html>';

    $mail->AltBody = 'Email Test Successful! Your Forensic Involve form email configuration is working correctly. Sent: ' . date('F j, Y \a\t g:i A T');

    // Send email
    echo "<div class='info'><h3>📤 Sending Test Email...</h3></div>";

    $mail->send();

    echo "<div class='success'>";
    echo "<h3>🎉 Email Sent Successfully!</h3>";
    echo "<p>A test email has been sent to <strong>{$config['email']['to_address']}</strong></p>";
    echo "<p>Please check your inbox to confirm receipt.</p>";
    echo "<h4>✅ What this means:</h4>";
    echo "<ul>";
    echo "<li>Your SMTP configuration is correct</li>";
    echo "<li>PHPMailer is working properly</li>";
    echo "<li>Your email credentials are valid</li>";
    echo "<li>The application form should work correctly</li>";
    echo "</ul>";
    echo "<h4>🔧 Next Steps:</h4>";
    echo "<ol>";
    echo "<li>Test the actual form at <a href='apply.html'>apply.html</a></li>";
    echo "<li>Disable debug output in production</li>";
    echo "<li>Monitor email delivery for actual submissions</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Email Test Failed</h3>";
    echo "<p><strong>Error:</strong> {$mail->ErrorInfo}</p>";
    echo "<p><strong>Exception:</strong> {$e->getMessage()}</p>";

    echo "<h4>🔧 Troubleshooting Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Check your email credentials:</strong> Ensure username and app password are correct</li>";
    echo "<li><strong>Verify 2-factor authentication:</strong> Must be enabled for Gmail app passwords</li>";
    echo "<li><strong>Check SMTP settings:</strong> Host and port must be correct</li>";
    echo "<li><strong>Firewall issues:</strong> Ensure outbound SMTP connections are allowed</li>";
    echo "<li><strong>App password:</strong> Use the 16-character app password, not your regular password</li>";
    echo "</ol>";

    echo "<h4>📋 Common Solutions:</h4>";
    echo "<ul>";
    echo "<li>Regenerate your Gmail app password</li>";
    echo "<li>Try using port 465 with SSL instead of 587 with TLS</li>";
    echo "<li>Check if your hosting provider blocks SMTP</li>";
    echo "<li>Verify your email address is correct</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>💡 <strong>Tip:</strong> Once everything is working, you can delete this test file for security.</small></p>";
echo "</body></html>";
?>
