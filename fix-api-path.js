/**
 * Immediate API Path Fix
 * Run this in browser console to fix the API path issue and load contact data
 */

(function() {
    console.log('🔧 API Path Fix - Starting...');
    
    // Test different API paths to find the working one
    async function findWorkingApiPath() {
        const basePath = window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1);
        
        const pathsToTest = [
            'api/get-contact-info.php',
            './api/get-contact-info.php',
            basePath + 'api/get-contact-info.php',
            '/api/get-contact-info.php'
        ];
        
        console.log('🔍 Testing API paths...');
        console.log('Current page:', window.location.href);
        console.log('Base path:', basePath);
        
        for (const path of pathsToTest) {
            try {
                console.log(`🔌 Testing: ${path}`);
                const response = await fetch(path);
                
                console.log(`Response status: ${response.status} for ${path}`);
                
                if (response.ok) {
                    const text = await response.text();
                    console.log(`Response preview: ${text.substring(0, 100)}...`);
                    
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            console.log(`✅ Working API path found: ${path}`);
                            return { path, data: data.data };
                        } else {
                            console.log(`⚠️ API responded but success=false: ${data.error || 'Unknown error'}`);
                        }
                    } catch (parseError) {
                        console.log(`❌ Invalid JSON response from ${path}`);
                    }
                } else {
                    console.log(`❌ HTTP ${response.status} for ${path}`);
                }
            } catch (error) {
                console.log(`❌ Network error for ${path}: ${error.message}`);
            }
        }
        
        console.log('⚠️ No working API path found, will use fallback data');
        return null;
    }
    
    // Replace contact information in footer
    function replaceContactInfo(contactData) {
        console.log('🔄 Replacing contact information...');
        
        const elements = {
            'contact-address-primary': contactData.addresses?.primary || '123 Demo Street, Demo City, DC 12345, United States',
            'contact-phone': contactData.phones?.general || '+1 (555) 123-DEMO',
            'contact-email': contactData.emails?.general || '<EMAIL>'
        };
        
        let replacements = 0;
        
        Object.keys(elements).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                const value = elements[id];
                
                if (id === 'contact-phone') {
                    const phoneNumber = value.replace(/\D/g, '');
                    element.innerHTML = `<a href="tel:${phoneNumber}" style="color: inherit; text-decoration: none;">${value}</a>`;
                } else if (id === 'contact-email') {
                    element.innerHTML = `<a href="mailto:${value}" style="color: inherit; text-decoration: none;">${value}</a>`;
                } else {
                    element.textContent = value;
                }
                
                console.log(`✅ Replaced ${id}: ${value}`);
                replacements++;
            } else {
                console.log(`❌ Element not found: ${id}`);
            }
        });
        
        // Handle secondary address
        const secondaryAddressEl = document.getElementById('contact-address-secondary');
        const secondaryWrapper = document.getElementById('contact-address-secondary-wrapper');
        if (secondaryAddressEl && secondaryWrapper && contactData.addresses?.secondary) {
            secondaryAddressEl.textContent = contactData.addresses.secondary;
            secondaryWrapper.style.display = 'block';
            console.log('✅ Secondary address shown');
            replacements++;
        }
        
        return replacements;
    }
    
    // Fix the contact loader API URL if it exists
    function fixContactLoaderApiUrl(workingPath) {
        if (window.contactLoader && workingPath) {
            console.log('🔧 Fixing existing contact loader API URL...');
            window.contactLoader.apiUrl = workingPath;
            console.log(`✅ Contact loader API URL updated to: ${workingPath}`);
            
            // Try to reload contact data
            if (window.contactLoader.loadContactData) {
                window.contactLoader.loadContactData()
                    .then(() => {
                        console.log('✅ Contact data reloaded successfully');
                        window.contactLoader.replaceContactInfo();
                    })
                    .catch(error => {
                        console.log('❌ Failed to reload contact data:', error);
                    });
            }
        }
    }
    
    // Main execution
    async function executeApiPathFix() {
        console.log('🔍 Checking footer elements...');
        
        const footerElements = [
            document.getElementById('contact-address-primary'),
            document.getElementById('contact-phone'),
            document.getElementById('contact-email')
        ];
        
        const elementsFound = footerElements.filter(el => el !== null).length;
        console.log(`Footer elements found: ${elementsFound}/3`);
        
        if (elementsFound === 0) {
            console.log('❌ No footer contact elements found. Footer may not be loaded yet.');
            console.log('💡 Try running this script again after the footer loads.');
            return;
        }
        
        console.log('🔌 Finding working API path...');
        const apiResult = await findWorkingApiPath();
        
        let contactData = {
            phones: { general: '+1 (555) 123-DEMO' },
            emails: { general: '<EMAIL>' },
            addresses: { 
                primary: '123 Demo Street, Demo City, DC 12345, United States',
                secondary: '456 Sample Avenue, Example Town, ET 67890, United Kingdom'
            }
        };
        
        if (apiResult) {
            console.log('✅ Using real contact data from API');
            contactData = apiResult.data;
            fixContactLoaderApiUrl(apiResult.path);
        } else {
            console.log('⚠️ Using fallback demo contact data');
        }
        
        const replacements = replaceContactInfo(contactData);
        
        if (replacements > 0) {
            console.log(`✅ API path fix complete! ${replacements} elements updated.`);
            console.log('📞 Footer should now show contact information.');
            
            // Create or update contact loader
            if (!window.contactLoader) {
                window.contactLoader = {
                    apiUrl: apiResult?.path || 'api/get-contact-info.php',
                    contactData: contactData,
                    getContactData: () => contactData,
                    replaceContactInfo: () => replaceContactInfo(contactData)
                };
                console.log('✅ Contact loader object created');
            }
        } else {
            console.log('ℹ️ No elements were replaced. Check if footer elements exist.');
        }
    }
    
    // Execute the fix
    executeApiPathFix();
    
    // Make functions available globally
    window.fixApiPath = executeApiPathFix;
    window.testApiPaths = findWorkingApiPath;
    
    console.log('🔧 API path fix functions available:');
    console.log('  - fixApiPath() - Run the API path fix again');
    console.log('  - testApiPaths() - Test API paths only');
    
})();

console.log('🔧 API Path Fix loaded!');
console.log('💡 If footer still shows "Loading..." text, run: fixApiPath()');
