<?php
require_once 'admin/includes/db_config.php';

echo "<h2>Fixing Database Schema</h2>";

try {
    // Check if category_id column exists
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE 'category_id'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ category_id column missing, adding it...</p>";
        
        // Add the category_id column
        $mysqli->query("ALTER TABLE blog_posts ADD COLUMN category_id int(11) DEFAULT NULL AFTER excerpt");
        $mysqli->query("ALTER TABLE blog_posts ADD INDEX (category_id)");
        
        echo "<p>✅ category_id column added successfully</p>";
    } else {
        echo "<p>✅ category_id column already exists</p>";
    }
    
    // Show current table structure
    echo "<h3>Current blog_posts table structure:</h3>";
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p>✅ Database schema fixed. Admin pages should work now.</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
