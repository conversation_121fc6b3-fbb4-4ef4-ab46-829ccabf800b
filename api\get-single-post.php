<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/includes/db_config.php';

try {
    // Get post ID from parameter
    $postId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if (!$postId) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Post ID is required'
        ]);
        exit;
    }
    
    // Fetch the specific post
    $query = "
        SELECT * FROM blog_posts 
        WHERE id = ? AND status = 'published'
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$postId]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$post) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Post not found'
        ]);
        exit;
    }
    
    // Format post for frontend
    $formatted_post = [
        'id' => $post['id'],
        'title' => $post['title'],
        'excerpt' => $post['excerpt'] ?: substr(strip_tags($post['content']), 0, 200) . '...',
        'content' => $post['content'],
        'author' => $post['author'],
        'category' => $post['category'] ?: 'News',
        'featured_image' => $post['featured_image'],
        'created_at' => $post['created_at'],
        'updated_at' => $post['updated_at'],
        'formatted_date' => date('F j, Y', strtotime($post['created_at'])),
        'slug' => $post['slug'] ?: 'post-' . $post['id'],
        'is_featured' => (bool)$post['is_featured']
    ];
    
    // Update view count if column exists
    try {
        $updateQuery = "UPDATE blog_posts SET views = COALESCE(views, 0) + 1 WHERE id = ?";
        $updateStmt = $pdo->prepare($updateQuery);
        $updateStmt->execute([$postId]);
    } catch (PDOException $e) {
        // Views column might not exist, ignore error
    }
    
    $response = [
        'success' => true,
        'post' => $formatted_post
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
