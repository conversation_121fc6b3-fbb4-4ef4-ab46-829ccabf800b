<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle category actions
if ($_POST) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        if ($action === 'add') {
            $name = trim($_POST['name'] ?? '');
            $slug = trim($_POST['slug'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $color = trim($_POST['color'] ?? '#1e40af');

            // Validation and sanitization
            if (empty($name)) {
                $error = 'Category name is required.';
            } elseif (strlen($name) > 100) {
                $error = 'Category name must be 100 characters or less.';
            } elseif (empty($slug)) {
                $error = 'Category slug is required.';
            } elseif (!preg_match('/^[a-z0-9-]+$/', $slug)) {
                $error = 'Category slug can only contain lowercase letters, numbers, and hyphens.';
            } elseif (strlen($slug) > 100) {
                $error = 'Category slug must be 100 characters or less.';
            } elseif (!preg_match('/^#[0-9a-fA-F]{6}$/', $color)) {
                $error = 'Invalid color format. Use hex format like #1e40af.';
            } else {
                try {
                    // Check if slug already exists
                    $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ?");
                    $stmt->execute([htmlspecialchars($slug, ENT_QUOTES, 'UTF-8')]);
                    if ($stmt->fetch()) {
                        $error = 'Category slug already exists.';
                    } else {
                        // Insert new category
                        $stmt = $pdo->prepare("INSERT INTO categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
                        if ($stmt->execute([
                            htmlspecialchars($name, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($slug, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($description, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($color, ENT_QUOTES, 'UTF-8')
                        ])) {
                            $message = 'Category created successfully.';
                        } else {
                            $error = 'Failed to create category.';
                        }
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
                }
            }
        } elseif ($action === 'edit') {
            $id = (int)($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $slug = trim($_POST['slug'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $color = trim($_POST['color'] ?? '#1e40af');

            // Validation and sanitization
            if ($id <= 0) {
                $error = 'Invalid category ID.';
            } elseif (empty($name)) {
                $error = 'Category name is required.';
            } elseif (strlen($name) > 100) {
                $error = 'Category name must be 100 characters or less.';
            } elseif (empty($slug)) {
                $error = 'Category slug is required.';
            } elseif (!preg_match('/^[a-z0-9-]+$/', $slug)) {
                $error = 'Category slug can only contain lowercase letters, numbers, and hyphens.';
            } elseif (strlen($slug) > 100) {
                $error = 'Category slug must be 100 characters or less.';
            } elseif (!preg_match('/^#[0-9a-fA-F]{6}$/', $color)) {
                $error = 'Invalid color format. Use hex format like #1e40af.';
            } else {
                try {
                    // Check if slug already exists for other categories
                    $stmt = $pdo->prepare("SELECT id FROM categories WHERE slug = ? AND id != ?");
                    $stmt->execute([htmlspecialchars($slug, ENT_QUOTES, 'UTF-8'), $id]);
                    if ($stmt->fetch()) {
                        $error = 'Category slug already exists.';
                    } else {
                        // Update category
                        $stmt = $pdo->prepare("UPDATE categories SET name = ?, slug = ?, description = ?, color = ? WHERE id = ?");
                        if ($stmt->execute([
                            htmlspecialchars($name, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($slug, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($description, ENT_QUOTES, 'UTF-8'),
                            htmlspecialchars($color, ENT_QUOTES, 'UTF-8'),
                            $id
                        ])) {
                            $message = 'Category updated successfully.';
                        } else {
                            $error = 'Failed to update category.';
                        }
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
                }
            }
        } elseif ($action === 'delete') {
            $id = (int)($_POST['id'] ?? 0);

            if ($id <= 0) {
                $error = 'Invalid category ID.';
            } else {
                try {
                    // Check if category is being used by posts
                    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM blog_posts WHERE category_id = ?");
                    $stmt->execute([$id]);
                    $result = $stmt->fetch();
                    $post_count = $result['count'] ?? 0;

                    if ($post_count > 0) {
                        $error = "Cannot delete category. It is being used by $post_count post(s).";
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
                        if ($stmt->execute([$id])) {
                            $message = 'Category deleted successfully.';
                        } else {
                            $error = 'Failed to delete category.';
                        }
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
                }
            }
        }
    }
}

// Get all categories
try {
    $stmt = $pdo->query("SELECT c.*, COUNT(bp.id) as post_count FROM categories c LEFT JOIN blog_posts bp ON c.id = bp.category_id GROUP BY c.id ORDER BY c.name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $categories = [];
    $error = 'Database error: ' . htmlspecialchars($e->getMessage(), ENT_QUOTES, 'UTF-8');
}

$current_user = getCurrentAdmin();
$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - Admin Dashboard</title>    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars($current_user['username'] ?? 'Unknown', ENT_QUOTES, 'UTF-8'); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item active">
                <span class="icon">📁</span>
                Categories
            </a>            <a href="media.php" class="menu-item">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1>Categories</h1>
            <p>Organize your content with categories</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
            </div>
        <?php endif; ?>

        <!-- Add Category Form -->
        <div class="category-form-section">
            <div class="form-header">
                <h2>Add New Category</h2>
                <p>Create a new category to organize your posts</p>
            </div>

            <form method="POST" action="" class="category-form">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" name="action" value="add">
                  <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Category Name *</label>
                        <input type="text" id="name" name="name" required maxlength="100"
                               pattern="[a-zA-Z0-9\s\-_]+"
                               title="Only letters, numbers, spaces, hyphens and underscores allowed"
                               placeholder="Enter category name">
                    </div>
                    <div class="form-group">
                        <label for="slug">Slug *</label>
                        <input type="text" id="slug" name="slug" required maxlength="100"
                               pattern="[a-z0-9\-]+"
                               title="Only lowercase letters, numbers and hyphens allowed"
                               placeholder="category-slug">
                        <small class="form-help">URL-friendly version (auto-generated)</small>
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group form-group-wide">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="3" maxlength="500"
                                  placeholder="Brief description of the category..."></textarea>
                        <small class="form-help">Optional description (max 500 characters)</small>
                    </div>
                    <div class="form-group">
                        <label for="color">Category Color</label>
                        <div class="color-input-group">
                            <input type="color" id="color" name="color" value="#1e40af" class="color-picker">
                            <span class="color-preview-text">#1e40af</span>
                        </div>
                        <small class="form-help">Choose a color to identify this category</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-icon">➕</span>
                        Add Category
                    </button>
                </div>
            </form>
        </div>        <!-- Categories List -->
        <div class="categories-section">
            <div class="section-header">
                <h2>Existing Categories</h2>
                <p>Manage your blog categories</p>
            </div>

            <?php if (empty($categories)): ?>
                <div class="empty-state">
                    <div class="empty-icon">📁</div>
                    <h3>No categories yet</h3>
                    <p>Create your first category using the form above</p>
                </div>
            <?php else: ?>
                <div class="categories-grid">
                    <?php foreach ($categories as $category): ?>
                        <div class="category-card" style="border-left-color: <?php echo htmlspecialchars($category['color'], ENT_QUOTES, 'UTF-8'); ?>">
                            <div class="category-header">
                                <div class="category-title">
                                    <span class="category-color-badge" style="background-color: <?php echo htmlspecialchars($category['color'], ENT_QUOTES, 'UTF-8'); ?>"></span>
                                    <h4><?php echo htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?></h4>
                                </div>
                                <div class="category-stats">
                                    <span class="post-count"><?php echo (int)$category['post_count']; ?> posts</span>
                                </div>
                            </div>

                            <div class="category-meta">
                                <p class="category-slug">
                                    <strong>Slug:</strong>
                                    <code><?php echo htmlspecialchars($category['slug'], ENT_QUOTES, 'UTF-8'); ?></code>
                                </p>
                            </div>

                            <?php if ($category['description']): ?>
                                <div class="category-description">
                                    <p><?php echo htmlspecialchars($category['description'], ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                            <?php endif; ?>

                            <div class="category-footer">
                                <small class="created-date">
                                    Created: <?php echo date('M j, Y', strtotime($category['created_at'])); ?>
                                </small>

                                <div class="category-actions">
                                    <button type="button" class="btn btn-sm btn-outline"
                                            onclick="editCategory(<?php echo (int)$category['id']; ?>, '<?php echo htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($category['slug'], ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($category['description'], ENT_QUOTES, 'UTF-8'); ?>', '<?php echo htmlspecialchars($category['color'], ENT_QUOTES, 'UTF-8'); ?>')">
                                        Edit
                                    </button>

                                    <?php if ($category['post_count'] == 0): ?>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo (int)$category['id']; ?>">
                                <button type="submit" class="btn btn-delete">Delete</button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>            <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Edit Category Modal -->
    <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px;">
            <h3>Edit Category</h3>
            <form method="POST" action="">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="id" id="edit_id">

                <div class="form-group">
                    <label for="edit_name">Category Name *</label>
                    <input type="text" id="edit_name" name="name" required maxlength="100" pattern="[a-zA-Z0-9\s\-_]+" title="Only letters, numbers, spaces, hyphens and underscores allowed">
                </div>

                <div class="form-group">
                    <label for="edit_slug">Slug *</label>
                    <input type="text" id="edit_slug" name="slug" required maxlength="100" pattern="[a-z0-9\-]+" title="Only lowercase letters, numbers and hyphens allowed">
                </div>

                <div class="form-group">
                    <label for="edit_description">Description</label>
                    <textarea id="edit_description" name="description" maxlength="500"></textarea>
                </div>

                <div class="form-group">
                    <label for="edit_color">Color</label>
                    <input type="color" id="edit_color" name="color" class="color-preview">
                </div>

                <div style="margin-top: 20px;">
                    <button type="submit" class="btn btn-primary">Update Category</button>
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <style>
        /* Categories Page Specific Styles */
        .category-form-section {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .form-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        }

        .form-header h2 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .form-header p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .category-form {
            padding: 1.5rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .form-group-wide {
            grid-column: 1 / -1;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group textarea {
            padding: 0.75rem;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-family: inherit;
            font-size: 0.875rem;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-help {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }

        .color-input-group {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .color-picker {
            width: 50px;
            height: 40px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        .color-preview-text {
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            color: var(--gray-600);
            padding: 0.5rem;
            background: var(--gray-100);
            border-radius: var(--border-radius);
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-icon {
            margin-right: 0.5rem;
        }

        .categories-section {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .section-header h2 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .section-header p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-600);
        }
          .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 1.5rem;
        }

        .category-card {
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            border-left: 4px solid var(--primary);
            transition: all 0.2s ease;
        }

        .category-card:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .category-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .category-color-badge {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid var(--white);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .category-title h4 {
            margin: 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .category-stats .post-count {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .category-meta {
            margin-bottom: 1rem;
        }

        .category-slug code {
            background: var(--gray-100);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8125rem;
            color: var(--gray-700);
        }

        .category-description {
            margin-bottom: 1rem;
        }

        .category-description p {
            margin: 0;
            color: var(--gray-600);
            font-size: 0.875rem;
            line-height: 1.5;
        }

        .category-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid var(--gray-100);
        }

        .created-date {
            color: var(--gray-500);
            font-size: 0.75rem;
        }

        .category-actions {
            display: flex;
            gap: 0.5rem;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal h3 {
            margin-bottom: 1.5rem;
            color: var(--gray-900);
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .categories-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .category-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .category-footer {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }
        }

        /* Auto-generate slug from name */
        #name {
            transition: border-color 0.2s ease;
        }

        #slug {
            font-family: 'Courier New', monospace;
        }

        /* Color picker enhancement */
        .color-picker::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        .color-picker::-webkit-color-swatch {
            border: none;
            border-radius: calc(var(--border-radius) - 1px);
        }
    </style>

    <script>
        function editCategory(id, name, slug, description, color) {
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_slug').value = slug;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_color').value = color;
            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // Generate slug from name
        document.getElementById('name').addEventListener('input', function() {
            const slug = this.value.toLowerCase()
                .replace(/[^a-z0-9\s\-]/g, '')
                .replace(/[\s_]+/g, '-')
                .replace(/^-+|-+$/g, '');
            document.getElementById('slug').value = slug;
        });

        // Close modal on outside click
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });

        // Auto-generate slug from name
        document.getElementById('name').addEventListener('input', function() {
            const name = this.value;
            const slug = name
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        });

        // Update color preview text
        document.getElementById('color').addEventListener('input', function() {
            document.querySelector('.color-preview-text').textContent = this.value;
        });
    </script>
</body>
</html>
