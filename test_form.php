<?php
// Simple test form processor to debug the issue
header('Content-Type: application/json');

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Check if this is a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    // Check if we have form data
    if (empty($_POST)) {
        throw new Exception('No form data received');
    }

    // Log the received data for debugging
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'method' => $_SERVER['REQUEST_METHOD'],
        'post_data' => $_POST,
        'files' => $_FILES ?? [],
        'headers' => getallheaders()
    ];

    // Save to log file for debugging
    file_put_contents('form_debug.log', json_encode($logData, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);

    // Simulate processing time
    sleep(1);

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Test form submitted successfully! Check form_debug.log for details.',
        'redirect' => 'application-success.html',
        'data_received' => count($_POST) . ' fields received',
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
