<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Loader Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .blog-post { border: 1px solid #ddd; margin: 20px 0; padding: 20px; }
        .loading { text-align: center; padding: 40px; }
    </style>
</head>
<body>
    <h1>Blog Loader Test</h1>
    
    <h2>API Test</h2>
    <div id="api-test">
        <p>Testing API connection...</p>
    </div>
    
    <h2>Blog Posts</h2>
    <div id="blog-posts" class="loading">Loading blog posts...</div>

    <script>
        // Test API directly
        fetch('/foresensic/api/get-posts.php')
            .then(response => {
                console.log('API Response Status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API Data:', data);
                document.getElementById('api-test').innerHTML = 
                    '<p>✅ API working! Found ' + data.posts.length + ' posts</p>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('API Error:', error);
                document.getElementById('api-test').innerHTML = 
                    '<p>❌ API Error: ' + error.message + '</p>';
            });

        // Test blog loader
        fetch('/foresensic/js/blog-loader.js')
            .then(response => response.text())
            .then(script => {
                // Execute the blog loader script
                eval(script);
                
                // Wait a moment then check if posts loaded
                setTimeout(() => {
                    const postsContainer = document.getElementById('blog-posts');
                    if (postsContainer.children.length > 1) {
                        postsContainer.insertAdjacentHTML('afterbegin', '<p>✅ Blog loader working!</p>');
                    } else {
                        postsContainer.innerHTML = '<p>❌ Blog loader not working - no posts loaded</p>';
                    }
                }, 2000);
            })
            .catch(error => {
                console.error('Blog Loader Error:', error);
                document.getElementById('blog-posts').innerHTML = 
                    '<p>❌ Blog Loader Error: ' + error.message + '</p>';
            });
    </script>
</body>
</html>
