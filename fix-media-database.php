<?php
require_once 'admin/includes/db_config.php';

echo "<h2>🔧 Fixing Media Database Schema</h2>";

try {
    // Check if media_files table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'media_files'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ media_files table missing, creating it...</p>";
        
        // Create media_files table
        $sql = "
        CREATE TABLE `media_files` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `original_name` varchar(255) NOT NULL,
            `file_name` varchar(255) NOT NULL,
            `file_path` varchar(500) NOT NULL,
            `file_size` int(11) NOT NULL,
            `mime_type` varchar(100) NOT NULL,
            `description` text,
            `uploaded_by` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON><PERSON>AR<PERSON>EY (`id`),
            <PERSON><PERSON><PERSON> `idx_uploaded_by` (`uploaded_by`),
            <PERSON><PERSON><PERSON> `idx_mime_type` (`mime_type`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`uploaded_by`) REFERENCES `admin_users`(`id`) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ media_files table created successfully!</p>";
        } else {
            echo "<p>❌ Failed to create media_files table: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p>✅ media_files table already exists</p>";
    }
    
    // Check if blog_posts table has featured_image column
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE 'featured_image'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ featured_image column missing from blog_posts, adding it...</p>";
        
        // Add featured_image column
        $sql = "ALTER TABLE blog_posts ADD COLUMN featured_image varchar(500) DEFAULT NULL AFTER excerpt";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ featured_image column added successfully!</p>";
        } else {
            echo "<p>❌ Failed to add featured_image column: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p>✅ featured_image column already exists in blog_posts</p>";
    }
    
    // Check if blog_posts table has author column
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE 'author'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ author column missing from blog_posts, adding it...</p>";
        
        // Add author column
        $sql = "ALTER TABLE blog_posts ADD COLUMN author varchar(100) DEFAULT 'Admin' AFTER featured_image";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ author column added successfully!</p>";
        } else {
            echo "<p>❌ Failed to add author column: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p>✅ author column already exists in blog_posts</p>";
    }
    
    // Check if blog_posts table has category column
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE 'category'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ category column missing from blog_posts, adding it...</p>";
        
        // Add category column
        $sql = "ALTER TABLE blog_posts ADD COLUMN category varchar(100) DEFAULT 'News' AFTER author";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ category column added successfully!</p>";
        } else {
            echo "<p>❌ Failed to add category column: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p>✅ category column already exists in blog_posts</p>";
    }
    
    // Check if blog_posts table has slug column
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE 'slug'");
    
    if ($result->num_rows == 0) {
        echo "<p>❌ slug column missing from blog_posts, adding it...</p>";
        
        // Add slug column
        $sql = "ALTER TABLE blog_posts ADD COLUMN slug varchar(255) DEFAULT NULL AFTER category";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ slug column added successfully!</p>";
        } else {
            echo "<p>❌ Failed to add slug column: " . $mysqli->error . "</p>";
        }
    } else {
        echo "<p>✅ slug column already exists in blog_posts</p>";
    }
    
    // Create uploads directory if it doesn't exist
    $upload_dir = __DIR__ . '/admin/uploads/';
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p>✅ Created uploads directory: admin/uploads/</p>";
            
            // Create .htaccess for security
            file_put_contents($upload_dir . '.htaccess', "Options -Indexes\nAllowOverride None\n");
            echo "<p>✅ Created .htaccess for security</p>";
            
            // Create index.php to prevent directory listing
            file_put_contents($upload_dir . 'index.php', '<?php header("Location: ../"); exit(); ?>');
            echo "<p>✅ Created index.php for security</p>";
        } else {
            echo "<p>⚠️ Could not create uploads directory - please create manually</p>";
        }
    } else {
        echo "<p>✅ Uploads directory already exists</p>";
    }
    
    // Show current blog_posts table structure
    echo "<h3>Current blog_posts table structure:</h3>";
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Type</th><th style='padding: 8px;'>Null</th><th style='padding: 8px;'>Key</th><th style='padding: 8px;'>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $row['Field'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['Type'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['Null'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['Key'] . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎉 Database schema fixed!</h3>";
    echo "<p><a href='admin/media.php'>Go to Media Library</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

<style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
    h2, h3 { color: #1e40af; }
    p { margin: 10px 0; }
    a { color: #1e40af; text-decoration: none; }
    a:hover { text-decoration: underline; }
    table { width: 100%; }
    th, td { text-align: left; }
</style>
