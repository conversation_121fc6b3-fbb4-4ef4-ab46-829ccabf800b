<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit();
}

$error = '';
$debug_info = '';
$csrf_token = generateCSRFToken();

if ($_POST) {
    $debug_info .= "POST data received. ";
    $debug_info .= "CSRF token from form: " . ($_POST['csrf_token'] ?? 'MISSING') . ". ";
    $debug_info .= "CSRF token from session: " . ($_SESSION['csrf_token'] ?? 'MISSING') . ". ";
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token. Please try again.';
        $debug_info .= "CSRF verification failed. ";
    } else {
        $debug_info .= "CSRF verification passed. ";
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';
        
        $debug_info .= "Attempting authentication for user: " . $username . ". ";
        
        if (authenticate($username, $password)) {
            $debug_info .= "Authentication successful. ";
            header('Location: index.php');
            exit();
        } else {
            $error = 'Invalid username or password.';
            $debug_info .= "Authentication failed. ";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Forensic Recovery Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>Admin Dashboard</h1>
                <p>Forensic Recovery Services</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($debug_info && isset($_GET['debug'])): ?>
                <div class="alert" style="background: #fef3c7; color: #92400e; border: 1px solid #fde68a;">
                    <strong>Debug Info:</strong> <?php echo htmlspecialchars($debug_info); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           placeholder="Enter username">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required
                           placeholder="Enter password">
                </div>
                
                <button type="submit" class="btn btn-primary">Login</button>
            </form>
            
            <div class="login-info" style="margin-top: 20px; padding: 15px; background: #f1f5f9; border-radius: 6px; font-size: 14px;">
                <p><strong>Default Credentials:</strong></p>
                <p>Username: <code>admin</code></p>
                <p>Password: <code>admin123</code></p>
                <p style="margin-top: 10px;"><a href="?debug=1">Enable Debug Mode</a></p>
            </div>
            
            <div class="login-footer">
                <p>&copy; <?php echo date('Y'); ?> Forensic Recovery Services</p>
            </div>
        </div>
    </div>
</body>
</html>
