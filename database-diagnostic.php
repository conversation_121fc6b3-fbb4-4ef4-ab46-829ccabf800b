<?php
/**
 * Database Diagnostic and Repair Tool
 * Checks database structure and fixes inconsistencies
 */

require_once 'admin/includes/db_config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Database Diagnostic Tool</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
        h1, h2, h3 { color: #1e40af; }
        .success { color: #10b981; font-weight: bold; }
        .error { color: #ef4444; font-weight: bold; }
        .warning { color: #f59e0b; font-weight: bold; }
        .info { color: #6366f1; font-weight: bold; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; }
        th { background: #f3f4f6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #e5e7eb; border-radius: 8px; }
        .fix-btn { background: #1e40af; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .fix-btn:hover { background: #1d4ed8; }
    </style>
</head>
<body>";

echo "<h1>🔧 Database Diagnostic Tool</h1>";
echo "<p>Environment: <strong>" . ($environment ?? 'unknown') . "</strong></p>";
echo "<p>Database: <strong>$db_name</strong> on <strong>$db_host</strong></p>";
echo "<p>Timestamp: " . date('Y-m-d H:i:s') . "</p>";

// Test database connection
echo "<div class='section'>";
echo "<h2>📡 Database Connection Test</h2>";
try {
    if ($mysqli->ping()) {
        echo "<p class='success'>✅ Database connection successful</p>";
        echo "<p>Server info: " . $mysqli->server_info . "</p>";
        echo "<p>Client info: " . $mysqli->client_info . "</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Connection error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Check table structure
echo "<div class='section'>";
echo "<h2>📋 Table Structure Analysis</h2>";

$tables_to_check = ['admin_users', 'blog_posts', 'categories', 'site_settings', 'media_files'];

foreach ($tables_to_check as $table) {
    echo "<h3>Table: $table</h3>";
    
    $result = $mysqli->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<p class='success'>✅ Table exists</p>";
        
        // Show table structure
        $result = $mysqli->query("SHOW COLUMNS FROM $table");
        if ($result) {
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td>" . ($row['Extra'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Count records
            $count_result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $count = $count_result->fetch_assoc()['count'];
                echo "<p class='info'>📊 Records: $count</p>";
            }
        }
    } else {
        echo "<p class='error'>❌ Table missing</p>";
        echo "<button class='fix-btn' onclick='createTable(\"$table\")'>Create Table</button>";
    }
}
echo "</div>";

// Check blog_posts specific columns
echo "<div class='section'>";
echo "<h2>🔍 Blog Posts Column Check</h2>";

$required_columns = [
    'featured_image' => 'varchar(500) DEFAULT NULL',
    'author' => 'varchar(100) DEFAULT \'Admin\'',
    'category' => 'varchar(100) DEFAULT \'News\'',
    'slug' => 'varchar(255) DEFAULT NULL',
    'is_featured' => 'tinyint(1) DEFAULT 0'
];

$missing_columns = [];
foreach ($required_columns as $column => $definition) {
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE '$column'");
    if ($result->num_rows == 0) {
        echo "<p class='error'>❌ Missing column: $column</p>";
        $missing_columns[$column] = $definition;
    } else {
        echo "<p class='success'>✅ Column exists: $column</p>";
    }
}

if (!empty($missing_columns)) {
    echo "<button class='fix-btn' onclick='addMissingColumns()'>Add Missing Columns</button>";
}
echo "</div>";

// Check featured images
echo "<div class='section'>";
echo "<h2>🖼️ Featured Image Analysis</h2>";

try {
    $stmt = $pdo->prepare("SELECT id, title, featured_image FROM blog_posts WHERE featured_image IS NOT NULL AND featured_image != ''");
    $stmt->execute();
    $posts_with_images = $stmt->fetchAll();
    
    echo "<p class='info'>📊 Posts with featured images: " . count($posts_with_images) . "</p>";
    
    $missing_files = 0;
    $found_files = 0;
    
    foreach ($posts_with_images as $post) {
        $image_path = $post['featured_image'];
        $full_path = __DIR__ . '/' . $image_path;
        
        if (!file_exists($full_path)) {
            // Try alternative paths
            $alt_paths = [
                __DIR__ . '/admin/' . $image_path,
                __DIR__ . '/admin/uploads/' . basename($image_path),
                __DIR__ . '/uploads/' . basename($image_path)
            ];
            
            $found = false;
            foreach ($alt_paths as $alt_path) {
                if (file_exists($alt_path)) {
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $missing_files++;
                echo "<p class='warning'>⚠️ Missing image for post #{$post['id']}: {$post['title']} - {$image_path}</p>";
            } else {
                $found_files++;
            }
        } else {
            $found_files++;
        }
    }
    
    echo "<p class='success'>✅ Found images: $found_files</p>";
    echo "<p class='error'>❌ Missing images: $missing_files</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking images: " . $e->getMessage() . "</p>";
}
echo "</div>";

// API Test
echo "<div class='section'>";
echo "<h2>🔌 API Endpoint Test</h2>";

$api_endpoints = [
    'get-posts.php' => 'api/get-posts.php',
    'get-contact-info.php' => 'api/get-contact-info.php',
    'blog-data.php' => 'includes/blog-data.php?type=homepage&limit=3'
];

foreach ($api_endpoints as $name => $endpoint) {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $endpoint;
    
    echo "<h4>Testing: $name</h4>";
    echo "<p>URL: <a href='$url' target='_blank'>$url</a></p>";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $json_data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p class='success'>✅ API working - JSON response valid</p>";
            if (isset($json_data['success']) && $json_data['success']) {
                echo "<p class='info'>📊 Success: true</p>";
                if (isset($json_data['posts'])) {
                    echo "<p class='info'>📊 Posts returned: " . count($json_data['posts']) . "</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ API returned success: false</p>";
            }
        } else {
            echo "<p class='error'>❌ Invalid JSON response</p>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    } else {
        echo "<p class='error'>❌ API request failed</p>";
    }
}
echo "</div>";

// Environment-specific recommendations
echo "<div class='section'>";
echo "<h2>💡 Recommendations</h2>";

if ($environment === 'production') {
    echo "<h3>Production Environment</h3>";
    echo "<ul>";
    echo "<li>✅ Using production database credentials</li>";
    echo "<li>🔒 Ensure error reporting is disabled in production</li>";
    echo "<li>🔒 Verify file permissions are secure</li>";
    echo "<li>📊 Monitor API response times</li>";
    echo "</ul>";
} else {
    echo "<h3>Development Environment</h3>";
    echo "<ul>";
    echo "<li>✅ Using development database credentials</li>";
    echo "<li>🔧 Debug mode enabled for troubleshooting</li>";
    echo "<li>📝 Test all features before deploying to production</li>";
    echo "<li>🔄 Run this diagnostic after any database changes</li>";
    echo "</ul>";
}

echo "<h3>General Recommendations</h3>";
echo "<ul>";
echo "<li>🔄 Use direct PHP connection (blog-data.php) instead of API for better consistency</li>";
echo "<li>🖼️ Ensure all featured images are properly uploaded to admin/uploads/</li>";
echo "<li>📝 Always include featured images when creating/editing posts</li>";
echo "<li>🔍 Test both offline and online environments regularly</li>";
echo "</ul>";
echo "</div>";

echo "<script>
function addMissingColumns() {
    if (confirm('Add missing columns to blog_posts table?')) {
        window.location.href = 'fix-media-database.php';
    }
}

function createTable(tableName) {
    if (confirm('Create missing table: ' + tableName + '?')) {
        alert('Please run the appropriate setup script for ' + tableName);
    }
}
</script>";

echo "</body></html>";

// Close database connection
$mysqli->close();
?>
