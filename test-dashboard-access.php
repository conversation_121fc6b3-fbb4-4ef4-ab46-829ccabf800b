<?php
// Test admin dashboard access
session_start();

// Temporarily set session for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_full_name'] = 'Administrator';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h2>🚀 Admin Dashboard Access Test</h2>";
echo "<style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f8fafc; }
    .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .success { color: #059669; }
    .info { color: #1d4ed8; }
    a { color: #1e40af; text-decoration: none; padding: 10px 15px; background: #eff6ff; border-radius: 4px; display: inline-block; margin: 5px; }
    a:hover { background: #dbeafe; }
</style>";

echo "<div class='test-section'>";
echo "<p class='success'>✅ Session variables set for testing</p>";
echo "<p class='info'>👤 Logged in as: " . $_SESSION['admin_username'] . "</p>";
echo "<p class='info'>📧 Email: " . $_SESSION['admin_email'] . "</p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>📋 Admin Pages Test</h3>";
echo "<p>Click the links below to test admin functionality:</p>";
echo "<p><a href='admin/index.php' target='_blank'>📊 Main Dashboard</a></p>";
echo "<p><a href='admin/posts.php' target='_blank'>📝 Manage Posts</a></p>";
echo "<p><a href='admin/add-post.php' target='_blank'>➕ Add New Post</a></p>";
echo "<p><a href='admin/logout.php' target='_blank'>🚪 Logout</a></p>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h3>🔐 Return to Login</h3>";
echo "<p><a href='admin/login.php'>Go back to login page</a></p>";
echo "<p><a href='?clear=1'>Clear test session</a></p>";
echo "</div>";

if (isset($_GET['clear'])) {
    session_unset();
    session_destroy();
    echo "<script>alert('Session cleared!'); window.location='admin/login.php';</script>";
}
?>
