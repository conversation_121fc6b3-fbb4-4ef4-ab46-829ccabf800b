/**
 * Emergency Footer Contact Fix
 * Run this script in browser console to immediately fix "Loading..." text
 * Copy and paste this entire script into browser console and press Enter
 */

(function() {
    console.log('🚨 Emergency Footer Contact Fix - Starting...');
    
    // Demo contact data
    const emergencyContactData = {
        phones: {
            general: '+1 (555) 123-DEMO',
            emergency: '(*************'
        },
        emails: {
            general: '<EMAIL>',
            help: '<EMAIL>',
            emergency: '<EMAIL>'
        },
        addresses: {
            primary: '123 Demo Street, Demo City, DC 12345, United States',
            secondary: '456 Sample Avenue, Example Town, ET 67890, United Kingdom'
        },
        social: {
            facebook: '#',
            twitter: '#',
            linkedin: '#',
            instagram: '#'
        }
    };
    
    // Function to replace loading text
    function replaceLoadingText() {
        let replacements = 0;
        
        // Replace address
        const addressEl = document.getElementById('contact-address-primary');
        if (addressEl && (addressEl.textContent.includes('Loading') || addressEl.textContent.trim() === '')) {
            addressEl.textContent = emergencyContactData.addresses.primary;
            console.log('✅ Address replaced');
            replacements++;
        }
        
        // Replace phone
        const phoneEl = document.getElementById('contact-phone');
        if (phoneEl && (phoneEl.textContent.includes('Loading') || phoneEl.textContent.trim() === '')) {
            const phoneNumber = emergencyContactData.phones.general.replace(/\D/g, '');
            phoneEl.innerHTML = `<a href="tel:${phoneNumber}" style="color: inherit; text-decoration: none;">${emergencyContactData.phones.general}</a>`;
            console.log('✅ Phone replaced');
            replacements++;
        }
        
        // Replace email
        const emailEl = document.getElementById('contact-email');
        if (emailEl && (emailEl.textContent.includes('Loading') || emailEl.textContent.trim() === '')) {
            emailEl.innerHTML = `<a href="mailto:${emergencyContactData.emails.general}" style="color: inherit; text-decoration: none;">${emergencyContactData.emails.general}</a>`;
            console.log('✅ Email replaced');
            replacements++;
        }
        
        // Show secondary address if element exists
        const secondaryAddressEl = document.getElementById('contact-address-secondary');
        const secondaryWrapper = document.getElementById('contact-address-secondary-wrapper');
        if (secondaryAddressEl && secondaryWrapper) {
            secondaryAddressEl.textContent = emergencyContactData.addresses.secondary;
            secondaryWrapper.style.display = 'block';
            console.log('✅ Secondary address shown');
            replacements++;
        }
        
        // Hide social media links (since they're set to #)
        const socialLinks = ['social-facebook', 'social-twitter', 'social-linkedin', 'social-instagram'];
        socialLinks.forEach(id => {
            const link = document.getElementById(id);
            if (link) {
                link.style.display = 'none';
            }
        });
        
        return replacements;
    }
    
    // Function to check footer elements
    function checkFooterElements() {
        const elements = {
            'Footer': document.querySelector('footer'),
            'Contact Info Container': document.getElementById('footer-contact-info'),
            'Address Element': document.getElementById('contact-address-primary'),
            'Phone Element': document.getElementById('contact-phone'),
            'Email Element': document.getElementById('contact-email')
        };
        
        console.log('📋 Footer Elements Check:');
        let foundCount = 0;
        Object.keys(elements).forEach(name => {
            const element = elements[name];
            if (element) {
                foundCount++;
                const content = element.textContent ? element.textContent.substring(0, 30) + '...' : 'No text';
                console.log(`  ✅ ${name}: Found - "${content}"`);
            } else {
                console.log(`  ❌ ${name}: Not found`);
            }
        });
        
        return foundCount;
    }
    
    // Function to try API call
    async function tryApiCall() {
        const apiPaths = [
            'api/get-contact-info.php',
            '../api/get-contact-info.php',
            './api/get-contact-info.php'
        ];
        
        for (const path of apiPaths) {
            try {
                console.log(`🔌 Trying API path: ${path}`);
                const response = await fetch(path);
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        console.log('✅ API call successful, using real data');
                        return data.data;
                    }
                }
            } catch (error) {
                console.log(`❌ API path failed: ${path}`);
            }
        }
        
        console.log('⚠️ All API paths failed, using emergency data');
        return null;
    }
    
    // Main execution
    async function executeEmergencyFix() {
        console.log('🔍 Checking footer elements...');
        const elementsFound = checkFooterElements();
        
        if (elementsFound < 3) {
            console.log('❌ Not enough footer elements found. Footer may not be loaded yet.');
            console.log('💡 Try running this script again in a few seconds.');
            return;
        }
        
        console.log('🔌 Attempting to load real contact data from API...');
        const apiData = await tryApiCall();
        
        if (apiData) {
            // Use real API data
            Object.assign(emergencyContactData, apiData);
            console.log('✅ Using real contact data from API');
        } else {
            console.log('⚠️ Using emergency demo contact data');
        }
        
        console.log('🔄 Replacing loading text...');
        const replacements = replaceLoadingText();
        
        if (replacements > 0) {
            console.log(`✅ Emergency fix complete! ${replacements} elements replaced.`);
            console.log('📞 Footer should now show contact information instead of "Loading..." text.');
            
            // Create a simple contact loader object for compatibility
            if (!window.contactLoader) {
                window.contactLoader = {
                    contactData: emergencyContactData,
                    getContactData: () => emergencyContactData,
                    refresh: () => {
                        console.log('🔄 Refreshing contact data...');
                        executeEmergencyFix();
                    }
                };
                console.log('✅ Emergency contact loader object created');
            }
        } else {
            console.log('ℹ️ No loading text found to replace. Footer may already be working.');
        }
    }
    
    // Execute the fix
    executeEmergencyFix();
    
    // Provide manual functions
    window.emergencyContactFix = executeEmergencyFix;
    window.checkFooter = checkFooterElements;
    
    console.log('🔧 Emergency functions available:');
    console.log('  - emergencyContactFix() - Run the emergency fix again');
    console.log('  - checkFooter() - Check footer elements');
    
})();

console.log('🚨 Emergency Footer Contact Fix loaded!');
console.log('💡 If footer still shows "Loading..." text, run: emergencyContactFix()');
