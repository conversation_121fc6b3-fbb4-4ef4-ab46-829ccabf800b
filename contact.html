<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us - Forensic Involve | Get Expert Help Now</title>
  <meta name="description" content="Contact our fraud recovery experts for immediate assistance. Available 24/7 for emergency cases. Get your free consultation and start your recovery process today.">

  <!-- Favicon -->
  <link rel="icon" href="assets/svg/logo.svg" type="image/svg+xml">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/modern.css?v=2025">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Custom styles for form enhancements -->
  <style>
    /* CAPTCHA and form enhancement styles */
    .captcha-container {
      background: #f8fafc;
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      padding: 1rem;
    }

    .captcha-display {
      background: white;
      border: 2px solid #1e40af;
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      font-family: 'Courier New', monospace;
      font-size: 1.2rem;
      font-weight: bold;
      color: #1e40af;
      letter-spacing: 3px;
      min-width: 120px;
      text-align: center;
      user-select: none;
      position: relative;
    }

    .captcha-refresh-btn {
      background: #1e40af;
      color: white;
      border: none;
      padding: 0.5rem;
      border-radius: 0.5rem;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    .captcha-refresh-btn:hover {
      background: #1d4ed8;
      transform: scale(1.05);
    }

    .acknowledgment-label {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      color: #374151;
      cursor: pointer;
      line-height: 1.5;
    }

    .acknowledgment-checkbox {
      margin-top: 0.25rem;
      width: 18px;
      height: 18px;
      accent-color: #1e40af;
      cursor: pointer;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
      .captcha-display {
        font-size: 1rem;
        letter-spacing: 2px;
        min-width: 100px;
        padding: 0.5rem 0.75rem;
      }

      .captcha-container {
        padding: 0.75rem;
      }

      .acknowledgment-label {
        font-size: 0.9rem;
      }

      .acknowledgment-checkbox {
        width: 16px;
        height: 16px;
      }
    }

    /* Enhanced form validation styles */
    .form-field-error {
      border-color: #dc2626 !important;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    .form-field-success {
      border-color: #10b981 !important;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    /* Quick Links hover effects */
    .quick-link:hover {
      background: #f1f5f9 !important;
      border-color: #cbd5e1 !important;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    /* Additional mobile responsiveness for new sections */
    @media (max-width: 768px) {
      .emergency-contact {
        padding: 1rem !important;
      }

      .testimonial-section {
        padding: 1.5rem !important;
      }

      .col-6 {
        margin-bottom: 2rem;
      }

      .quick-link {
        padding: 0.75rem !important;
      }

      .hero-info-card {
        padding: 1.5rem !important;
      }
    }
  </style>
</head>
<body>
  <!-- Header Placeholder -->
  <div id="header-placeholder"></div>

  <main>
    <!-- Hero Section -->
    <section class="hero-section" style="background: linear-gradient(rgba(30, 64, 175, 0.8), rgba(59, 130, 246, 0.8)), url('images/contact-hero.webp'); background-size: cover; background-position: center; background-attachment: fixed;">
      <div class="container" style="padding-top: 90px;">
        <div class="row">
          <div class="col-6">
            <div class="hero-content">
              <div class="hero-badge">Contact Us</div>
              <h1>Get <span class="highlight">Expert Help</span> Now</h1>
              <p class="hero-description">Don't wait - time is critical in fraud recovery. Our expert team is available 24/7 to provide immediate assistance and start your recovery process. Get your free consultation and take the first step toward getting your funds back.</p>
              <div class="hero-cta">
                <a href="tel:+***********" class="btn btn-primary btn-lg">
                  <i class="fas fa-phone"></i>
                  Call (*************
                </a>
                <a href="#contact-form" class="btn btn-secondary btn-lg">
                  <i class="fas fa-envelope"></i>
                  Send Message
                </a>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="hero-image">
              <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background elements -->
                <circle cx="400" cy="80" r="60" fill="#dbeafe" opacity="0.6"/>
                <circle cx="100" cy="320" r="40" fill="#f0fdf4" opacity="0.8"/>

                <!-- Contact support illustration -->
                <g transform="translate(250, 200)">
                  <!-- Support agent -->
                  <circle cx="0" cy="-20" r="40" fill="#1e40af" opacity="0.9"/>
                  <circle cx="0" cy="-30" r="25" fill="#dbeafe"/>
                  <rect x="-35" y="10" width="70" height="60" fill="#1e40af"/>

                  <!-- Headset -->
                  <path d="M-20 -35 Q-25 -40 -25 -30 Q-25 -20 -20 -25" stroke="#10b981" stroke-width="3" fill="none"/>
                  <path d="M20 -35 Q25 -40 25 -30 Q25 -20 20 -25" stroke="#10b981" stroke-width="3" fill="none"/>
                  <path d="M-20 -30 Q0 -35 20 -30" stroke="#10b981" stroke-width="2" fill="none"/>

                  <!-- 24/7 indicator -->
                  <circle cx="60" cy="-60" r="20" fill="#10b981"/>
                  <text x="60" y="-65" text-anchor="middle" fill="white" font-size="8" font-weight="bold">24/7</text>
                  <text x="60" y="-55" text-anchor="middle" fill="white" font-size="6">SUPPORT</text>
                </g>

                <!-- Communication channels -->
                <g transform="translate(120, 120)">
                  <circle cx="0" cy="0" r="25" fill="#f59e0b" opacity="0.8"/>
                  <path d="M-10 -5 L10 -5 L10 5 L5 5 L0 10 L-5 5 L-10 5 Z" fill="white"/>
                  <text x="0" y="-35" text-anchor="middle" fill="#64748b" font-size="10">Email</text>
                </g>

                <g transform="translate(380, 280)">
                  <circle cx="0" cy="0" r="25" fill="#8b5cf6" opacity="0.8"/>
                  <path d="M-8 -8 Q-8 -12 -4 -12 L4 -12 Q8 -12 8 -8 L8 8 Q8 12 4 12 L-4 12 Q-8 12 -8 8 Z" fill="white"/>
                  <circle cx="0" cy="0" r="3" fill="#8b5cf6"/>
                  <text x="0" y="40" text-anchor="middle" fill="#64748b" font-size="10">Phone</text>
                </g>

                <!-- Response time indicators -->
                <g transform="translate(80, 280)">
                  <circle cx="0" cy="0" r="20" fill="#ef4444" opacity="0.7"/>
                  <circle cx="0" cy="0" r="12" stroke="white" stroke-width="2" fill="none"/>
                  <path d="M0 -8 L0 0 L6 6" stroke="white" stroke-width="2" fill="none"/>
                  <text x="0" y="35" text-anchor="middle" fill="#64748b" font-size="9">Fast Response</text>
                </g>

                <!-- Success indicators -->
                <g transform="translate(420, 120)">
                  <circle cx="0" cy="0" r="15" fill="#10b981" opacity="0.8"/>
                  <path d="M-5 0 L-2 3 L5 -5" stroke="white" stroke-width="2" fill="none"/>
                </g>

                <!-- Decorative elements -->
                <circle cx="30" cy="30" r="3" fill="#1e40af" opacity="0.4"/>
                <circle cx="470" cy="50" r="4" fill="#10b981" opacity="0.5"/>
                <circle cx="20" cy="380" r="3" fill="#8b5cf6" opacity="0.6"/>
                <circle cx="480" cy="370" r="3" fill="#f59e0b" opacity="0.4"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Methods Section -->
    <section class="section" style="background: white;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Multiple Ways to Reach Us</h2>
          <p class="section-subtitle" style="margin: 0;">Choose the contact method that works best for your situation. We're available 24/7 for emergency cases.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 3rem; margin-top: 3rem;">
          <!-- Emergency Hotline -->
          <div style="text-align: center; padding: 2rem; background: #fef2f2; border-radius: 1rem;">
            <div style="width: 80px; height: 80px; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
              <i class="fas fa-phone" style="color: white; font-size: 2rem;"></i>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Emergency Hotline</h3>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">For urgent cases requiring immediate attention</p>
            <a href="tel:+***********" style="color: #ef4444; font-weight: 600; font-size: 1.2rem; text-decoration: none;">(*************</a>
            <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">Available 24/7</div>
          </div>

          <!-- General Inquiries -->
          <div style="text-align: center; padding: 2rem; background: #dbeafe; border-radius: 1rem;">
            <div style="width: 80px; height: 80px; background: #1e40af; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
              <i class="fas fa-envelope" style="color: white; font-size: 2rem;"></i>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Email Support</h3>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">For detailed case information and documentation</p>
            <a href="mailto:<EMAIL>" style="color: #1e40af; font-weight: 600; text-decoration: none;"><EMAIL></a>
            <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">Response within 2 hours</div>
          </div>

          <!-- Live Chat -->
          <div style="text-align: center; padding: 2rem; background: #f0fdf4; border-radius: 1rem;">
            <div style="width: 80px; height: 80px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem;">
              <i class="fas fa-comments" style="color: white; font-size: 2rem;"></i>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Live Chat</h3>
            <p style="color: #6b7280; margin-bottom: 1.5rem;">Instant support for quick questions</p>
            <button style="background: #10b981; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 600; cursor: pointer;">Start Chat</button>
            <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">Available 9 AM - 9 PM EST</div>
          </div>
        </div>

        <!-- Emergency Response Link -->
        <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: #fef2f2; border-radius: 1rem; border: 2px solid #ef4444;">
          <h3 style="color: #ef4444; margin-bottom: 1rem;">Need Emergency Response?</h3>
          <p style="color: #6b7280; margin-bottom: 1.5rem;">If you're experiencing active fraud or need immediate assistance, our emergency response team is available 24/7.</p>
          <a href="emergency-response.html" class="btn btn-lg" style="background: #ef4444; color: white; text-decoration: none; display: inline-block;">
            <i class="fas fa-exclamation-triangle"></i>
            Emergency Response
          </a>
        </div>
      </div>
    </section>

    <!-- Contact Information Section -->
    <section class="section" style="background: #f8fafc;">
      <div class="container">
        <div class="section-header" style="text-align: center;">
          <h2>Our Contact Information</h2>
          <p class="section-subtitle">All the ways to reach our expert team</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
          <!-- Office Locations -->
          <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <h3 style="color: #111827; margin-bottom: 1.5rem; display: flex; align-items: center;">
              <i class="fas fa-map-marker-alt" style="color: #1e40af; margin-right: 0.75rem;"></i>
              Office Locations
            </h3>
            <div style="margin-bottom: 1.5rem;">
              <h4 style="color: #374151; font-size: 1rem; margin-bottom: 0.5rem;">Primary Office</h4>
              <p style="color: #6b7280; margin: 0; line-height: 1.6;">123 Demo Street<br>Demo City, DC 12345<br>United States</p>
            </div>
            <div>
              <h4 style="color: #374151; font-size: 1rem; margin-bottom: 0.5rem;">International Office</h4>
              <p style="color: #6b7280; margin: 0; line-height: 1.6;">456 Sample Avenue<br>Example Town, ET 67890<br>United Kingdom</p>
            </div>
          </div>

          <!-- Phone Numbers -->
          <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <h3 style="color: #111827; margin-bottom: 1.5rem; display: flex; align-items: center;">
              <i class="fas fa-phone" style="color: #1e40af; margin-right: 0.75rem;"></i>
              Phone Support
            </h3>
            <div style="margin-bottom: 1.5rem;">
              <h4 style="color: #374151; font-size: 1rem; margin-bottom: 0.5rem;">General Inquiries</h4>
              <a href="tel:+***********" style="color: #1e40af; text-decoration: none; font-weight: 600; font-size: 1.1rem;">+1 (555) 123-DEMO</a>
              <p style="color: #6b7280; margin: 0.25rem 0 0 0; font-size: 0.875rem;">Business hours: 9 AM - 6 PM EST</p>
            </div>
            <div style="padding: 1rem; background: #fef2f2; border-radius: 0.5rem;">
              <h4 style="color: #ef4444; font-size: 1rem; margin-bottom: 0.5rem;">Emergency Hotline</h4>
              <a href="tel:+***********" style="color: #ef4444; text-decoration: none; font-weight: 700; font-size: 1.2rem;">(*************</a>
              <p style="color: #6b7280; margin: 0.25rem 0 0 0; font-size: 0.875rem;">Available 24/7 for urgent cases</p>
            </div>
          </div>

          <!-- Email Addresses -->
          <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <h3 style="color: #111827; margin-bottom: 1.5rem; display: flex; align-items: center;">
              <i class="fas fa-envelope" style="color: #1e40af; margin-right: 0.75rem;"></i>
              Email Support
            </h3>
            <div style="margin-bottom: 1.5rem;">
              <h4 style="color: #374151; font-size: 1rem; margin-bottom: 0.5rem;">General Inquiries</h4>
              <a href="mailto:<EMAIL>" style="color: #1e40af; text-decoration: none; font-weight: 600;"><EMAIL></a>
              <p style="color: #6b7280; margin: 0.25rem 0 0 0; font-size: 0.875rem;">Response within 2 hours</p>
            </div>
            <div style="padding: 1rem; background: #fef2f2; border-radius: 0.5rem;">
              <h4 style="color: #ef4444; font-size: 1rem; margin-bottom: 0.5rem;">Emergency Email</h4>
              <a href="mailto:<EMAIL>" style="color: #ef4444; text-decoration: none; font-weight: 600;"><EMAIL></a>
              <p style="color: #6b7280; margin: 0.25rem 0 0 0; font-size: 0.875rem;">Priority response for urgent cases</p>
            </div>
          </div>


        </div>
      </div>
    </section>

    <!-- Contact Form Section -->
    <section class="section" id="contact-form" style="background: #f8fafc;">
      <div class="container">
        <div class="row">
          <div class="col-6">
            <div class="section-header" style="text-align: left;">
              <h2>Send Us a Message</h2>
              <p class="section-subtitle" style="margin: 0;">Provide details about your case and we'll get back to you within 2 hours with a preliminary assessment.</p>
            </div>

            <form action="process_contact.php" method="post" id="contactForm" style="margin-top: 2rem;">
              <!-- Success/Error Messages -->
              <div id="form-messages" style="margin-bottom: 1rem; display: none;"></div>

              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                <div>
                  <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">First Name *</label>
                  <input type="text" name="first_name" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                </div>
                <div>
                  <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Last Name *</label>
                  <input type="text" name="last_name" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                </div>
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Email Address *</label>
                <input type="email" name="email" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Phone Number</label>
                <input type="tel" name="phone" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Type of Fraud *</label>
                <select name="fraud_type" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                  <option value="">Select fraud type...</option>
                  <option value="cryptocurrency">Cryptocurrency Scam</option>
                  <option value="romance">Romance Scam</option>
                  <option value="investment">Investment Fraud</option>
                  <option value="phishing">Phishing Attack</option>
                  <option value="identity">Identity Theft</option>
                  <option value="bec">Business Email Compromise</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div style="margin-bottom: 1rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Amount Lost (USD)</label>
                <input type="number" name="amount_lost" placeholder="Enter amount..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
              </div>

              <div style="margin-bottom: 2rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Case Details *</label>
                <textarea name="case_details" required rows="5" placeholder="Please provide details about what happened, when it occurred, and any evidence you have..." style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; resize: vertical;"></textarea>
              </div>

              <!-- CAPTCHA Section -->
              <div style="margin-bottom: 1.5rem;">
                <label style="display: block; color: #374151; font-weight: 600; margin-bottom: 0.5rem;">Security Verification *</label>
                <div class="captcha-container">
                  <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                    <div class="captcha-display" id="captcha-display">
                      <!-- CAPTCHA will be generated here -->
                    </div>
                    <button type="button" onclick="generateCaptcha()" class="captcha-refresh-btn" title="Generate new CAPTCHA">
                      <i class="fas fa-sync-alt"></i>
                    </button>
                  </div>
                  <input type="text" name="captcha_input" id="captcha-input" required placeholder="Enter the characters shown above" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" autocomplete="off">
                  <input type="hidden" name="captcha_code" id="captcha-code">
                  <div style="color: #6b7280; font-size: 0.875rem; margin-top: 0.5rem;">
                    <i class="fas fa-info-circle"></i>
                    Please enter the exact characters shown above (case-sensitive)
                  </div>
                </div>
              </div>

              <!-- Acknowledgment Checkbox -->
              <div style="margin-bottom: 2rem;">
                <label class="acknowledgment-label">
                  <input type="checkbox" name="acknowledgment" id="acknowledgment" required class="acknowledgment-checkbox">
                  <span style="font-size: 0.95rem; line-height: 1.5;">
                    I acknowledge that I have read and agree to the
                    <a href="privacy-terms.html" style="color: #1e40af; text-decoration: underline;" target="_blank">terms of service and privacy policy</a>,
                    and I confirm that the information provided is accurate. I consent to being contacted by Forensic Involve regarding my inquiry.
                  </span>
                </label>
              </div>

              <button type="submit" class="btn btn-primary btn-lg" style="width: 100%;" id="submit-btn">
                <i class="fas fa-paper-plane"></i>
                <span id="submit-text">Send Message</span>
              </button>
            </form>
          </div>

          <div class="col-6">
            <!-- What Happens Next Section -->
            <div style="background: white; padding: 2rem; border-radius: 1rem; margin-top: 2rem;">
              <h3 style="color: #111827; margin-bottom: 1.5rem;">What Happens Next?</h3>

              <div style="display: flex; align-items: flex-start; margin-bottom: 1.5rem;">
                <div style="width: 40px; height: 40px; background: #dbeafe; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem; flex-shrink: 0;">
                  <span style="color: #1e40af; font-weight: bold;">1</span>
                </div>
                <div>
                  <h4 style="color: #111827; margin-bottom: 0.5rem;">Immediate Response</h4>
                  <p style="color: #6b7280; margin: 0; font-size: 0.9rem;">We'll acknowledge your message within 30 minutes and provide initial guidance.</p>
                </div>
              </div>

              <div style="display: flex; align-items: flex-start; margin-bottom: 1.5rem;">
                <div style="width: 40px; height: 40px; background: #f0fdf4; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem; flex-shrink: 0;">
                  <span style="color: #10b981; font-weight: bold;">2</span>
                </div>
                <div>
                  <h4 style="color: #111827; margin-bottom: 0.5rem;">Case Assessment</h4>
                  <p style="color: #6b7280; margin: 0; font-size: 0.9rem;">Our experts will review your case details and provide a preliminary recovery assessment.</p>
                </div>
              </div>

              <div style="display: flex; align-items: flex-start;">
                <div style="width: 40px; height: 40px; background: #fef3c7; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem; flex-shrink: 0;">
                  <span style="color: #f59e0b; font-weight: bold;">3</span>
                </div>
                <div>
                  <h4 style="color: #111827; margin-bottom: 0.5rem;">Action Plan</h4>
                  <p style="color: #6b7280; margin: 0; font-size: 0.9rem;">We'll develop a customized recovery strategy and begin immediate action if you choose to proceed.</p>
                </div>
              </div>
            </div>

            <!-- Quick Links Section -->
            <div style="background: white; padding: 2rem; border-radius: 1rem; margin-top: 2rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
              <h3 style="color: #111827; margin-bottom: 1.5rem;">Quick Links</h3>

              <div style="display: grid; gap: 1rem;">
                <a href="services.html" class="quick-link" style="display: flex; align-items: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem; text-decoration: none; border: 1px solid #e5e7eb; transition: all 0.3s ease;">
                  <div style="width: 40px; height: 40px; background: #dbeafe; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                    <i class="fas fa-shield-alt" style="color: #1e40af;"></i>
                  </div>
                  <div>
                    <div style="font-weight: 600; color: #111827; margin-bottom: 0.25rem;">Our Services</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">View all fraud recovery services</div>
                  </div>
                </a>

                <a href="process.html" class="quick-link" style="display: flex; align-items: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem; text-decoration: none; border: 1px solid #e5e7eb; transition: all 0.3s ease;">
                  <div style="width: 40px; height: 40px; background: #f0fdf4; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                    <i class="fas fa-cogs" style="color: #10b981;"></i>
                  </div>
                  <div>
                    <div style="font-weight: 600; color: #111827; margin-bottom: 0.25rem;">Our Process</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">Learn how we recover funds</div>
                  </div>
                </a>

                <a href="success-stories.html" class="quick-link" style="display: flex; align-items: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem; text-decoration: none; border: 1px solid #e5e7eb; transition: all 0.3s ease;">
                  <div style="width: 40px; height: 40px; background: #fef3c7; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                    <i class="fas fa-trophy" style="color: #f59e0b;"></i>
                  </div>
                  <div>
                    <div style="font-weight: 600; color: #111827; margin-bottom: 0.25rem;">Success Stories</div>
                    <div style="color: #6b7280; font-size: 0.875rem;">Read client recovery cases</div>
                  </div>
                </a>
              </div>
            </div>



            <!-- Emergency Contact Section -->
            <div style="background: #fef2f2; border: 2px solid #ef4444; padding: 1.5rem; border-radius: 1rem; margin-top: 2rem;">
              <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <i class="fas fa-exclamation-triangle" style="color: #ef4444; font-size: 1.5rem; margin-right: 0.75rem;"></i>
                <h4 style="color: #ef4444; margin: 0;">Emergency? Call Now!</h4>
              </div>
              <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.9rem;">If you're experiencing active fraud or need immediate assistance:</p>
              <a href="tel:+***********" style="display: flex; align-items: center; background: #ef4444; color: white; padding: 1rem; border-radius: 0.5rem; text-decoration: none; font-weight: 600;">
                <i class="fas fa-phone" style="margin-right: 0.75rem;"></i>
                (************* - 24/7 Emergency Line
              </a>
            </div>

            <!-- Client Testimonial Section -->
            <div style="background: white; padding: 2rem; border-radius: 1rem; margin-top: 2rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
              <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                <div style="display: flex; color: #f59e0b; margin-right: 0.75rem;">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span style="color: #6b7280; font-size: 0.9rem;">5.0 out of 5</span>
              </div>
              <blockquote style="font-style: italic; color: #374151; margin-bottom: 1rem; line-height: 1.6;">
                "Forensic Involve recovered $45,000 that I thought was gone forever. Their team was professional, responsive, and kept me informed throughout the entire process."
              </blockquote>
              <div style="display: flex; align-items: center;">
                <div style="width: 40px; height: 40px; background: #dbeafe; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 0.75rem;">
                  <i class="fas fa-user" style="color: #1e40af;"></i>
                </div>
                <div>
                  <div style="font-weight: 600; color: #111827;">Sarah M.</div>
                  <div style="color: #6b7280; font-size: 0.875rem;">Cryptocurrency Fraud Victim</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer Placeholder -->
  <div id="footer-placeholder"></div>
  <!-- JavaScript -->
  <script src="js/main.js"></script>

  <script>
    // CAPTCHA functionality
    let currentCaptcha = '';

    function generateCaptcha() {
      const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
      let captcha = '';
      for (let i = 0; i < 6; i++) {
        captcha += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      currentCaptcha = captcha;
      document.getElementById('captcha-display').textContent = captcha;
      document.getElementById('captcha-code').value = captcha;
      document.getElementById('captcha-input').value = '';

      // Add visual feedback for refresh
      const refreshBtn = document.querySelector('button[onclick="generateCaptcha()"]');
      refreshBtn.style.transform = 'rotate(360deg)';
      refreshBtn.style.transition = 'transform 0.5s ease';
      setTimeout(() => {
        refreshBtn.style.transform = 'rotate(0deg)';
      }, 500);
    }

    function validateCaptcha() {
      const userInput = document.getElementById('captcha-input').value;
      const captchaField = document.getElementById('captcha-input');

      if (userInput === currentCaptcha) {
        captchaField.style.borderColor = '#10b981';
        return true;
      } else {
        captchaField.style.borderColor = '#dc2626';
        return false;
      }
    }



    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('contactForm');
      const messagesDiv = document.getElementById('form-messages');
      const submitBtn = document.getElementById('submit-btn');
      const submitText = document.getElementById('submit-text');

      // Generate initial CAPTCHA
      generateCaptcha();

      // CAPTCHA input validation
      document.getElementById('captcha-input').addEventListener('input', validateCaptcha);

      // Check for success/error messages in URL
      const urlParams = new URLSearchParams(window.location.search);
      if (urlParams.get('success') === 'true') {
        showSuccess('Message sent successfully! We will contact you within 2 hours.');
      } else if (urlParams.get('error')) {
        showError(decodeURIComponent(urlParams.get('error')));
      }

      // Form submission handler
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate required fields
        const requiredFields = form.querySelectorAll('[required]');
        let hasErrors = false;

        requiredFields.forEach(field => {
          if (field.type === 'checkbox') {
            if (!field.checked) {
              field.style.outline = '2px solid #dc2626';
              hasErrors = true;
            } else {
              field.style.outline = 'none';
            }
          } else {
            if (!field.value.trim()) {
              field.style.borderColor = '#dc2626';
              hasErrors = true;
            } else {
              field.style.borderColor = '#d1d5db';
            }
          }
        });

        // Validate CAPTCHA
        if (!validateCaptcha()) {
          showError('Please enter the correct security verification code.');
          return;
        }

        // Validate acknowledgment checkbox
        const acknowledgmentCheckbox = document.getElementById('acknowledgment');
        if (!acknowledgmentCheckbox.checked) {
          acknowledgmentCheckbox.style.outline = '2px solid #dc2626';
          showError('Please acknowledge the terms and conditions.');
          return;
        }

        if (hasErrors) {
          showError('Please fill in all required fields correctly.');
          return;
        }

        // Set loading state
        setLoadingState(true);

        // Create FormData object
        const formData = new FormData(form);

        // Submit form via AJAX
        fetch('process_contact.php', {
          method: 'POST',
          body: formData,
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          setLoadingState(false);

          if (data.success) {
            showSuccess(data.message);
            form.reset(); // Clear the form
            generateCaptcha(); // Generate new CAPTCHA after successful submission
          } else {
            showError(data.message || 'An error occurred while sending your message. Please try again.');
            generateCaptcha(); // Generate new CAPTCHA after failed submission for security
          }
        })
        .catch(error => {
          setLoadingState(false);
          console.error('Error:', error);
          showError('A network error occurred. Please check your connection and try again.');
        });
      });

      // Helper functions
      function showSuccess(message) {
        messagesDiv.innerHTML = `
          <div style="background: #d1fae5; border: 1px solid #a7f3d0; color: #065f46; padding: 1rem; border-radius: 0.5rem;">
            <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
            ${message}
          </div>
        `;
        messagesDiv.style.display = 'block';
        messagesDiv.scrollIntoView({ behavior: 'smooth' });
      }

      function showError(message) {
        messagesDiv.innerHTML = `
          <div style="background: #fee2e2; border: 1px solid #fca5a5; color: #dc2626; padding: 1rem; border-radius: 0.5rem;">
            <i class="fas fa-exclamation-circle" style="margin-right: 0.5rem;"></i>
            ${message}
          </div>
        `;
        messagesDiv.style.display = 'block';
        messagesDiv.scrollIntoView({ behavior: 'smooth' });
      }

      function setLoadingState(loading) {
        if (loading) {
          submitBtn.disabled = true;
          submitText.textContent = 'Sending...';
          submitBtn.style.opacity = '0.7';
        } else {
          submitBtn.disabled = false;
          submitText.textContent = 'Send Message';
          submitBtn.style.opacity = '1';
        }
      }

      // Real-time validation for email field
      const emailField = form.querySelector('input[type="email"]');
      if (emailField) {
        emailField.addEventListener('blur', function() {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (this.value && !emailRegex.test(this.value)) {
            this.style.borderColor = '#dc2626';
          } else {
            this.style.borderColor = '#d1d5db';
          }
        });
      }
    });
  </script>
</body>
</html>
