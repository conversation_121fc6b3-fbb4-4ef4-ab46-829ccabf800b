# Forensic Involve - Email Setup Guide

This guide will help you set up the PHP mailer functionality for the Forensic Involve service application form.

## Prerequisites

- PHP 7.4 or higher
- Composer (PHP package manager)
- A web server (Apache, Nginx, or local development server)
- An email account with SMTP access (Gmail recommended)

## Installation Steps

### 1. Install Dependencies

Run the following command in your project directory to install PHPMailer:

```bash
composer install
```

This will create a `vendor` folder with all necessary dependencies.

### 2. Configure Email Settings

Edit the `process_application.php` file and update the configuration section:

```php
$config = [
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',     // Your Gmail address
    'smtp_password' => 'your-app-password',        // Your Gmail app password
    'from_email' => '<EMAIL>',
    'from_name' => 'Forensic Involve Application Form',
    'to_email' => '<EMAIL>',     // Destination email
    'to_name' => 'Forensic Involve Team'
];
```

### 3. Gmail Setup (Recommended)

If using Gmail, follow these steps:

1. **Enable 2-Factor Authentication:**
   - Go to your Google Account settings
   - Navigate to Security → 2-Step Verification
   - Enable 2-factor authentication

2. **Generate App Password:**
   - Go to Security → 2-Step Verification → App passwords
   - Select "Mail" and "Other (custom name)"
   - Enter "Forensic Involve Form" as the name
   - Copy the generated 16-character password
   - Use this password in the `smtp_password` field

3. **Update Configuration:**
   ```php
   'smtp_username' => '<EMAIL>',
   'smtp_password' => 'abcd efgh ijkl mnop',  // 16-character app password
   ```

### 4. Alternative SMTP Providers

#### For Other Email Providers:

**Outlook/Hotmail:**
```php
'smtp_host' => 'smtp-mail.outlook.com',
'smtp_port' => 587,
```

**Yahoo:**
```php
'smtp_host' => 'smtp.mail.yahoo.com',
'smtp_port' => 587,
```

**Custom SMTP:**
```php
'smtp_host' => 'mail.yourdomain.com',
'smtp_port' => 587, // or 465 for SSL
```

### 5. File Permissions

Ensure your web server can write to the project directory:

```bash
chmod 755 process_application.php
chmod 755 application-success.html
```

### 6. Test the Setup

1. Open your website in a browser
2. Navigate to the application form (`apply.html`)
3. Fill out the form with test data
4. Submit the form
5. Check if you receive the email at `<EMAIL>`

## Security Considerations

### Production Setup:

1. **Disable Debug Mode:**
   ```php
   error_reporting(0);
   ini_set('display_errors', 0);
   ```

2. **Use Environment Variables:**
   Create a `.env` file for sensitive data:
   ```
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   TO_EMAIL=<EMAIL>
   ```

3. **Add Rate Limiting:**
   The current setup includes basic rate limiting to prevent spam.

4. **SSL Certificate:**
   Ensure your website uses HTTPS for secure form submission.

## Troubleshooting

### Common Issues:

1. **"SMTP Error: Could not authenticate"**
   - Check your email and app password
   - Ensure 2-factor authentication is enabled
   - Verify the app password is correct

2. **"Connection refused"**
   - Check SMTP host and port settings
   - Verify firewall settings allow outbound SMTP

3. **"Form not submitting"**
   - Check browser console for JavaScript errors
   - Verify `process_application.php` path is correct
   - Ensure PHP is running on your server

4. **"500 Internal Server Error"**
   - Check PHP error logs
   - Verify file permissions
   - Ensure all required PHP extensions are installed

### Debug Mode:

To enable debug mode for troubleshooting, edit `process_application.php`:

```php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Add SMTP debug output
$mail->SMTPDebug = 2; // Add this line before $mail->send()
```

## File Structure

```
project/
├── apply.html                    # Application form
├── process_application.php       # Form processor
├── application-success.html      # Success page
├── composer.json                 # Dependencies
├── config.php                    # Configuration file
├── vendor/                       # PHPMailer dependencies
└── README-EMAIL-SETUP.md        # This file
```

## Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify all configuration settings
3. Test with a simple email first
4. Check server logs for detailed error messages

## Email Template Features

The email sent includes:

- ✅ Professional HTML formatting
- ✅ All form data organized in sections
- ✅ Urgency level highlighting
- ✅ Client contact information for easy reply
- ✅ Timestamp of submission
- ✅ Mobile-friendly design

The system automatically:
- Validates all required fields
- Sanitizes input data
- Formats financial amounts
- Handles checkbox arrays
- Sets appropriate reply-to headers
- Provides user feedback

## Next Steps

After setup:
1. Test thoroughly with different form data
2. Monitor email delivery
3. Set up email filters/folders for organization
4. Consider adding email notifications for urgent cases
5. Implement backup email addresses if needed
