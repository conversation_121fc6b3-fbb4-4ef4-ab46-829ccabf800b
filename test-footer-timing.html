<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Timing Test - Forensic Involve</title>
    <link rel="stylesheet" href="css/modern.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-weight: bold;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #10b981; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #ef4444; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #f59e0b; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #3b82f6; }
        .debug {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .btn {
            background: #1e40af;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .btn:hover { background: #1d4ed8; }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .timeline {
            background: #f8fafc;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .timeline-item {
            margin: 0.5rem 0;
            padding: 0.25rem 0;
        }
        .timestamp {
            color: #6b7280;
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔧 Footer Timing Test</h1>
            <p>Testing footer loading and contact replacement timing</p>
        </div>

        <div class="test-section">
            <h2>📋 Test Status</h2>
            <div id="overall-status" class="status info">Initializing tests...</div>
        </div>

        <div class="test-section">
            <h2>⏱️ Loading Timeline</h2>
            <div id="timeline" class="timeline">
                <div class="timeline-item">
                    <span class="timestamp">[00:00.000]</span> Page started loading...
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 Footer Elements Check</h2>
            <button onclick="checkFooterElements()" class="btn">Check Footer Elements</button>
            <div id="footer-check-results"></div>
        </div>

        <div class="test-section">
            <h2>🔄 Manual Contact Loader Test</h2>
            <button onclick="manualContactTest()" class="btn">Test Contact Loader</button>
            <div id="manual-test-results"></div>
        </div>

        <div class="test-section">
            <h2>📞 Current Footer Contact Info</h2>
            <div id="current-contact-info">
                <p><strong>Address:</strong> <span id="display-address">Not loaded</span></p>
                <p><strong>Phone:</strong> <span id="display-phone">Not loaded</span></p>
                <p><strong>Email:</strong> <span id="display-email">Not loaded</span></p>
            </div>
        </div>
    </div>

    <!-- Footer placeholder -->
    <div id="footer-placeholder"></div>

    <script src="js/main.js"></script>
    <script src="js/contact-loader.js"></script>
    <script>
        const startTime = Date.now();
        let timeline = [];

        function addToTimeline(message) {
            const elapsed = Date.now() - startTime;
            const timestamp = `[${(elapsed / 1000).toFixed(3).padStart(7, '0')}]`;
            timeline.push({ timestamp, message });
            
            const timelineDiv = document.getElementById('timeline');
            const item = document.createElement('div');
            item.className = 'timeline-item';
            item.innerHTML = `<span class="timestamp">${timestamp}</span> ${message}`;
            timelineDiv.appendChild(item);
            
            console.log(timestamp, message);
        }

        function updateOverallStatus(message, type = 'info') {
            const statusDiv = document.getElementById('overall-status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function checkFooterElements() {
            const resultsDiv = document.getElementById('footer-check-results');
            
            const elements = {
                'Footer': document.querySelector('footer'),
                'Contact Info Container': document.getElementById('footer-contact-info'),
                'Address Element': document.getElementById('contact-address-primary'),
                'Phone Element': document.getElementById('contact-phone'),
                'Email Element': document.getElementById('contact-email'),
                'Secondary Address Wrapper': document.getElementById('contact-address-secondary-wrapper'),
                'Social Facebook': document.getElementById('social-facebook'),
                'Social Twitter': document.getElementById('social-twitter'),
                'Social LinkedIn': document.getElementById('social-linkedin'),
                'Social Instagram': document.getElementById('social-instagram')
            };

            let results = [];
            let foundCount = 0;

            Object.keys(elements).forEach(name => {
                const element = elements[name];
                if (element) {
                    foundCount++;
                    const content = element.textContent ? element.textContent.substring(0, 50) : 'No text content';
                    results.push(`✅ ${name}: Found - "${content}"`);
                } else {
                    results.push(`❌ ${name}: Not found`);
                }
            });

            const status = foundCount >= 5 ? 'success' : foundCount >= 3 ? 'warning' : 'error';
            resultsDiv.innerHTML = `
                <div class="status ${status}">Found ${foundCount}/${Object.keys(elements).length} footer elements</div>
                <div class="debug">${results.join('\n')}</div>
            `;

            addToTimeline(`Footer elements check: ${foundCount}/${Object.keys(elements).length} found`);
        }

        async function manualContactTest() {
            const resultsDiv = document.getElementById('manual-test-results');
            resultsDiv.innerHTML = '<div class="status info">Testing contact loader...</div>';

            try {
                addToTimeline('Manual contact loader test started');

                // Check if ContactLoader is available
                if (!window.ContactLoader) {
                    throw new Error('ContactLoader class not available');
                }

                // Create new instance
                const testLoader = new window.ContactLoader();
                addToTimeline('ContactLoader instance created');

                // Wait a moment for it to load
                setTimeout(() => {
                    const contactData = testLoader.getContactData();
                    if (contactData) {
                        resultsDiv.innerHTML = `
                            <div class="status success">✅ Contact loader working!</div>
                            <div class="debug">Contact Data:
${JSON.stringify(contactData, null, 2)}</div>
                        `;
                        addToTimeline('Contact data loaded successfully');
                        updateCurrentContactInfo();
                    } else {
                        resultsDiv.innerHTML = '<div class="status warning">⚠️ Contact loader created but no data loaded</div>';
                        addToTimeline('Contact loader created but no data loaded');
                    }
                }, 2000);

            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ Error: ${error.message}</div>`;
                addToTimeline(`Contact loader test failed: ${error.message}`);
            }
        }

        function updateCurrentContactInfo() {
            const addressEl = document.getElementById('contact-address-primary');
            const phoneEl = document.getElementById('contact-phone');
            const emailEl = document.getElementById('contact-email');

            document.getElementById('display-address').textContent = addressEl ? addressEl.textContent : 'Element not found';
            document.getElementById('display-phone').textContent = phoneEl ? phoneEl.textContent : 'Element not found';
            document.getElementById('display-email').textContent = emailEl ? emailEl.textContent : 'Element not found';
        }

        // Monitor events
        document.addEventListener('DOMContentLoaded', function() {
            addToTimeline('DOM Content Loaded');
            updateOverallStatus('DOM loaded, waiting for footer...', 'info');
        });

        window.addEventListener('footerLoaded', function() {
            addToTimeline('Footer loaded event received');
            updateOverallStatus('Footer loaded, contact loader should initialize...', 'info');
            
            setTimeout(() => {
                checkFooterElements();
                updateCurrentContactInfo();
                
                if (window.contactLoader) {
                    updateOverallStatus('✅ Contact loader initialized successfully!', 'success');
                    addToTimeline('Contact loader confirmed initialized');
                } else {
                    updateOverallStatus('⚠️ Footer loaded but contact loader not initialized', 'warning');
                    addToTimeline('Contact loader not found after footer load');
                }
            }, 1000);
        });

        // Monitor contact loader initialization
        const checkContactLoader = setInterval(() => {
            if (window.contactLoader) {
                addToTimeline('Contact loader detected in global scope');
                clearInterval(checkContactLoader);
                
                setTimeout(() => {
                    updateCurrentContactInfo();
                    const addressEl = document.getElementById('contact-address-primary');
                    if (addressEl && !addressEl.textContent.includes('Loading')) {
                        updateOverallStatus('✅ All systems working! Contact info loaded.', 'success');
                        addToTimeline('Contact information successfully replaced');
                    }
                }, 1000);
            }
        }, 500);

        // Auto-check footer elements after 3 seconds
        setTimeout(() => {
            checkFooterElements();
            updateCurrentContactInfo();
        }, 3000);

        // Final status check after 10 seconds
        setTimeout(() => {
            const addressEl = document.getElementById('contact-address-primary');
            if (addressEl) {
                if (addressEl.textContent.includes('Loading')) {
                    updateOverallStatus('❌ Footer loaded but contact info still shows "Loading..."', 'error');
                    addToTimeline('ISSUE: Contact info still showing loading text');
                } else {
                    updateOverallStatus('✅ Everything working correctly!', 'success');
                    addToTimeline('SUCCESS: Contact info properly loaded');
                }
            } else {
                updateOverallStatus('❌ Footer elements not found', 'error');
                addToTimeline('ISSUE: Footer elements not found');
            }
        }, 10000);
    </script>
</body>
</html>
