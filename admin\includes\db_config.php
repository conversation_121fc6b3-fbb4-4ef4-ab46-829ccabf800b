<?php
/**
 * Environment-Based Database Configuration for Admin Panel
 * Supports both offline (localhost) and online (production) environments
 */

// Detect environment based on server name or domain
function detectEnvironment() {
    $server_name = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';

    // Check for development indicators first
    if (strpos($server_name, 'localhost') !== false ||
        strpos($server_name, '127.0.0.1') !== false ||
        strpos($server_name, '192.168.') !== false ||
        strpos($server_name, '10.0.') !== false ||
        strpos($server_name, '.local') !== false ||
        strpos($server_name, ':8080') !== false ||
        strpos($server_name, ':3000') !== false ||
        strpos($server_name, 'dev.') !== false ||
        strpos($server_name, 'test.') !== false ||
        strpos($server_name, 'staging.') !== false) {
        return 'development';
    }

    // If not development, assume production
    return 'production';
}

$environment = detectEnvironment();

// Database configuration based on environment
if ($environment === 'production') {
    // Online/Production Database Configuration
    $db_host = 'localhost';
    $db_username = 'u659553769_news';
    $db_password = 'Money2025@Demo#';
    $db_name = 'u659553769_news'; // Use the same name as username for hosting compatibility
} else {
    // Offline/Development Database Configuration
    $db_host = 'localhost';
    $db_username = 'root';
    $db_password = 'root';
    $db_name = 'forensics_involve';
}

// Create database connection using mysqli
$mysqli = new mysqli($db_host, $db_username, $db_password);

// Check connection
if ($mysqli->connect_error) {
    // Log error details for debugging
    error_log("Database connection failed - Environment: $environment, Host: $db_host, User: $db_username, Error: " . $mysqli->connect_error);

    // Return JSON error for API calls
    if (strpos($_SERVER['REQUEST_URI'] ?? '', '/api/') !== false) {
        header('Content-Type: application/json');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Database connection failed',
            'environment' => $environment,
            'debug' => $environment === 'development' ? $mysqli->connect_error : 'Connection error'
        ]);
        exit;
    }

    die("Database connection failed. Environment: $environment. " . ($environment === 'development' ? $mysqli->connect_error : 'Please check configuration.'));
}

// Create database if it doesn't exist
$mysqli->query("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

// Select the database
$mysqli->select_db($db_name);

// Set charset
$mysqli->set_charset('utf8mb4');

// Create tables if they don't exist
$tables = [
    // Admin users table
    "CREATE TABLE IF NOT EXISTS `admin_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `last_login` datetime DEFAULT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",

    // Blog posts table with all required columns
    "CREATE TABLE IF NOT EXISTS `blog_posts` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `slug` varchar(255) DEFAULT NULL,
        `content` longtext NOT NULL,
        `excerpt` text,
        `featured_image` varchar(500) DEFAULT NULL,
        `author` varchar(100) DEFAULT 'Admin',
        `category` varchar(100) DEFAULT 'News',
        `category_id` int(11) DEFAULT NULL,
        `status` enum('draft','published','archived') DEFAULT 'draft',
        `is_featured` tinyint(1) DEFAULT 0,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`),
        KEY `category_id` (`category_id`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`),
        KEY `is_featured` (`is_featured`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",

    // Categories table
    "CREATE TABLE IF NOT EXISTS `categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL UNIQUE,
        `slug` varchar(100) NOT NULL UNIQUE,
        `description` text,
        `color` varchar(7) DEFAULT '#1e40af',
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
];

foreach ($tables as $table) {
    if (!$mysqli->query($table)) {
        die("Error creating table: " . $mysqli->error);
    }
}

// Database migration: Add missing columns to existing blog_posts table
$migrations = [
    'featured_image' => "ALTER TABLE blog_posts ADD COLUMN featured_image varchar(500) DEFAULT NULL AFTER excerpt",
    'author' => "ALTER TABLE blog_posts ADD COLUMN author varchar(100) DEFAULT 'Admin' AFTER featured_image",
    'category' => "ALTER TABLE blog_posts ADD COLUMN category varchar(100) DEFAULT 'News' AFTER author",
    'slug' => "ALTER TABLE blog_posts ADD COLUMN slug varchar(255) DEFAULT NULL AFTER title",
    'is_featured' => "ALTER TABLE blog_posts ADD COLUMN is_featured tinyint(1) DEFAULT 0 AFTER status"
];

foreach ($migrations as $column => $sql) {
    $result = $mysqli->query("SHOW COLUMNS FROM blog_posts LIKE '$column'");
    if ($result->num_rows == 0) {
        $mysqli->query($sql);
        // Add index for slug if it was just created
        if ($column === 'slug') {
            $mysqli->query("ALTER TABLE blog_posts ADD UNIQUE KEY slug (slug)");
        }
        // Add index for is_featured if it was just created
        if ($column === 'is_featured') {
            $mysqli->query("ALTER TABLE blog_posts ADD KEY is_featured (is_featured)");
        }
    }
}

// Insert default admin user if not exists
$stmt = $mysqli->prepare("SELECT COUNT(*) FROM admin_users WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$admin_username = 'admin';
$stmt->execute();
$result = $stmt->get_result();
$count = $result->fetch_row()[0];

if ($count == 0) {
    $stmt = $mysqli->prepare("INSERT INTO admin_users (username, password, full_name, email) VALUES (?, ?, ?, ?)");
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $full_name = 'Administrator';
    $email = '<EMAIL>';
    $stmt->bind_param("ssss", $admin_username, $password_hash, $full_name, $email);
    $stmt->execute();
}

// Insert default categories if not exist
$categories = [
    ['Cryptocurrency', 'cryptocurrency', 'Articles about cryptocurrency fraud and recovery', '#1e40af'],
    ['Fraud Prevention', 'fraud-prevention', 'Tips and guides for preventing fraud', '#10b981'],
    ['Recovery Tips', 'recovery-tips', 'Advice for fraud victims', '#f59e0b'],
    ['Case Studies', 'case-studies', 'Real case studies and success stories', '#8b5cf6'],
    ['Industry News', 'industry-news', 'Latest news in forensic investigation', '#ef4444'],
    ['Technology', 'technology', 'Technology updates and digital forensics', '#6366f1']
];

foreach ($categories as $cat) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
    $stmt->bind_param("s", $cat[1]);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_row()[0];

    if ($count == 0) {
        $stmt = $mysqli->prepare("INSERT INTO categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $cat[0], $cat[1], $cat[2], $cat[3]);
        $stmt->execute();
    }
}

// Create a simple PDO-like wrapper for compatibility
class SimplePDO {
    private $mysqli;

    public function __construct($mysqli) {
        $this->mysqli = $mysqli;
    }

    public function prepare($sql) {
        $stmt = $this->mysqli->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $this->mysqli->error);
        }
        return new SimpleStatement($stmt);
    }

    public function query($sql) {
        $result = $this->mysqli->query($sql);
        if ($result === false) {
            throw new Exception("Query failed: " . $this->mysqli->error);
        }
        return new SimpleResult($result, $this->mysqli);
    }

    public function exec($sql) {
        return $this->mysqli->query($sql);
    }

    public function lastInsertId() {
        return $this->mysqli->insert_id;
    }
}

class SimpleStatement {
    private $stmt;

    public function __construct($stmt) {
        $this->stmt = $stmt;
    }

    public function execute($params = []) {
        if (!empty($params)) {
            $types = str_repeat('s', count($params));
            $this->stmt->bind_param($types, ...$params);
        }
        return $this->stmt->execute();
    }

    public function fetch($mode = null) {
        $result = $this->stmt->get_result();
        return $result ? $result->fetch_assoc() : false;
    }

    public function fetchAll($mode = null) {
        $result = $this->stmt->get_result();
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }

    public function rowCount() {
        return $this->stmt->affected_rows;
    }

    public function fetchColumn() {
        $result = $this->stmt->get_result();
        if ($result) {
            $row = $result->fetch_row();
            return $row ? $row[0] : false;
        }
        return false;
    }
}

class SimpleResult {
    private $result;
    private $mysqli;

    public function __construct($result, $mysqli) {
        $this->result = $result;
        $this->mysqli = $mysqli;
    }

    public function fetch($mode = null) {
        if (is_bool($this->result)) {
            return false;
        }
        return $this->result->fetch_assoc();
    }

    public function fetchAll($mode = null) {
        if (is_bool($this->result)) {
            return [];
        }
        return $this->result->fetch_all(MYSQLI_ASSOC);
    }

    public function fetchColumn() {
        $row = $this->fetch();
        return $row ? reset($row) : false;
    }

    public function rowCount() {
        return is_bool($this->result) ? $this->mysqli->affected_rows : $this->result->num_rows;
    }
}

// Create the PDO-like object
$pdo = new SimplePDO($mysqli);
?>
