/*
 * Foresenic Invole - Modern Clean Design
 * Inspired by clean, modern financial websites
 */

/* ===== CSS RESET & BASE ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Manrope', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #1f2937;
  background-color: #ffffff;
  overflow-x: hidden;
  font-size: 15px;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
  font-weight: 800;
}

h2 {
  font-size: 1.8rem;
  font-weight: 700;
}

h3 {
  font-size: 1.2rem;
  font-weight: 600;
}

h4 {
  font-size: 1rem;
  font-weight: 600;
}

p {
  margin-bottom: 1rem;
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.6;
}

/* ===== LAYOUT UTILITIES ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.row {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.col-6 {
  flex: 1;
}

.text-center {
  text-align: center;
}

/* ===== HEADER ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e5e7eb;
  z-index: 1000;
  padding: 1rem 0;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-image {
  height: 50px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e40af;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  text-decoration: none;
  color: #4b5563;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #1e40af;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  right: 0;
  height: 2px;
  background: #1e40af;
  border-radius: 1px;
}

/* ===== HERO SECTION ===== */
.hero-section {
  padding: 90px 0;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  position: relative;
  overflow: hidden;
  color: white;
  height: 600px;
  display: flex;
  align-items: center;
  animation: heroGlow 3s ease-in-out infinite alternate;
}

/* ===== HERO SLIDER ===== */
.hero-slider {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-slide {
  display: none;
  width: 100%;
  height: 100%;
  align-items: center;
}

.hero-slide.active {
  display: flex;
}

.hero-nav-dots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.hero-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.hero-dot.active {
  background: white;
}

@keyframes heroGlow {
  0% {
    box-shadow: inset 0 0 50px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: inset 0 0 100px rgba(255, 255, 255, 0.2);
  }
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.15"><circle cx="20" cy="20" r="2"/><circle cx="10" cy="10" r="1"/><circle cx="30" cy="10" r="1"/><circle cx="10" cy="30" r="1"/><circle cx="30" cy="30" r="1"/></g></svg>') repeat;
  opacity: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.hero-title {
  margin-bottom: 1.5rem;
  color: white;
}

.hero-title .highlight {
  color: #fbbf24;
}

.hero-description {
  font-size: 1rem;
  margin-bottom: 2rem;
  max-width: 500px;
  color: rgba(255, 255, 255, 0.9);
}

.hero-cta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.hero-image {
  position: relative;
  z-index: 2;
}

.hero-image svg {
  width: 100%;
  height: auto;
  max-width: 500px;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  gap: 0.5rem;
}

.btn-primary {
  background: #1e40af;
  color: white;
}

.btn-primary:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #1e40af;
  transform: translateY(-2px);
}

.btn-lg {
  padding: 0.875rem 2rem;
  font-size: 1rem;
}

/* ===== STATS SECTION ===== */
.stats-section {
  padding: 4rem 0;
  background: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.2rem;
  font-weight: 800;
  color: #1e40af;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

/* ===== SECTION STYLES ===== */
.section {
  padding: 6rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  margin-bottom: 1rem;
  color: #111827;
}

.section-subtitle {
  font-size: 1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* ===== CARDS ===== */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: #dbeafe;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: #1e40af;
  font-size: 1.5rem;
}

.card-title {
  margin-bottom: 1rem;
  color: #111827;
}

.card-description {
  margin-bottom: 1.5rem;
}

/* ===== RECOVERY PROCESS ===== */
.recovery-process-section {
  padding: 5rem 0;
  background: #f8fafc;
}

.recovery-process-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.process-step {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  text-align: center;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.process-step:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.process-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #1e40af, #3b82f6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin: 0 auto 1rem auto;
}

.process-step h3 {
  color: #1e40af;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.process-step p {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.6;
}

/* ===== TESTIMONIALS ===== */
.testimonials-section {
  padding: 90px 0;
  background: #f0f8ff;
  min-height: 450px;
}

.testimonial-slider {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  overflow: hidden;
}

.testimonial {
  display: none;
  text-align: center;
  padding: 2rem;
  animation: fadeIn 0.5s ease-in-out;
}

.testimonial.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.testimonial-content {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #374151;
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.testimonial-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.testimonial-info h4 {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.testimonial-info p {
  color: #6b7280;
  margin: 0;
  font-size: 0.875rem;
}

.testimonial-stars {
  display: flex;
  justify-content: center;
  gap: 0.25rem;
  margin-bottom: 1.5rem;
}

.testimonial-stars .star {
  color: #fbbf24;
  font-size: 1.2rem;
}

.testimonial-nav {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.testimonial-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #d1d5db;
  cursor: pointer;
  transition: background 0.3s ease;
}

.testimonial-dot.active {
  background: #1e40af;
}

/* ===== FAQ SECTION ===== */
.faq-section {
  background: #f8fafc;
  padding: 4rem 0;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.faq-item {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.faq-question {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 0.75rem;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.faq-question::after {
  content: '+';
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.faq-item.active .faq-question::after {
  transform: rotate(45deg);
}

.faq-answer {
  color: #6b7280;
  line-height: 1.6;
  font-size: 0.9rem;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
  max-height: 200px;
}

/* ===== FOOTER ===== */
.footer {
  background: #111827;
  color: #d1d5db;
  padding-top: 100px;
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
  font-size: 15px;
  margin-top: 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 3rem;
  margin-bottom: 3rem;
  align-items: start;
}

.footer-section h4 {
  color: #ffffff;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.footer-section p {
  font-size: 15px;
  line-height: 1.7;
  margin-bottom: 1.5rem;
  color: #cbd5e1;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 15px;
}

.footer-links a:hover {
  color: #60a5fa;
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 8px;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.social-links a:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: #60a5fa;
  color: #60a5fa;
  transform: translateY(-2px);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  font-size: 15px;
  line-height: 1.6;
}

.contact-item i {
  color: #60a5fa;
  margin-top: 0.2rem;
  font-size: 1rem;
  min-width: 16px;
}

.contact-link {
  color: #cbd5e1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: #60a5fa;
}

.emergency-phone {
  color: #fbbf24 !important;
  font-weight: 600;
}

.emergency-phone:hover {
  color: #f59e0b !important;
}

.emergency-label {
  display: block;
  font-size: 12px;
  color: #fbbf24;
  margin-top: 2px;
  font-weight: 500;
}

.business-hours {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid #374151;
}

.business-hours span {
  color: #10b981;
  font-weight: 500;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding: 2rem 0;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
}

.footer-bottom p {
  margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .row {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-section {
    padding: 6rem 0 4rem 0;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: flex-start;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .nav-menu {
    display: none;
  }

  /* Mobile logo adjustments */
  .logo-image {
    height: 42px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .card-grid {
    grid-template-columns: 1fr;
  }

  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .footer {
    padding-top: 100px;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    margin-top: 0;
  }

  .social-links {
    justify-content: flex-start;
  }
}

@media (max-width: 600px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer {
    padding-top: 100px;
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
    margin-top: 0;
  }

  .footer-section h4 {
    font-size: 1rem;
  }

  .contact-item {
    flex-direction: column;
    gap: 0.5rem;
  }

  .contact-item i {
    margin-top: 0;
  }

  .emergency-label {
    margin-left: 0;
    margin-top: 4px;
  }

  .business-hours {
    margin-top: 1rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  /* CTA heading responsive sizing */
  .section h2[style*="font-size: 3rem"] {
    font-size: 2rem !important;
  }
}

@media (max-width: 768px) {
  /* CTA heading responsive sizing for tablets */
  .section h2[style*="font-size: 3rem"] {
    font-size: 2.5rem !important;
  }
}