<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Contact Test - Forensic Involve</title>
    <link rel="stylesheet" href="css/modern.css">
    <style>
        .test-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-result {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-weight: bold;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #10b981; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #ef4444; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #f59e0b; }
        .loading { background: #e0f2fe; color: #0c4a6e; border: 1px solid #0ea5e9; }
        .debug-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .btn {
            background: #1e40af;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover { background: #1d4ed8; }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🔧 Footer Contact Test</h1>
            <p>Testing dynamic footer contact information controlled from admin panel</p>
        </div>

        <div class="test-section">
            <h2>🔌 Contact API Test</h2>
            <div id="api-status" class="test-result loading">Testing contact API...</div>
            <div id="api-details"></div>
        </div>

        <div class="test-section">
            <h2>📋 Contact Data Preview</h2>
            <div id="contact-preview" class="test-result loading">Loading contact data...</div>
        </div>

        <div class="test-section">
            <h2>🔄 Footer Replacement Test</h2>
            <button onclick="testFooterReplacement()" class="btn">Test Footer Replacement</button>
            <div id="replacement-status" class="test-result loading" style="display: none;">Testing footer replacement...</div>
            <div id="replacement-details"></div>
        </div>

        <div class="test-section">
            <h2>⚙️ Setup & Admin Links</h2>
            <a href="setup-footer-contact.php" class="btn">Setup Footer Contact System</a>
            <a href="admin/contact-settings.php" class="btn">Edit Contact Settings</a>
            <a href="admin/login.php" class="btn">Admin Login</a>
            <a href="api/get-contact-info.php" target="_blank" class="btn">View Raw API Data</a>
        </div>
    </div>

    <!-- Load the footer dynamically -->
    <div id="footer-container"></div>

    <script src="js/main.js"></script>
    <script src="js/contact-loader.js"></script>
    <script>
        let contactData = null;

        // Test contact API
        async function testContactAPI() {
            const apiStatus = document.getElementById('api-status');
            const apiDetails = document.getElementById('api-details');
            
            try {
                const response = await fetch('api/get-contact-info.php');
                const data = await response.json();
                
                if (data.success) {
                    apiStatus.className = 'test-result success';
                    apiStatus.innerHTML = '✅ Contact API working successfully';
                    
                    contactData = data.data;
                    
                    apiDetails.innerHTML = `
                        <div class="debug-info">
API Response:
- Environment: ${data.debug?.environment || 'unknown'}
- Database: ${data.debug?.database || 'unknown'}
- Settings Table Exists: ${data.debug?.site_settings_table_exists || 'unknown'}
- Contact Settings Count: ${data.debug?.contact_settings_count || 0}

Contact Data:
- Primary Phone: ${data.data.phones?.general || 'Not set'}
- Emergency Phone: ${data.data.phones?.emergency || 'Not set'}
- General Email: ${data.data.emails?.general || 'Not set'}
- Primary Address: ${data.data.addresses?.primary || 'Not set'}
- Secondary Address: ${data.data.addresses?.secondary || 'Not set'}
                        </div>
                    `;
                    
                    displayContactPreview(data.data);
                } else {
                    apiStatus.className = 'test-result error';
                    apiStatus.innerHTML = '❌ Contact API failed';
                    apiDetails.innerHTML = `<div class="debug-info">Error: ${data.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                apiStatus.className = 'test-result error';
                apiStatus.innerHTML = '❌ Contact API request failed';
                apiDetails.innerHTML = `<div class="debug-info">Network Error: ${error.message}</div>`;
            }
        }

        function displayContactPreview(data) {
            const preview = document.getElementById('contact-preview');
            preview.className = 'test-result success';
            preview.innerHTML = '✅ Contact data loaded successfully';
            
            const previewHtml = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 4px;">
                        <h4>📞 Phone Numbers</h4>
                        <p><strong>General:</strong> ${data.phones?.general || 'Not set'}</p>
                        <p><strong>Emergency:</strong> ${data.phones?.emergency || 'Not set'}</p>
                    </div>
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 4px;">
                        <h4>📧 Email Addresses</h4>
                        <p><strong>General:</strong> ${data.emails?.general || 'Not set'}</p>
                        <p><strong>Help:</strong> ${data.emails?.help || 'Not set'}</p>
                        <p><strong>Emergency:</strong> ${data.emails?.emergency || 'Not set'}</p>
                    </div>
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 4px;">
                        <h4>📍 Addresses</h4>
                        <p><strong>Primary:</strong> ${data.addresses?.primary || 'Not set'}</p>
                        <p><strong>Secondary:</strong> ${data.addresses?.secondary || 'Not set'}</p>
                    </div>
                    <div style="background: #f8fafc; padding: 1rem; border-radius: 4px;">
                        <h4>🌐 Social Media</h4>
                        <p><strong>Facebook:</strong> ${data.social?.facebook !== '#' ? data.social?.facebook : 'Not set'}</p>
                        <p><strong>Twitter:</strong> ${data.social?.twitter !== '#' ? data.social?.twitter : 'Not set'}</p>
                        <p><strong>LinkedIn:</strong> ${data.social?.linkedin !== '#' ? data.social?.linkedin : 'Not set'}</p>
                        <p><strong>Instagram:</strong> ${data.social?.instagram !== '#' ? data.social?.instagram : 'Not set'}</p>
                    </div>
                </div>
            `;
            
            document.getElementById('contact-preview').innerHTML += previewHtml;
        }

        async function testFooterReplacement() {
            const status = document.getElementById('replacement-status');
            const details = document.getElementById('replacement-details');
            
            status.style.display = 'block';
            status.className = 'test-result loading';
            status.innerHTML = 'Testing footer replacement...';
            
            try {
                // Load footer first
                await loadFooter();
                
                // Wait a moment for footer to render
                setTimeout(() => {
                    // Check if footer elements exist
                    const footerElements = {
                        'contact-address-primary': document.getElementById('contact-address-primary'),
                        'contact-phone': document.getElementById('contact-phone'),
                        'contact-email': document.getElementById('contact-email'),
                        'social-facebook': document.getElementById('social-facebook'),
                        'social-twitter': document.getElementById('social-twitter'),
                        'social-linkedin': document.getElementById('social-linkedin'),
                        'social-instagram': document.getElementById('social-instagram')
                    };
                    
                    let foundElements = 0;
                    let results = [];
                    
                    Object.keys(footerElements).forEach(id => {
                        if (footerElements[id]) {
                            foundElements++;
                            results.push(`✅ Found element: ${id}`);
                        } else {
                            results.push(`❌ Missing element: ${id}`);
                        }
                    });
                    
                    if (foundElements > 0) {
                        status.className = 'test-result success';
                        status.innerHTML = `✅ Footer elements found (${foundElements}/7)`;
                        
                        // Test contact loader
                        if (window.contactLoader) {
                            results.push('✅ Contact loader initialized');
                            results.push('🔄 Triggering contact replacement...');
                            
                            setTimeout(() => {
                                window.contactLoader.replaceFooterContactInfo();
                                results.push('✅ Footer replacement completed');
                                details.innerHTML = `<div class="debug-info">${results.join('\n')}</div>`;
                            }, 1000);
                        } else {
                            results.push('❌ Contact loader not initialized');
                        }
                    } else {
                        status.className = 'test-result error';
                        status.innerHTML = '❌ No footer elements found';
                    }
                    
                    details.innerHTML = `<div class="debug-info">${results.join('\n')}</div>`;
                }, 2000);
                
            } catch (error) {
                status.className = 'test-result error';
                status.innerHTML = '❌ Footer replacement test failed';
                details.innerHTML = `<div class="debug-info">Error: ${error.message}</div>`;
            }
        }

        async function loadFooter() {
            try {
                const response = await fetch('includes/footer.html');
                const footerHtml = await response.text();
                document.getElementById('footer-container').innerHTML = footerHtml;
                console.log('Footer loaded for testing');
            } catch (error) {
                console.error('Error loading footer:', error);
                throw error;
            }
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            testContactAPI();
            
            // Load footer for testing
            loadFooter();
        });
    </script>
</body>
</html>
