<?php
/**
 * Email Configuration for Forensic Involve Application Form
 *
 * IMPORTANT: Update these settings with your actual email configuration
 * For Gmail, you'll need to:
 * 1. Enable 2-factor authentication
 * 2. Generate an "App Password" for this application
 * 3. Use the app password instead of your regular password
 */

return [
    // SMTP Configuration
    'smtp' => [
        'host' => 'smtp.hostinger.com',           // Your SMTP server
        'port' => 465,                        // SMTP port (587 for TLS, 465 for SSL)
        'username' => '<EMAIL>', // Your email address
        'password' => 'Money2025@Demo#',    // Your app password (not regular password)
        'encryption' => 'ssl',                // 'tls' or 'ssl' (use 'ssl' for port 465)
    ],

    // Email Addresses
    'email' => [
        'from_address' => '<EMAIL>',
        'from_name' => 'Forensic Involve Application Form',
        'to_address' => '<EMAIL>',
        'to_name' => 'Forensic Involve Team',
        'reply_to_client' => true, // Whether to set client email as reply-to
    ],

    // Application Settings
    'app' => [
        'debug' => false,                     // Set to false in production
        'success_redirect' => 'application-success.php?success=true',
        'error_redirect' => 'apply.html',
        'max_file_size' => 10485760,         // 10MB in bytes
        'allowed_file_types' => ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'txt'],
    ],

    // Security Settings
    'security' => [
        'csrf_protection' => true,
        'rate_limiting' => true,
        'max_submissions_per_hour' => 5,     // Per IP address
        'honeypot_field' => 'website',       // Hidden field to catch bots
    ],

    // Notification Settings
    'notifications' => [
        'send_confirmation_to_client' => true,
        'send_copy_to_admin' => true,
        'urgent_cases_phone_alert' => true,  // For emergency/urgent cases
        'admin_phone' => '+44 7352 286599',
    ]
];
?>
