# Deny direct access to uploaded files for security
# This prevents execution of any uploaded scripts

# Deny access to all files by default
<Files "*">
    Order Deny,Allow
    Deny from all
</Files>

# Allow only specific safe file types to be accessed
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|txt)$">
    Order Allow,<PERSON>y
    Allow from all
</FilesMatch>

# Prevent execution of PHP and other script files
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Remove server signature
ServerSignature Off

# Prevent access to hidden files
<FilesMatch "^\.">
    Order Deny,Allow
    Deny from all
</FilesMatch>
