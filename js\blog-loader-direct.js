/**
 * Direct PHP Blog Loader - Alternative to API for better consistency
 * This version uses direct PHP includes instead of API calls for more reliable data
 */

// Function to load blog posts for homepage
function loadHomepageBlogsDirect() {
    const blogContainer = document.querySelector('#blog-grid');
    if (!blogContainer) {
        console.log('Blog container not found on this page');
        return;
    }

    console.log('Loading homepage blogs via direct PHP connection...');

    // Create a hidden iframe to load PHP content
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = 'includes/blog-data.php?limit=3&type=homepage';
    
    iframe.onload = function() {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const dataElement = iframeDoc.querySelector('#blog-data');
            
            if (dataElement) {
                const blogData = JSON.parse(dataElement.textContent);
                console.log('Direct PHP blog data loaded:', blogData);
                
                if (blogData.success && blogData.posts.length > 0) {
                    displayHomepagePostsDirect(blogData.posts);
                } else {
                    displayFallbackPosts();
                }
            } else {
                console.error('No blog data found in PHP response');
                displayFallbackPosts();
            }
        } catch (error) {
            console.error('Error parsing PHP blog data:', error);
            displayFallbackPosts();
        } finally {
            document.body.removeChild(iframe);
        }
    };
    
    iframe.onerror = function() {
        console.error('Error loading PHP blog data');
        displayFallbackPosts();
        document.body.removeChild(iframe);
    };
    
    document.body.appendChild(iframe);
}

function displayHomepagePostsDirect(posts) {
    const blogContainer = document.querySelector('#blog-grid');
    
    const postsHtml = posts.map(post => {
        // Handle featured image with multiple fallback paths
        let imageHtml = '';
        if (post.featured_image && post.featured_image.trim() !== '') {
            let imagePath = post.featured_image.trim();
            
            // Ensure proper path format
            if (!imagePath.startsWith('admin/') && !imagePath.startsWith('http')) {
                imagePath = 'admin/' + imagePath;
            }
            
            imageHtml = `
                <div class="card-image" style="height: 200px; background: url('${imagePath}') center/cover; border-radius: 0.5rem 0.5rem 0 0; margin: -1.5rem -1.5rem 1.5rem -1.5rem;">
                    <img src="${imagePath}" alt="${escapeHtml(post.title)}" style="display: none;" 
                         onerror="this.parentElement.style.background='linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)'; this.parentElement.innerHTML='<div style=\\'display:flex;align-items:center;justify-content:center;height:100%;color:white;font-size:3rem;\\'>📰</div>';">
                </div>
            `;
        } else {
            imageHtml = `
                <div class="card-image" style="height: 200px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); border-radius: 0.5rem 0.5rem 0 0; margin: -1.5rem -1.5rem 1.5rem -1.5rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
                    📰
                </div>
            `;
        }

        return `
            <div class="card">
                ${imageHtml}
                <div class="card-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h3 class="card-title">${escapeHtml(post.title)}</h3>
                <p class="card-description">${escapeHtml(post.excerpt)}</p>
                <a href="single-news.html?id=${post.id}" class="btn btn-primary">Read More</a>
            </div>
        `;
    }).join('');

    blogContainer.innerHTML = postsHtml;
    console.log('✅ Homepage blog posts displayed successfully via direct PHP');
}

// Function to load blog posts for news page
function loadNewsPageBlogsDirect() {
    const newsContainer = document.querySelector('.news-grid');
    if (!newsContainer) {
        console.log('News container not found on this page');
        return;
    }

    console.log('Loading news page blogs via direct PHP connection...');

    const page = getUrlParameter('page') || 1;
    const category = getUrlParameter('category') || '';

    // Create a hidden iframe to load PHP content
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    iframe.src = `includes/blog-data.php?limit=12&page=${page}&category=${encodeURIComponent(category)}&type=news`;
    
    iframe.onload = function() {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            const dataElement = iframeDoc.querySelector('#blog-data');
            
            if (dataElement) {
                const blogData = JSON.parse(dataElement.textContent);
                console.log('Direct PHP news data loaded:', blogData);
                
                if (blogData.success && blogData.posts.length > 0) {
                    displayNewsPagePostsDirect(blogData.posts, blogData.pagination);
                    updatePaginationDirect(blogData.pagination);
                } else {
                    displayNoPostsMessage();
                }
            } else {
                console.error('No news data found in PHP response');
                displayNoPostsMessage();
            }
        } catch (error) {
            console.error('Error parsing PHP news data:', error);
            displayErrorMessage();
        } finally {
            document.body.removeChild(iframe);
        }
    };
    
    iframe.onerror = function() {
        console.error('Error loading PHP news data');
        displayErrorMessage();
        document.body.removeChild(iframe);
    };
    
    document.body.appendChild(iframe);
}

function displayNewsPagePostsDirect(posts, pagination) {
    const newsContainer = document.querySelector('.news-grid');
    
    const postsHtml = posts.map(post => {
        // Handle featured image with multiple fallback paths
        let imageHtml = '';
        if (post.featured_image && post.featured_image.trim() !== '') {
            let imagePath = post.featured_image.trim();
            
            // Ensure proper path format
            if (!imagePath.startsWith('admin/') && !imagePath.startsWith('http')) {
                imagePath = 'admin/' + imagePath;
            }
            
            imageHtml = `
                <div class="news-image" style="height: 200px; background: url('${imagePath}') center/cover; border-radius: 0.5rem; margin-bottom: 1rem;">
                    <img src="${imagePath}" alt="${escapeHtml(post.title)}" style="display: none;" 
                         onerror="this.parentElement.style.background='linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)'; this.parentElement.innerHTML='<div style=\\'display:flex;align-items:center;justify-content:center;height:100%;color:white;font-size:3rem;\\'>📰</div>';">
                </div>
            `;
        } else {
            imageHtml = `
                <div class="news-image" style="height: 200px; background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); border-radius: 0.5rem; margin-bottom: 1rem; display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">
                    📰
                </div>
            `;
        }

        return `
            <article class="news-card">
                ${imageHtml}
                <div class="news-content">
                    <div class="news-meta">
                        <span class="news-category">${escapeHtml(post.category)}</span>
                        <span class="news-date">${post.formatted_date}</span>
                    </div>
                    <h3 class="news-title">${escapeHtml(post.title)}</h3>
                    <p class="news-excerpt">${escapeHtml(post.excerpt)}</p>
                    <a href="single-news.html?id=${post.id}" class="btn btn-primary">Read Full Article</a>
                </div>
            </article>
        `;
    }).join('');

    newsContainer.innerHTML = postsHtml;
    console.log('✅ News page posts displayed successfully via direct PHP');
}

// Utility functions (same as original)
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text ? text.replace(/[&<>"']/g, m => map[m]) : '';
}

function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

// Fallback functions (same as original)
function displayFallbackPosts() {
    const blogContainer = document.querySelector('#blog-grid');
    if (!blogContainer) return;
    
    console.log('Displaying fallback blog posts');
    // Implementation same as original blog-loader.js
}

function displayNoPostsMessage() {
    const newsContainer = document.querySelector('.news-grid');
    if (!newsContainer) return;
    
    newsContainer.innerHTML = `
        <div style="text-align: center; padding: 4rem; color: #6b7280;">
            <i class="fas fa-newspaper" style="font-size: 3rem; margin-bottom: 1rem; color: #d1d5db;"></i>
            <h3>No posts found</h3>
            <p>There are no published posts at the moment. Please check back later.</p>
        </div>
    `;
}

function displayErrorMessage() {
    const newsContainer = document.querySelector('.news-grid');
    if (!newsContainer) return;
    
    newsContainer.innerHTML = `
        <div style="text-align: center; padding: 4rem; color: #ef4444;">
            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
            <h3>Error Loading Posts</h3>
            <p>There was an error loading the blog posts. Please try again later.</p>
        </div>
    `;
}

function updatePaginationDirect(pagination) {
    // Implementation for pagination update
    console.log('Pagination:', pagination);
}

// Auto-initialize based on page
document.addEventListener('DOMContentLoaded', function() {
    // Check which page we're on and load appropriate content
    if (document.querySelector('#blog-grid')) {
        loadHomepageBlogsDirect();
    } else if (document.querySelector('.news-grid')) {
        loadNewsPageBlogsDirect();
    }
});
