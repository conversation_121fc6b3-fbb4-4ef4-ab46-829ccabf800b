<?php
/**
 * Installation Script for Forensic Involve Email Setup
 * Run this script once to set up the email configuration
 */

// Check if running from command line or web browser
$isCLI = php_sapi_name() === 'cli';

if (!$isCLI) {
    echo "<!DOCTYPE html><html><head><title>Forensic Involve - Email Setup</title>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:50px auto;padding:20px;line-height:1.6;}";
    echo ".success{background:#d1fae5;border:1px solid #a7f3d0;color:#065f46;padding:15px;border-radius:5px;margin:10px 0;}";
    echo ".error{background:#fee2e2;border:1px solid #fecaca;color:#dc2626;padding:15px;border-radius:5px;margin:10px 0;}";
    echo ".warning{background:#fef3c7;border:1px solid #fde68a;color:#92400e;padding:15px;border-radius:5px;margin:10px 0;}";
    echo ".code{background:#f3f4f6;padding:10px;border-radius:5px;font-family:monospace;margin:10px 0;}";
    echo "input,select{padding:8px;margin:5px 0;width:300px;border:1px solid #ccc;border-radius:4px;}";
    echo "button{background:#1e40af;color:white;padding:10px 20px;border:none;border-radius:5px;cursor:pointer;}";
    echo "button:hover{background:#1e3a8a;}</style></head><body>";
    echo "<h1>🚀 Forensic Involve Email Setup</h1>";
}

function output($message, $type = 'info') {
    global $isCLI;
    
    if ($isCLI) {
        $prefix = '';
        switch ($type) {
            case 'success': $prefix = '✅ '; break;
            case 'error': $prefix = '❌ '; break;
            case 'warning': $prefix = '⚠️  '; break;
            default: $prefix = 'ℹ️  '; break;
        }
        echo $prefix . $message . "\n";
    } else {
        $class = $type === 'info' ? '' : $type;
        echo "<div class='$class'>$message</div>";
    }
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    output("Error: PHP 7.4 or higher is required. Current version: " . PHP_VERSION, 'error');
    exit(1);
}

output("PHP version check passed: " . PHP_VERSION, 'success');

// Check if Composer is available
if (!file_exists('composer.json')) {
    output("Error: composer.json not found. Please ensure you're running this from the project directory.", 'error');
    exit(1);
}

// Install dependencies if vendor directory doesn't exist
if (!file_exists('vendor/autoload.php')) {
    output("Installing PHPMailer dependencies...", 'info');
    
    $composerCommand = 'composer install --no-dev --optimize-autoloader';
    $output = [];
    $returnCode = 0;
    
    exec($composerCommand . ' 2>&1', $output, $returnCode);
    
    if ($returnCode === 0) {
        output("Dependencies installed successfully!", 'success');
    } else {
        output("Error installing dependencies. Please run 'composer install' manually.", 'error');
        output("Composer output: " . implode("\n", $output), 'error');
        exit(1);
    }
} else {
    output("Dependencies already installed.", 'success');
}

// Handle form submission for web interface
if (!$isCLI && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $config = [
        'smtp_host' => $_POST['smtp_host'] ?? 'smtp.gmail.com',
        'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
        'smtp_username' => $_POST['smtp_username'] ?? '',
        'smtp_password' => $_POST['smtp_password'] ?? '',
        'from_email' => $_POST['from_email'] ?? '',
        'from_name' => $_POST['from_name'] ?? 'Forensic Involve Application Form',
        'to_email' => $_POST['to_email'] ?? '<EMAIL>',
        'to_name' => $_POST['to_name'] ?? 'Forensic Involve Team'
    ];
    
    // Validate required fields
    $required = ['smtp_username', 'smtp_password', 'from_email', 'to_email'];
    $missing = [];
    
    foreach ($required as $field) {
        if (empty($config[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        output("Error: Missing required fields: " . implode(', ', $missing), 'error');
    } else {
        // Update process_application.php with new configuration
        $processFile = 'process_application.php';
        if (file_exists($processFile)) {
            $content = file_get_contents($processFile);
            
            // Replace configuration array
            $newConfig = "// Configuration\n\$config = [\n";
            $newConfig .= "    'smtp_host' => '{$config['smtp_host']}',\n";
            $newConfig .= "    'smtp_port' => {$config['smtp_port']},\n";
            $newConfig .= "    'smtp_username' => '{$config['smtp_username']}',\n";
            $newConfig .= "    'smtp_password' => '{$config['smtp_password']}',\n";
            $newConfig .= "    'from_email' => '{$config['from_email']}',\n";
            $newConfig .= "    'from_name' => '{$config['from_name']}',\n";
            $newConfig .= "    'to_email' => '{$config['to_email']}',\n";
            $newConfig .= "    'to_name' => '{$config['to_name']}'\n";
            $newConfig .= "];";
            
            // Use regex to replace the configuration section
            $pattern = '/\/\/ Configuration\s*\$config\s*=\s*\[[^\]]*\];/s';
            $newContent = preg_replace($pattern, $newConfig, $content);
            
            if ($newContent && file_put_contents($processFile, $newContent)) {
                output("Configuration updated successfully!", 'success');
                output("You can now test the form by visiting apply.html", 'success');
                
                // Disable debug mode
                $newContent = str_replace(
                    "error_reporting(E_ALL);\nini_set('display_errors', 1);",
                    "// error_reporting(E_ALL);\n// ini_set('display_errors', 1);",
                    $newContent
                );
                file_put_contents($processFile, $newContent);
                
                output("Debug mode disabled for production.", 'success');
            } else {
                output("Error: Could not update configuration file.", 'error');
            }
        } else {
            output("Error: process_application.php not found.", 'error');
        }
    }
}

// Show configuration form for web interface
if (!$isCLI) {
    echo "<h2>📧 Email Configuration</h2>";
    echo "<p>Please enter your email settings below:</p>";
    
    echo "<form method='post'>";
    echo "<h3>SMTP Settings</h3>";
    echo "<label>SMTP Host:</label><br>";
    echo "<input type='text' name='smtp_host' value='smtp.gmail.com' required><br>";
    
    echo "<label>SMTP Port:</label><br>";
    echo "<input type='number' name='smtp_port' value='587' required><br>";
    
    echo "<label>SMTP Username (your email):</label><br>";
    echo "<input type='email' name='smtp_username' placeholder='<EMAIL>' required><br>";
    
    echo "<label>SMTP Password (app password for Gmail):</label><br>";
    echo "<input type='password' name='smtp_password' placeholder='16-character app password' required><br>";
    
    echo "<h3>Email Addresses</h3>";
    echo "<label>From Email:</label><br>";
    echo "<input type='email' name='from_email' placeholder='<EMAIL>' required><br>";
    
    echo "<label>From Name:</label><br>";
    echo "<input type='text' name='from_name' value='Forensic Involve Application Form'><br>";
    
    echo "<label>To Email (where forms are sent):</label><br>";
    echo "<input type='email' name='to_email' value='<EMAIL>' required><br>";
    
    echo "<label>To Name:</label><br>";
    echo "<input type='text' name='to_name' value='Forensic Involve Team'><br>";
    
    echo "<br><button type='submit'>💾 Save Configuration</button>";
    echo "</form>";
    
    echo "<div class='warning'>";
    echo "<h3>📋 Gmail Setup Instructions:</h3>";
    echo "<ol>";
    echo "<li>Enable 2-factor authentication in your Google Account</li>";
    echo "<li>Go to Security → 2-Step Verification → App passwords</li>";
    echo "<li>Generate an app password for 'Mail'</li>";
    echo "<li>Use the 16-character app password (not your regular password)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div class='code'>";
    echo "<strong>Test the setup:</strong><br>";
    echo "1. Save the configuration above<br>";
    echo "2. Visit apply.html<br>";
    echo "3. Fill out and submit the form<br>";
    echo "4. Check your email at the 'To Email' address";
    echo "</div>";
    
    echo "</body></html>";
} else {
    // CLI mode - show instructions
    output("Setup completed! Next steps:", 'success');
    output("1. Edit process_application.php and update the email configuration", 'info');
    output("2. Set up Gmail app password (see README-EMAIL-SETUP.md)", 'info');
    output("3. Test the form by visiting apply.html", 'info');
    output("4. Check email <NAME_EMAIL>", 'info');
}

// Check file permissions
$files = ['process_application.php', 'apply.html', 'application-success.html'];
foreach ($files as $file) {
    if (file_exists($file)) {
        if (is_readable($file) && is_writable($file)) {
            output("File permissions OK: $file", 'success');
        } else {
            output("Warning: Check permissions for $file", 'warning');
        }
    } else {
        output("Warning: File not found: $file", 'warning');
    }
}

if ($isCLI) {
    output("Installation complete! 🎉", 'success');
}
?>
