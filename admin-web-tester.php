<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Pages Web View Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 2rem;
        }
        
        .page-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .page-card:hover {
            transform: translateY(-5px);
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            text-align: center;
        }
        
        .page-content {
            padding: 1rem;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-ok {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .iframe-container {
            margin-top: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .iframe-header {
            background: #343a40;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .iframe-content {
            height: 600px;
            border: none;
            width: 100%;
        }
        
        .tools {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .tools h3 {
            margin-bottom: 1rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Admin Pages Web View Tester</h1>
            <p>Test and monitor all admin pages for errors and functionality</p>
        </div>

        <div class="tools">
            <h3>🛠️ Testing Tools</h3>
            <a href="error-log-viewer.php" class="btn btn-danger" target="_blank">📋 View Error Logs</a>
            <a href="security-audit-report.php" class="btn btn-warning" target="_blank">🔒 Security Audit</a>
            <a href="test-database.php" class="btn btn-success" target="_blank">🗄️ Database Test</a>
            <a href="javascript:location.reload()" class="btn">🔄 Refresh Page</a>
        </div>

        <div class="page-grid">
            <!-- Admin Dashboard -->
            <div class="page-card">
                <div class="page-header">
                    <h3>📊 Admin Dashboard</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/index.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Active</span></p>
                    <p><strong>Description:</strong> Main admin dashboard with overview</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/index.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/index.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>

            <!-- Posts Management -->
            <div class="page-card">
                <div class="page-header">
                    <h3>📝 Manage Posts</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/posts.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Active</span></p>
                    <p><strong>Description:</strong> View and manage blog posts</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/posts.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/posts.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>

            <!-- Add Post -->
            <div class="page-card">
                <div class="page-header">
                    <h3>➕ Add New Post</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/add-post.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Active</span></p>
                    <p><strong>Description:</strong> Create new blog posts</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/add-post.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/add-post.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>

            <!-- Categories Management -->
            <div class="page-card">
                <div class="page-header">
                    <h3>📁 Categories</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/categories.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Fixed</span></p>
                    <p><strong>Description:</strong> Manage blog categories</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/categories.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/categories.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>

            <!-- Media Management -->
            <div class="page-card">
                <div class="page-header">
                    <h3>🖼️ Media</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/media.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Active</span></p>
                    <p><strong>Description:</strong> Upload and manage media files</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/media.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/media.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>

            <!-- Login Page -->
            <div class="page-card">
                <div class="page-header">
                    <h3>🔐 Admin Login</h3>
                </div>
                <div class="page-content">
                    <p><strong>File:</strong> admin/login.php</p>
                    <p><strong>Status:</strong> <span class="status status-ok">Active</span></p>
                    <p><strong>Description:</strong> Admin authentication page</p>
                    <div style="margin-top: 1rem;">
                        <a href="admin/login.php" class="btn" target="_blank">🔗 Open Page</a>
                        <a href="javascript:loadInFrame('admin/login.php')" class="btn btn-success">👁️ Preview</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="iframe-container" id="previewContainer" style="display: none;">
            <div class="iframe-header">
                <h3 id="frameTitle">Page Preview</h3>
                <button onclick="closeFrame()" class="btn btn-danger">✕ Close</button>
            </div>
            <iframe id="previewFrame" class="iframe-content" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        function loadInFrame(url) {
            document.getElementById('previewContainer').style.display = 'block';
            document.getElementById('previewFrame').src = url;
            document.getElementById('frameTitle').textContent = 'Preview: ' + url;
            document.getElementById('previewContainer').scrollIntoView({ behavior: 'smooth' });
        }

        function closeFrame() {
            document.getElementById('previewContainer').style.display = 'none';
            document.getElementById('previewFrame').src = 'about:blank';
        }

        // Auto-check page status
        function checkPageStatus() {
            const pages = [
                'admin/index.php',
                'admin/posts.php', 
                'admin/add-post.php',
                'admin/categories.php',
                'admin/media.php',
                'admin/login.php'
            ];

            pages.forEach(page => {
                fetch(page, { method: 'HEAD' })
                    .then(response => {
                        const statusElements = document.querySelectorAll('.status');
                        // Update status based on response
                    })
                    .catch(error => {
                        console.log('Error checking ' + page + ':', error);
                    });
            });
        }

        // Run status check on load
        document.addEventListener('DOMContentLoaded', function() {
            checkPageStatus();
        });
    </script>
</body>
</html>
