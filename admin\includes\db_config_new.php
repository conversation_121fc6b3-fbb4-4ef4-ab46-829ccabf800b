<?php
/**
 * Simple Database Configuration for Admin Panel
 */

// Database configuration
$db_host = 'localhost';
$db_username = 'root';
$db_password = 'root';
$db_name = 'forensics_involve';

// Create database connection using mysqli
$mysqli = new mysqli($db_host, $db_username, $db_password);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

// Create database if it doesn't exist
$mysqli->query("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

// Select the database
$mysqli->select_db($db_name);

// Set charset
$mysqli->set_charset('utf8mb4');

// Create tables if they don't exist
$tables = [
    // Admin users table
    "CREATE TABLE IF NOT EXISTS `admin_users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `last_login` datetime DEFAULT NULL,
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
    
    // Blog posts table
    "CREATE TABLE IF NOT EXISTS `blog_posts` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `content` longtext NOT NULL,
        `excerpt` text,
        `category_id` int(11) DEFAULT NULL,
        `status` enum('draft','published','archived') DEFAULT 'draft',
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `category_id` (`category_id`),
        KEY `status` (`status`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
    
    // Categories table
    "CREATE TABLE IF NOT EXISTS `categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL UNIQUE,
        `slug` varchar(100) NOT NULL UNIQUE,
        `description` text,
        `color` varchar(7) DEFAULT '#1e40af',
        `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
];

foreach ($tables as $table) {
    if (!$mysqli->query($table)) {
        die("Error creating table: " . $mysqli->error);
    }
}

// Insert default admin user if not exists
$stmt = $mysqli->prepare("SELECT COUNT(*) FROM admin_users WHERE username = ?");
$stmt->bind_param("s", $admin_username);
$admin_username = 'admin';
$stmt->execute();
$result = $stmt->get_result();
$count = $result->fetch_row()[0];

if ($count == 0) {
    $stmt = $mysqli->prepare("INSERT INTO admin_users (username, password, full_name, email) VALUES (?, ?, ?, ?)");
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $full_name = 'Administrator';
    $email = '<EMAIL>';
    $stmt->bind_param("ssss", $admin_username, $password_hash, $full_name, $email);
    $stmt->execute();
}

// Insert default categories if not exist
$categories = [
    ['Cryptocurrency', 'cryptocurrency', 'Articles about cryptocurrency fraud and recovery', '#1e40af'],
    ['Fraud Prevention', 'fraud-prevention', 'Tips and guides for preventing fraud', '#10b981'],
    ['Recovery Tips', 'recovery-tips', 'Advice for fraud victims', '#f59e0b'],
    ['Case Studies', 'case-studies', 'Real case studies and success stories', '#8b5cf6'],
    ['Industry News', 'industry-news', 'Latest news in forensic investigation', '#ef4444'],
    ['Technology', 'technology', 'Technology updates and digital forensics', '#6366f1']
];

foreach ($categories as $cat) {
    $stmt = $mysqli->prepare("SELECT COUNT(*) FROM categories WHERE slug = ?");
    $stmt->bind_param("s", $cat[1]);
    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_row()[0];
    
    if ($count == 0) {
        $stmt = $mysqli->prepare("INSERT INTO categories (name, slug, description, color) VALUES (?, ?, ?, ?)");
        $stmt->bind_param("ssss", $cat[0], $cat[1], $cat[2], $cat[3]);
        $stmt->execute();
    }
}

// Create a simple PDO-like wrapper for compatibility
class SimplePDO {
    private $mysqli;
    
    public function __construct($mysqli) {
        $this->mysqli = $mysqli;
    }
    
    public function prepare($sql) {
        $stmt = $this->mysqli->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $this->mysqli->error);
        }
        return new SimpleStatement($stmt);
    }
    
    public function query($sql) {
        $result = $this->mysqli->query($sql);
        if ($result === false) {
            throw new Exception("Query failed: " . $this->mysqli->error);
        }
        return new SimpleResult($result, $this->mysqli);
    }
    
    public function exec($sql) {
        return $this->mysqli->query($sql);
    }
}

class SimpleStatement {
    private $stmt;
    
    public function __construct($stmt) {
        $this->stmt = $stmt;
    }
    
    public function execute($params = []) {
        if (!empty($params)) {
            $types = str_repeat('s', count($params));
            $this->stmt->bind_param($types, ...$params);
        }
        return $this->stmt->execute();
    }
    
    public function fetch($mode = null) {
        $result = $this->stmt->get_result();
        return $result ? $result->fetch_assoc() : false;
    }
    
    public function fetchAll($mode = null) {
        $result = $this->stmt->get_result();
        return $result ? $result->fetch_all(MYSQLI_ASSOC) : [];
    }
    
    public function rowCount() {
        return $this->stmt->affected_rows;
    }
}

class SimpleResult {
    private $result;
    private $mysqli;
    
    public function __construct($result, $mysqli) {
        $this->result = $result;
        $this->mysqli = $mysqli;
    }
    
    public function fetch($mode = null) {
        if (is_bool($this->result)) {
            return false;
        }
        return $this->result->fetch_assoc();
    }
    
    public function fetchAll($mode = null) {
        if (is_bool($this->result)) {
            return [];
        }
        return $this->result->fetch_all(MYSQLI_ASSOC);
    }
    
    public function fetchColumn() {
        $row = $this->fetch();
        return $row ? reset($row) : false;
    }
    
    public function rowCount() {
        return is_bool($this->result) ? $this->mysqli->affected_rows : $this->result->num_rows;
    }
}

// Create the PDO-like object
$pdo = new SimplePDO($mysqli);
?>
