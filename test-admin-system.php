<?php
/**
 * Quick test script to check admin system functionality
 */
require_once 'admin/includes/db_config.php';
require_once 'admin/includes/auth.php';

echo "<h2>Admin System Test</h2>";

try {
    // Test database connection
    echo "<p>✅ Database connection: SUCCESS</p>";
    
    // Test tables exist
    $tables_to_check = ['admin_users', 'blog_posts', 'categories'];
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Table '$table': EXISTS</p>";
        } else {
            echo "<p>❌ Table '$table': MISSING</p>";
        }
    }
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM admin_users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>✅ Admin user: EXISTS (username: admin)</p>";
    } else {
        echo "<p>❌ Admin user: MISSING</p>";
    }
    
    // Check categories
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
    $result = $stmt->fetch();
    echo "<p>✅ Categories: " . $result['count'] . " categories found</p>";
    
    // Check blog posts
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM blog_posts");
    $result = $stmt->fetch();
    echo "<p>✅ Blog posts: " . $result['count'] . " posts found</p>";
    
} catch (PDOException $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
