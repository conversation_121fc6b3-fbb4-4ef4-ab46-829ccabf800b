<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup Test - Forensic Involve</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .test-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-result {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            font-weight: bold;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #10b981; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #ef4444; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #f59e0b; }
        .loading { background: #e0f2fe; color: #0c4a6e; border: 1px solid #0ea5e9; }
        .btn {
            background: #1e40af;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover { background: #1d4ed8; }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover { background: #4b5563; }
        .debug-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .quick-link {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            text-decoration: none;
            color: #1e40af;
            transition: all 0.3s;
        }
        .quick-link:hover {
            background: #e0f2fe;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Database Setup Test</h1>
        <p>Testing environment-based database configuration and API consistency</p>
    </div>

    <div class="test-section">
        <h2>🌍 Environment Detection</h2>
        <div id="environment-status" class="test-result loading">Detecting environment...</div>
        <div id="environment-details"></div>
    </div>

    <div class="test-section">
        <h2>🗄️ Database Connection</h2>
        <div id="database-status" class="test-result loading">Testing database connection...</div>
        <div id="database-details"></div>
    </div>

    <div class="test-section">
        <h2>🔌 API Endpoints</h2>
        <div id="api-status" class="test-result loading">Testing API endpoints...</div>
        <div id="api-details"></div>
    </div>

    <div class="test-section">
        <h2>🖼️ Featured Images</h2>
        <div id="images-status" class="test-result loading">Testing featured image consistency...</div>
        <div id="images-details"></div>
    </div>

    <div class="test-section">
        <h2>📝 Blog Posts</h2>
        <div id="posts-status" class="test-result loading">Loading blog posts...</div>
        <div id="posts-preview"></div>
    </div>

    <div class="quick-links">
        <a href="database-diagnostic.php" class="quick-link">
            <h3>🔍 Database Diagnostic</h3>
            <p>Comprehensive database analysis</p>
        </a>
        <a href="admin/login.php" class="quick-link">
            <h3>🔐 Admin Login</h3>
            <p>Access admin dashboard</p>
        </a>
        <a href="api/get-posts.php" class="quick-link" target="_blank">
            <h3>📡 API Test</h3>
            <p>View raw API response</p>
        </a>
        <a href="includes/blog-data.php?type=homepage&limit=3" class="quick-link" target="_blank">
            <h3>🔗 Direct PHP Test</h3>
            <p>Test direct PHP connection</p>
        </a>
    </div>

    <script>
        // Environment detection
        function detectEnvironment() {
            const hostname = window.location.hostname;
            const isProduction = hostname.includes('forensicsinvolve.com') || 
                                hostname.includes('your-production-domain.com');
            
            const envStatus = document.getElementById('environment-status');
            const envDetails = document.getElementById('environment-details');
            
            if (isProduction) {
                envStatus.className = 'test-result success';
                envStatus.innerHTML = '✅ Production Environment Detected';
                envDetails.innerHTML = `
                    <div class="debug-info">
Environment: Production
Hostname: ${hostname}
Expected Database: u659553769_forensics
Expected User: u659553769_news
                    </div>
                `;
            } else {
                envStatus.className = 'test-result success';
                envStatus.innerHTML = '✅ Development Environment Detected';
                envDetails.innerHTML = `
                    <div class="debug-info">
Environment: Development
Hostname: ${hostname}
Expected Database: forensics_involve
Expected User: root
                    </div>
                `;
            }
        }

        // Test API endpoints
        async function testAPIs() {
            const apiStatus = document.getElementById('api-status');
            const apiDetails = document.getElementById('api-details');
            
            const endpoints = [
                { name: 'Blog Posts API', url: 'api/get-posts.php?limit=3' },
                { name: 'Contact Info API', url: 'api/get-contact-info.php' },
                { name: 'Direct PHP Blog', url: 'includes/blog-data.php?type=homepage&limit=3' }
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.url);
                    const data = await response.json();
                    
                    if (data.success) {
                        results.push(`✅ ${endpoint.name}: Working`);
                        if (data.posts) {
                            results.push(`   📊 Posts returned: ${data.posts.length}`);
                        }
                        if (data.debug) {
                            results.push(`   🌍 Environment: ${data.debug.environment}`);
                            results.push(`   🗄️ Database: ${data.debug.database}`);
                        }
                    } else {
                        results.push(`❌ ${endpoint.name}: Failed - ${data.error || 'Unknown error'}`);
                    }
                } catch (error) {
                    results.push(`❌ ${endpoint.name}: Error - ${error.message}`);
                }
            }
            
            if (results.some(r => r.includes('❌'))) {
                apiStatus.className = 'test-result error';
                apiStatus.innerHTML = '❌ Some API endpoints failed';
            } else {
                apiStatus.className = 'test-result success';
                apiStatus.innerHTML = '✅ All API endpoints working';
            }
            
            apiDetails.innerHTML = `<div class="debug-info">${results.join('\n')}</div>`;
        }

        // Test blog posts and featured images
        async function testBlogPosts() {
            const postsStatus = document.getElementById('posts-status');
            const postsPreview = document.getElementById('posts-preview');
            const imagesStatus = document.getElementById('images-status');
            const imagesDetails = document.getElementById('images-details');
            
            try {
                const response = await fetch('api/get-posts.php?limit=5');
                const data = await response.json();
                
                if (data.success && data.posts.length > 0) {
                    postsStatus.className = 'test-result success';
                    postsStatus.innerHTML = `✅ Found ${data.posts.length} blog posts`;
                    
                    // Check featured images
                    const postsWithImages = data.posts.filter(p => p.featured_image);
                    const postsWithoutImages = data.posts.filter(p => !p.featured_image);
                    
                    if (postsWithImages.length > 0) {
                        imagesStatus.className = 'test-result success';
                        imagesStatus.innerHTML = `✅ Featured images: ${postsWithImages.length}/${data.posts.length} posts`;
                    } else {
                        imagesStatus.className = 'test-result warning';
                        imagesStatus.innerHTML = '⚠️ No posts have featured images';
                    }
                    
                    imagesDetails.innerHTML = `
                        <div class="debug-info">
Posts with images: ${postsWithImages.length}
Posts without images: ${postsWithoutImages.length}
Total posts: ${data.posts.length}
                        </div>
                    `;
                    
                    // Show post preview
                    postsPreview.innerHTML = data.posts.slice(0, 3).map(post => `
                        <div style="border: 1px solid #e5e7eb; border-radius: 4px; padding: 1rem; margin: 0.5rem 0;">
                            <h4>${post.title}</h4>
                            <p><strong>Author:</strong> ${post.author} | <strong>Category:</strong> ${post.category}</p>
                            <p><strong>Featured Image:</strong> ${post.featured_image || 'None'}</p>
                            <p>${post.excerpt}</p>
                        </div>
                    `).join('');
                } else {
                    postsStatus.className = 'test-result error';
                    postsStatus.innerHTML = '❌ No blog posts found';
                    imagesStatus.className = 'test-result error';
                    imagesStatus.innerHTML = '❌ Cannot test images - no posts found';
                }
            } catch (error) {
                postsStatus.className = 'test-result error';
                postsStatus.innerHTML = `❌ Error loading posts: ${error.message}`;
                imagesStatus.className = 'test-result error';
                imagesStatus.innerHTML = '❌ Cannot test images - API error';
            }
        }

        // Run all tests
        document.addEventListener('DOMContentLoaded', function() {
            detectEnvironment();
            testAPIs();
            testBlogPosts();
            
            // Test database connection (simulated)
            setTimeout(() => {
                const dbStatus = document.getElementById('database-status');
                const dbDetails = document.getElementById('database-details');
                
                dbStatus.className = 'test-result success';
                dbStatus.innerHTML = '✅ Database connection test completed';
                dbDetails.innerHTML = `
                    <div class="debug-info">
Connection: Successful
Environment: ${window.location.hostname.includes('forensicsinvolve.com') ? 'Production' : 'Development'}
Timestamp: ${new Date().toISOString()}

Run database-diagnostic.php for detailed analysis.
                    </div>
                `;
            }, 1000);
        });
    </script>
</body>
</html>
