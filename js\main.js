/**
 * Foresenic Invole - Main JavaScript File
 */

// Function to load includes
async function loadIncludes() {
  // Load header
  const headerPlaceholder = document.getElementById('header-placeholder');
  if (headerPlaceholder) {
    try {
      const response = await fetch('includes/header.html?v=' + Date.now());
      const headerHTML = await response.text();
      headerPlaceholder.innerHTML = headerHTML;
      
      // Call setActivePage function from header after it loads
      setTimeout(() => {
        if (typeof setActivePage === 'function') {
          setActivePage();
        }
      }, 100);
    } catch (error) {
      console.error('Error loading header:', error);
    }
  }

  // Load footer
  const footerPlaceholder = document.getElementById('footer-placeholder');
  if (footerPlaceholder) {
    try {
      const response = await fetch('includes/footer.html?v=' + Date.now());
      const footerHTML = await response.text();
      footerPlaceholder.innerHTML = footerHTML;
      console.log('Footer loaded successfully');

      // Notify that footer is loaded - let contact-loader.js handle initialization
      setTimeout(() => {
        console.log('Footer loaded, dispatching footerLoaded event...');
        window.dispatchEvent(new CustomEvent('footerLoaded'));
      }, 100);

    } catch (error) {
      console.error('Error loading footer:', error);
    }
  }

  // Set active navigation after header is loaded
  setTimeout(() => {
    // Only call if header doesn't have its own setActivePage function
    if (typeof setActivePage === 'undefined') {
      setActiveNavigation();
    }
  }, 200);
}

// Function to set active navigation
function setActiveNavigation() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    link.classList.remove('active');
    const href = link.getAttribute('href');
    if (href === currentPage || (currentPage === '' && href === 'index.html')) {
      link.classList.add('active');
    }
  });
}

document.addEventListener('DOMContentLoaded', function() {
  // Load includes first
  loadIncludes();

  // Mobile menu toggle functionality
  document.addEventListener('click', function(e) {
    if (e.target.matches('.mobile-menu-toggle') || e.target.closest('.mobile-menu-toggle')) {
      const navMenu = document.querySelector('.nav-menu');
      const toggle = e.target.closest('.mobile-menu-toggle') || e.target;

      if (navMenu) {
        navMenu.classList.toggle('active');
        toggle.classList.toggle('active');
      }
    }
  });

  // Smooth scrolling for anchor links
  document.addEventListener('click', function(e) {
    if (e.target.matches('a[href^="#"]')) {
      e.preventDefault();
      const targetId = e.target.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }
  });

  // Form validation
  document.addEventListener('submit', function(e) {
    if (e.target.matches('form')) {
      const requiredFields = e.target.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          isValid = false;
          field.classList.add('error');
        } else {
          field.classList.remove('error');
        }
      });

      if (!isValid) {
        e.preventDefault();
        alert('Please fill in all required fields.');
      }
    }
  });

  // Newsletter form handling
  document.addEventListener('submit', function(e) {
    if (e.target.matches('.newsletter-form')) {
      e.preventDefault();
      const email = e.target.querySelector('input[type="email"]').value;

      if (email) {
        alert('Thank you for subscribing to our newsletter!');
        e.target.reset();
      }
    }
  });

  // Hero slider
  initHeroSlider();

  // Testimonial slider
  initTestimonialSlider();

  // FAQ functionality
  initFAQ();

  // Back to top functionality
  initBackToTop();

  // Counter animation
  initCounterAnimation();
});

function initHeroSlider() {
  const slider = document.querySelector('.hero-slider');
  if (!slider) return;

  const slides = slider.querySelectorAll('.hero-slide');
  const dots = slider.querySelectorAll('.hero-dot');
  let currentIndex = 0;

  function showSlide(index) {
    slides.forEach((slide, i) => {
      slide.classList.toggle('active', i === index);
    });

    dots.forEach((dot, i) => {
      dot.classList.toggle('active', i === index);
    });
  }

  // Dot navigation
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      currentIndex = index;
      showSlide(currentIndex);
    });
  });

  // Auto-slide every 6 seconds
  setInterval(() => {
    currentIndex = (currentIndex + 1) % slides.length;
    showSlide(currentIndex);
  }, 6000);

  // Initialize first slide
  showSlide(0);
}

function initTestimonialSlider() {
  const slider = document.querySelector('.testimonial-slider');
  if (!slider) return;

  const testimonials = slider.querySelectorAll('.testimonial');
  const dots = slider.querySelectorAll('.testimonial-dot');
  let currentIndex = 0;

  function showTestimonial(index) {
    testimonials.forEach((testimonial, i) => {
      testimonial.classList.toggle('active', i === index);
    });

    dots.forEach((dot, i) => {
      dot.classList.toggle('active', i === index);
    });
  }

  // Dot navigation
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      currentIndex = index;
      showTestimonial(currentIndex);
    });
  });

  // Auto-slide every 5 seconds
  setInterval(() => {
    currentIndex = (currentIndex + 1) % testimonials.length;
    showTestimonial(currentIndex);
  }, 5000);

  // Initialize first testimonial
  showTestimonial(0);
}

function initFAQ() {
  const faqItems = document.querySelectorAll('.faq-item');

  faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');

    if (question) {
      question.addEventListener('click', () => {
        item.classList.toggle('active');
      });
    }
  });
}

function initBackToTop() {
  // Wait for footer to be loaded
  setTimeout(() => {
    const backToTopBtn = document.querySelector('.back-to-top');

    if (backToTopBtn) {
      backToTopBtn.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      });
    }
  }, 200);
}

function initCounterAnimation() {
  const counters = document.querySelectorAll('.counter');
  const options = {
    threshold: 0.5,
    rootMargin: '0px 0px -100px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const counter = entry.target;
        const target = parseFloat(counter.getAttribute('data-target'));
        const hasDecimal = counter.hasAttribute('data-decimal');
        const decimalPlaces = hasDecimal ? parseInt(counter.getAttribute('data-decimal')) : 0;

        animateCounter(counter, target, decimalPlaces);
        observer.unobserve(counter);
      }
    });
  }, options);

  counters.forEach(counter => {
    observer.observe(counter);
  });
}

function animateCounter(element, target, decimalPlaces = 0) {
  let current = 0;
  const increment = target / 100; // 100 steps for smooth animation
  const duration = 2000; // 2 seconds
  const stepTime = duration / 100;

  const timer = setInterval(() => {
    current += increment;

    if (current >= target) {
      current = target;
      clearInterval(timer);
    }

    if (decimalPlaces > 0) {
      element.textContent = current.toFixed(decimalPlaces);
    } else {
      element.textContent = Math.floor(current).toLocaleString();
    }
  }, stepTime);
}
