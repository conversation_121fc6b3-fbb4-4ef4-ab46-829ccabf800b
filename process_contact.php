<?php
// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include PHPMailer
require_once 'vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Load configuration
$config = require_once 'config.php';

// Function to sanitize input
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Collect and sanitize form data
        $formData = [
            'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
            'email' => filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'fraud_type' => sanitizeInput($_POST['fraud_type'] ?? ''),
            'amount_lost' => sanitizeInput($_POST['amount_lost'] ?? ''),
            'case_details' => sanitizeInput($_POST['case_details'] ?? ''),
            'submission_time' => date('Y-m-d H:i:s')
        ];

        // Validate required fields
        $requiredFields = ['first_name', 'last_name', 'email', 'fraud_type', 'case_details'];
        $missingFields = [];
        
        foreach ($requiredFields as $field) {
            if (empty($formData[$field])) {
                $missingFields[] = str_replace('_', ' ', ucfirst($field));
            }
        }

        if (!empty($missingFields)) {
            throw new Exception('Missing required fields: ' . implode(', ', $missingFields));
        }

        // Validate email
        if (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address provided.');
        }

        // Create PHPMailer instance
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp']['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp']['username'];
        $mail->Password = $config['smtp']['password'];
        $mail->SMTPSecure = ($config['smtp']['encryption'] === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $config['smtp']['port'];

        // Recipients
        $mail->setFrom($config['email']['from_address'], $config['email']['from_name']);
        $mail->addAddress($config['email']['to_address'], $config['email']['to_name']);
        $mail->addReplyTo($formData['email'], $formData['first_name'] . ' ' . $formData['last_name']);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'New Contact Form Inquiry - ' . $formData['first_name'] . ' ' . $formData['last_name'] . ' (' . $formData['fraud_type'] . ')';

        // Create email body
        $emailBody = generateContactEmailBody($formData);
        $mail->Body = $emailBody;
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $emailBody));

        // Send email
        $mail->send();

        // Success response
        $response = [
            'success' => true,
            'message' => 'Message sent successfully! We will contact you within 2 hours.',
            'redirect' => 'contact.html?success=true'
        ];

    } catch (Exception $e) {
        // Error response
        $response = [
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'error_details' => $mail->ErrorInfo ?? ''
        ];
    }

    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // For non-AJAX requests, redirect with message
    if ($response['success']) {
        header('Location: contact.html?success=true');
    } else {
        header('Location: contact.html?error=' . urlencode($response['message']));
    }
    exit;
}

// Function to generate email body for contact form
function generateContactEmailBody($data) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Contact Form Inquiry</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #1e40af; color: white; padding: 20px; text-align: center; }
            .content { background: #f8f9fa; padding: 20px; }
            .section { margin-bottom: 20px; }
            .label { font-weight: bold; color: #1e40af; }
            .value { margin-bottom: 10px; }
            .footer { background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 New Contact Form Inquiry</h1>
                <p>Forensic Involve - Expert Fund Recovery</p>
            </div>
            
            <div class="content">
                <div class="section">
                    <h2>📋 Contact Information</h2>
                    <div class="label">Name:</div>
                    <div class="value">' . htmlspecialchars($data['first_name'] . ' ' . $data['last_name']) . '</div>
                    
                    <div class="label">Email:</div>
                    <div class="value">' . htmlspecialchars($data['email']) . '</div>
                    
                    <div class="label">Phone:</div>
                    <div class="value">' . htmlspecialchars($data['phone'] ?: 'Not provided') . '</div>
                </div>

                <div class="section">
                    <h2>🚨 Case Information</h2>
                    <div class="label">Type of Fraud:</div>
                    <div class="value">' . htmlspecialchars($data['fraud_type']) . '</div>
                    
                    <div class="label">Amount Lost:</div>
                    <div class="value">$' . htmlspecialchars($data['amount_lost'] ?: 'Not specified') . '</div>
                </div>

                <div class="section">
                    <h2>📝 Case Details</h2>
                    <div class="value">' . nl2br(htmlspecialchars($data['case_details'])) . '</div>
                </div>

                <div class="section">
                    <h2>⏰ Submission Details</h2>
                    <div class="label">Submitted:</div>
                    <div class="value">' . htmlspecialchars($data['submission_time']) . '</div>
                    
                    <div class="label">IP Address:</div>
                    <div class="value">' . htmlspecialchars($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . '</div>
                </div>
            </div>
            
            <div class="footer">
                <p>This inquiry was submitted through the Forensic Involve contact form.</p>
                <p>Please respond within 2 hours as promised to the client.</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

// If accessed directly without POST, redirect to contact page
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: contact.html');
    exit;
}
?>
