<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Get dashboard statistics
$total_posts = 0;
$published_posts = 0;
$draft_posts = 0;
$recent_posts = [];
$error = null;

try {    // Count total posts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM blog_posts");
    $result = $stmt->fetch();
    $total_posts = $result ? $result['total'] : 0;

    // Count published posts
    $stmt = $pdo->query("SELECT COUNT(*) as published FROM blog_posts WHERE status = 'published'");
    $result = $stmt->fetch();
    $published_posts = $result ? $result['published'] : 0;

    // Count draft posts
    $stmt = $pdo->query("SELECT COUNT(*) as drafts FROM blog_posts WHERE status = 'draft'");
    $result = $stmt->fetch();
    $draft_posts = $result ? $result['drafts'] : 0;

    // Get recent posts
    $stmt = $pdo->query("SELECT id, title, status, created_at FROM blog_posts ORDER BY created_at DESC LIMIT 5");
    $recent_posts = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

$current_user = getCurrentAdmin();
if (!$current_user) {
    $current_user = ['username' => 'Unknown'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Forensic Recovery Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars($current_user['username']); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item active">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item">
                <span class="icon">📁</span>
                Categories
            </a>
            <a href="media.php" class="menu-item">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1>Dashboard Overview</h1>
            <p>Manage your news and blog content</p>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $total_posts; ?></div>
                <div class="stat-label">Total Posts</div>
                <div class="stat-icon">📄</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $published_posts; ?></div>
                <div class="stat-label">Published</div>
                <div class="stat-icon">✅</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $draft_posts; ?></div>
                <div class="stat-label">Drafts</div>
                <div class="stat-icon">📝</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo date('M j'); ?></div>
                <div class="stat-label">Today</div>
                <div class="stat-icon">📅</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-section">
            <h2>Quick Actions</h2>
            <div class="quick-actions">
                <a href="add-post.php" class="action-card">
                    <div class="action-icon">➕</div>
                    <div class="action-content">
                        <h3>Add New Post</h3>
                        <p>Create a new blog post or news article</p>
                    </div>
                </a>
                <a href="posts.php" class="action-card">
                    <div class="action-icon">📝</div>
                    <div class="action-content">
                        <h3>Manage Posts</h3>
                        <p>Edit, delete, or publish existing posts</p>
                    </div>
                </a>
                <a href="media.php" class="action-card">
                    <div class="action-icon">🖼️</div>
                    <div class="action-content">
                        <h3>Media Library</h3>
                        <p>Upload and manage images and files</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Recent Posts -->
        <div class="dashboard-section">
            <h2>Recent Posts</h2>
            <div class="recent-posts">
                <?php if (empty($recent_posts)): ?>
                    <div class="empty-state">
                        <p>No posts found. <a href="add-post.php">Create your first post</a></p>
                    </div>
                <?php else: ?>
                    <div class="posts-table">
                        <div class="table-header">
                            <div class="col-title">Title</div>
                            <div class="col-status">Status</div>
                            <div class="col-date">Date</div>
                            <div class="col-actions">Actions</div>
                        </div>
                        <?php foreach ($recent_posts as $post): ?>
                            <div class="table-row">
                                <div class="col-title">
                                    <?php echo htmlspecialchars($post['title']); ?>
                                </div>
                                <div class="col-status">
                                    <span class="status-badge status-<?php echo $post['status']; ?>">
                                        <?php echo ucfirst($post['status']); ?>
                                    </span>
                                </div>
                                <div class="col-date">
                                    <?php echo date('M j, Y', strtotime($post['created_at'])); ?>
                                </div>
                                <div class="col-actions">
                                    <a href="edit-post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm">Edit</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script src="js/admin.js"></script>
</body>
</html>
