<?php
require_once 'admin/includes/db_config.php';

echo "Testing Database Connection:\n";
echo "============================\n";

try {
    // Check connection
    echo "✓ Database connection: OK\n";
    
    // List all tables
    echo "\nTables in database:\n";
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll();
    foreach($tables as $table) {
        echo "- " . $table[0] . "\n";
    }
    
    // Try to describe media_files table
    echo "\nMedia files table structure:\n";
    try {
        $stmt = $pdo->query('DESCRIBE media_files');
        $columns = $stmt->fetchAll();
        foreach($columns as $column) {
            echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
        }
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        
        // Create the table
        echo "\nCreating media_files table...\n";
        $sql = "CREATE TABLE IF NOT EXISTS `media_files` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `original_name` varchar(255) NOT NULL,
            `file_name` varchar(255) NOT NULL,
            `file_path` varchar(500) NOT NULL,
            `file_size` int(11) NOT NULL,
            `mime_type` varchar(100) NOT NULL,
            `description` text,
            `uploaded_by` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_uploaded_by` (`uploaded_by`),
            KEY `idx_mime_type` (`mime_type`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`uploaded_by`) REFERENCES `admin_users`(`id`) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($mysqli->query($sql)) {
            echo "✓ media_files table created successfully!\n";
        } else {
            echo "✗ Failed to create table: " . $mysqli->error . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}
?>
