<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Simulate login for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';

require_once 'admin/includes/db_config.php';
require_once 'admin/includes/auth.php';

$message = '';
$error = '';
$categories = [];

// Get categories
try {
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    echo "<p>✅ Got " . count($categories) . " categories</p>";
} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    echo "<p>❌ Categories error: $error</p>";
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Post - Admin</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .error { color: red; padding: 10px; background: #ffebee; border-radius: 4px; margin-bottom: 20px; }
        .success { color: green; padding: 10px; background: #e8f5e8; border-radius: 4px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <h1>Test Add New Post</h1>
    
    <?php if ($error): ?>
        <div class="error"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>
    
    <?php if ($message): ?>
        <div class="success"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
        
        <div class="form-group">
            <label for="title">Title *</label>
            <input type="text" id="title" name="title" required>
        </div>
        
        <div class="form-group">
            <label for="category_id">Category</label>
            <select id="category_id" name="category_id">
                <option value="">-- Select Category --</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>">
                        <?php echo htmlspecialchars($category['name']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="excerpt">Excerpt</label>
            <textarea id="excerpt" name="excerpt" rows="3" placeholder="Brief description of the post..."></textarea>
        </div>
        
        <div class="form-group">
            <label for="content">Content *</label>
            <textarea id="content" name="content" rows="10" required placeholder="Write your post content here..."></textarea>
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="draft">Draft</option>
                <option value="published">Published</option>
                <option value="archived">Archived</option>
            </select>
        </div>
        
        <button type="submit">Create Post</button>
        <a href="admin/posts.php" style="margin-left: 10px;">Back to Posts</a>
    </form>
    
    <hr>
    <h3>Debug Info:</h3>
    <p>Login Status: <?php echo isLoggedIn() ? 'LOGGED IN' : 'NOT LOGGED IN'; ?></p>
    <p>Categories Available: <?php echo count($categories); ?></p>
    <p>CSRF Token: <?php echo substr($csrf_token, 0, 20) . '...'; ?></p>
</body>
</html>
