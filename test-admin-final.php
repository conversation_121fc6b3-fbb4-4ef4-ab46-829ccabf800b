<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simulate login
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_full_name'] = 'Administrator';
$_SESSION['admin_email'] = '<EMAIL>';

echo "<h2>Admin System Final Test</h2>";

try {
    require_once 'admin/includes/db_config.php';
    require_once 'admin/includes/auth.php';
    
    echo "<p>✅ Auth and DB loaded successfully</p>";
    echo "<p>Login status: " . (isLoggedIn() ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";
    
    // Test database operations
    echo "<h3>Testing Database Operations...</h3>";
    
    // Test blog posts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM blog_posts");
    $result = $stmt->fetch();
    echo "<p>✅ Posts count: " . $result['total'] . "</p>";
    
    // Test categories
    $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
    echo "<p>✅ Categories: " . count($categories) . " available</p>";
    
    // Test admin user functions
    $admin = getCurrentAdmin();
    echo "<p>✅ Current admin: " . $admin['username'] . "</p>";
    
    // Test CSRF token
    $token = generateCSRFToken();
    echo "<p>✅ CSRF token generated</p>";
    
    echo "<h3>✅ All Systems Ready!</h3>";
    echo "<p>Admin pages should now work properly. You can:</p>";
    echo "<ol>";
    echo "<li><a href='admin/login.php' target='_blank'>Login to Admin</a> (admin/admin123)</li>";
    echo "<li><a href='admin/index.php' target='_blank'>View Dashboard</a></li>";
    echo "<li><a href='admin/posts.php' target='_blank'>Manage Posts</a></li>";
    echo "<li><a href='admin/add-post.php' target='_blank'>Add New Post</a></li>";
    echo "<li><a href='admin/edit-post.php?id=1' target='_blank'>Edit Post (ID: 1)</a></li>";
    echo "</ol>";
    
    echo "<hr>";
    echo "<h3>Note:</h3>";
    echo "<p>If pages are still blank, please make sure you're logged in first through the login page.</p>";
    echo "<p>The admin system requires authentication for all pages except login.php</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
}
?>
