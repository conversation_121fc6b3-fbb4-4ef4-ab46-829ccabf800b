<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .mobile-app-section { display: block; padding: 20px; background: #f0f0f0; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>JavaScript Fixes Test</h1>
    
    <div id="test-results"></div>
    
    <div class="mobile-app-section">
        <h3>Mobile App Section (for testing toggleMobileAppSection)</h3>
        <p>This section should be visible/hidden based on screen size.</p>
    </div>

    <div id="footer-placeholder"></div>

    <script src="js/contact-loader.js"></script>
    <script src="js/blog-loader.js"></script>
    
    <script>
        const results = document.getElementById('test-results');
        
        function addResult(test, success, message) {
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${test}:</strong> ${message}`;
            results.appendChild(div);
        }

        // Test 1: mobileAppSection function
        try {
            function toggleMobileAppSection() {
                const mobileAppSection = document.querySelector('.mobile-app-section');
                if (mobileAppSection) {
                    if (window.innerWidth <= 768) {
                        mobileAppSection.style.display = 'block';
                    } else {
                        mobileAppSection.style.display = 'none';
                    }
                }
            }
            
            toggleMobileAppSection();
            addResult('toggleMobileAppSection', true, 'Function defined and executed successfully');
        } catch (error) {
            addResult('toggleMobileAppSection', false, `Error: ${error.message}`);
        }

        // Test 2: API endpoints
        fetch('api/get-contact-info.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addResult('Contact API', true, 'API returns valid JSON data');
                } else {
                    addResult('Contact API', false, 'API returned error response');
                }
            })
            .catch(error => {
                addResult('Contact API', false, `Fetch error: ${error.message}`);
            });

        fetch('api/get-posts.php?limit=3')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addResult('Posts API', true, `API returns ${data.posts ? data.posts.length : 0} posts`);
                } else {
                    addResult('Posts API', false, 'API returned error response');
                }
            })
            .catch(error => {
                addResult('Posts API', false, `Fetch error: ${error.message}`);
            });

        // Test 3: Contact loader initialization
        setTimeout(() => {
            const footerContent = document.getElementById('footer-placeholder').innerHTML;
            if (footerContent && footerContent.trim() !== '') {
                addResult('Contact Loader', true, 'Footer content loaded successfully');
            } else {
                addResult('Contact Loader', false, 'Footer content not loaded');
            }
        }, 2000);
    </script>
</body>
</html>
