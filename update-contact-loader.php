<?php
/**
 * Update Contact Loader Script
 * Replaces old contact-loader.js with new simple-contact-loader.js
 * across all HTML files
 */

// List of HTML files to update
$htmlFiles = [
    'about.html',
    'news.html',
    'process.html',
    'services.html',
    'crypto-scam.html',
    'romance-scam.html',
    'phishing-scam.html',
    'wire-fraud.html',
    'digital-forensics.html',
    'bec-scam.html',
    'apply.html',
    'team.html',
    'testimonials.html'
];

$updatedFiles = [];
$errors = [];

foreach ($htmlFiles as $file) {
    if (!file_exists($file)) {
        $errors[] = "File not found: $file";
        continue;
    }
    
    try {
        // Read file content
        $content = file_get_contents($file);
        
        if ($content === false) {
            $errors[] = "Could not read file: $file";
            continue;
        }
        
        $originalContent = $content;
        
        // Check if file already has simple-contact-loader.js
        if (strpos($content, 'simple-contact-loader.js') !== false) {
            $errors[] = "File $file already has simple-contact-loader.js";
            continue;
        }
        
        // Add simple-contact-loader.js after main.js
        $content = str_replace(
            '<script src="js/main.js"></script>',
            '<script src="js/main.js"></script>' . "\n" . '  <script src="js/simple-contact-loader.js"></script>',
            $content
        );
        
        // Write back to file only if content changed
        if ($content !== $originalContent) {
            if (file_put_contents($file, $content) !== false) {
                $updatedFiles[] = $file;
            } else {
                $errors[] = "Could not write to file: $file";
            }
        } else {
            $errors[] = "No changes needed for: $file";
        }
        
    } catch (Exception $e) {
        $errors[] = "Error processing $file: " . $e->getMessage();
    }
}

// Output results
echo "<!DOCTYPE html>\n";
echo "<html><head><title>Contact Loader Update Results</title></head><body>\n";
echo "<h1>🔄 Contact Loader Update Results</h1>\n";

if (!empty($updatedFiles)) {
    echo "<h2>✅ Successfully Updated Files (" . count($updatedFiles) . "):</h2>\n";
    echo "<ul>\n";
    foreach ($updatedFiles as $file) {
        echo "<li><strong>$file</strong> - Added simple-contact-loader.js</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($errors)) {
    echo "<h2>ℹ️ Notes/Errors (" . count($errors) . "):</h2>\n";
    echo "<ul>\n";
    foreach ($errors as $error) {
        echo "<li>$error</li>\n";
    }
    echo "</ul>\n";
}

echo "<h2>📋 Summary:</h2>\n";
echo "<p><strong>Total files processed:</strong> " . count($htmlFiles) . "</p>\n";
echo "<p><strong>Successfully updated:</strong> " . count($updatedFiles) . "</p>\n";
echo "<p><strong>Notes/Errors:</strong> " . count($errors) . "</p>\n";

echo "<h2>🎯 What Was Added:</h2>\n";
echo "<p>Added <code>&lt;script src=\"js/simple-contact-loader.js\"&gt;&lt;/script&gt;</code> after main.js in each file.</p>\n";

echo "<h2>✅ Benefits:</h2>\n";
echo "<ul>\n";
echo "<li>Footer will now load contact information from admin/contact-settings.php</li>\n";
echo "<li>Database-driven contact information management</li>\n";
echo "<li>Emergency phone styling will work correctly</li>\n";
echo "<li>Fallback to demo data if API fails</li>\n";
echo "<li>Simple, reliable loading mechanism</li>\n";
echo "</ul>\n";

echo "<p><strong>Next Steps:</strong> Test the footer on any page. It should load contact information from the database and display emergency phone styling correctly.</p>\n";

echo "</body></html>\n";
?>
