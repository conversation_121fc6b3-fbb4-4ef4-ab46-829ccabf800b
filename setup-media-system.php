<?php
// Media Table Migration - Creates media_files table for file management
// Run this once to add the media_files table to your database

require_once 'admin/includes/db_config.php';

echo "<h2>🔧 Media Files Database Migration</h2>";

try {
    // Use the existing PDO connection from db_config.php
    global $pdo;
    
    if (!$pdo) {
        throw new Exception("Database connection not available");
    }
    
    echo "<p>✅ Database connection successful</p>";    
    // Check if media_files table exists
    $result = $pdo->query("SHOW TABLES LIKE 'media_files'");
    
    if ($result->rowCount() > 0) {
        echo "<p>⚠️ media_files table already exists</p>";
    } else {
        // Create media_files table
        $sql = "
        CREATE TABLE `media_files` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `original_name` varchar(255) NOT NULL,
            `file_name` varchar(255) NOT NULL,
            `file_path` varchar(500) NOT NULL,
            `file_size` int(11) NOT NULL,
            `mime_type` varchar(100) NOT NULL,
            `description` text,
            `uploaded_by` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_uploaded_by` (`uploaded_by`),
            KEY `idx_mime_type` (`mime_type`),
            KEY `idx_created_at` (`created_at`),
            FOREIGN KEY (`uploaded_by`) REFERENCES `admin_users`(`id`) ON DELETE RESTRICT
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        if ($pdo->exec($sql)) {
            echo "<p>✅ Successfully created media_files table</p>";
        } else {
            throw new Exception("Error creating table");
        }
    }
    
    // Create uploads directory with security measures
    $upload_dir = __DIR__ . '/admin/uploads/';
    if (!file_exists($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p>✅ Created uploads directory</p>";
            
            // Create .htaccess for security
            $htaccess_content = "Options -Indexes\nAllowOverride None\n<Files *.php>\nOrder Allow,Deny\nDeny from all\n</Files>\n";
            if (file_put_contents($upload_dir . '.htaccess', $htaccess_content)) {
                echo "<p>✅ Created security .htaccess file</p>";
            }
            
            // Create index.php to prevent directory listing
            $index_content = '<?php header("Location: ../"); exit(); ?>';
            if (file_put_contents($upload_dir . 'index.php', $index_content)) {
                echo "<p>✅ Created security index.php file</p>";
            }
        } else {
            echo "<p>⚠️ Could not create uploads directory - please create manually</p>";
        }
    } else {
        echo "<p>✅ Uploads directory already exists</p>";
    }
    
    $mysqli->close();
    
    echo "<h3>🎉 Media system setup complete!</h3>";
    echo "<p><a href='admin/media.php'>Go to Media Library</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}
?>

<style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
    h2 { color: #1e40af; }
    p { margin: 10px 0; }
    a { color: #1e40af; text-decoration: none; }
    a:hover { text-decoration: underline; }
</style>
