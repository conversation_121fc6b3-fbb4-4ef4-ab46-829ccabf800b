<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Footer Test - Forensic Involve</title>
    <link rel="stylesheet" href="css/modern.css?v=2025">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .test-content {
            flex: 1;
            padding: 2rem;
            background: #f8fafc;
        }
        
        .test-header {
            background: #1e40af;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .test-info {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .status-check {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
            padding: 1rem;
            background: #f0f9ff;
            border-radius: 8px;
            border-left: 4px solid #0ea5e9;
        }
        
        .status-check.success {
            background: #f0fdf4;
            border-left-color: #10b981;
        }
        
        .status-check.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        
        .status-check i {
            font-size: 1.2rem;
        }
        
        .success {
            color: #10b981;
        }
        
        .warning {
            color: #f59e0b;
        }
        
        .info {
            color: #0ea5e9;
        }
        
        .api-test {
            background: #f8fafc;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .console-log {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 6px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>🗄️ Database Footer Test</h1>
        <p>Testing footer with admin/contact-settings.php database integration</p>
    </div>
    
    <div class="test-content">
        <div class="test-info">
            <h2>🎯 Database Integration Test</h2>
            
            <div class="status-check info">
                <i class="fas fa-database info"></i>
                <div>
                    <strong>Database Connection</strong><br>
                    Footer loads contact information from site_settings table via API
                </div>
            </div>
            
            <div class="status-check info">
                <i class="fas fa-cog info"></i>
                <div>
                    <strong>Admin Control</strong><br>
                    Contact details managed through admin/contact-settings.php
                </div>
            </div>
            
            <div class="status-check warning">
                <i class="fas fa-shield-alt warning"></i>
                <div>
                    <strong>Fallback Protection</strong><br>
                    If API fails, footer shows demo data instead of "Loading..."
                </div>
            </div>
        </div>
        
        <div class="test-info">
            <h3>🔍 API Test</h3>
            <p>Testing the contact information API:</p>
            <div class="api-test" id="api-test-result">
                <strong>Testing API...</strong><br>
                <span id="api-status">Connecting to api/get-contact-info.php...</span>
            </div>
        </div>
        
        <div class="test-info">
            <h3>📋 Expected Behavior:</h3>
            <ul>
                <li><strong>Loading State:</strong> Footer shows "Loading..." initially</li>
                <li><strong>Database Success:</strong> Footer displays contact info from admin settings</li>
                <li><strong>Database Failure:</strong> Footer displays demo fallback data</li>
                <li><strong>Emergency Styling:</strong> Emergency phone highlighted in yellow/orange</li>
                <li><strong>Clickable Links:</strong> Phone and email addresses are clickable</li>
                <li><strong>Social Media:</strong> Links from admin settings or hidden if empty</li>
            </ul>
        </div>
        
        <div class="test-info">
            <h3>🔧 Admin Settings Integration:</h3>
            <p>To test the admin integration:</p>
            <ol>
                <li>Go to <strong>admin/contact-settings.php</strong></li>
                <li>Update any contact information</li>
                <li>Save the settings</li>
                <li>Refresh this page</li>
                <li>Footer should show your updated information</li>
            </ol>
        </div>
        
        <div class="test-info">
            <h3>📊 Console Logs</h3>
            <p>JavaScript console output (check browser dev tools for full logs):</p>
            <div class="console-log" id="console-output">
                Console logs will appear here...
            </div>
        </div>
    </div>
    
    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>
    
    <!-- Load the footer -->
    <script src="js/main.js"></script>
    <script src="js/simple-contact-loader.js"></script>
    
    <script>
        // Test the API directly
        async function testAPI() {
            const apiStatus = document.getElementById('api-status');
            const apiResult = document.getElementById('api-test-result');
            
            try {
                apiStatus.textContent = 'Fetching from api/get-contact-info.php...';
                
                const response = await fetch('api/get-contact-info.php');
                const data = await response.json();
                
                if (data.success) {
                    apiStatus.innerHTML = '<span style="color: #10b981;">✅ API Success!</span>';
                    apiResult.innerHTML = `
                        <strong>✅ API Response:</strong><br>
                        <strong>General Phone:</strong> ${data.data.phones.general}<br>
                        <strong>Emergency Phone:</strong> ${data.data.phones.emergency}<br>
                        <strong>General Email:</strong> ${data.data.emails.general}<br>
                        <strong>Primary Address:</strong> ${data.data.addresses.primary}<br>
                        <strong>Environment:</strong> ${data.debug.environment}<br>
                        <strong>Database:</strong> ${data.debug.database}
                    `;
                } else {
                    throw new Error('API returned unsuccessful response');
                }
                
            } catch (error) {
                apiStatus.innerHTML = '<span style="color: #ef4444;">❌ API Failed</span>';
                apiResult.innerHTML = `
                    <strong>❌ API Error:</strong><br>
                    ${error.message}<br>
                    <em>Footer will use fallback demo data</em>
                `;
            }
        }
        
        // Capture console logs
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const logEntry = args.join(' ');
            if (logEntry.includes('Simple Contact Loader') || logEntry.includes('Footer') || logEntry.includes('contact')) {
                consoleOutput.innerHTML += logEntry + '<br>';
                consoleOutput.scrollTop = consoleOutput.scrollHeight;
            }
        };
        
        // Run tests
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Database Footer Test: Starting tests...');
            
            // Test API
            testAPI();
            
            // Monitor footer loading
            setTimeout(() => {
                const footer = document.querySelector('footer');
                const contactItems = document.querySelectorAll('.contact-item');
                const emergencyPhone = document.querySelector('.emergency-phone');
                
                console.log(`📊 Test Results: Footer found: ${!!footer}, Contact items: ${contactItems.length}, Emergency phone styling: ${!!emergencyPhone}`);
                
                if (footer && contactItems.length > 0) {
                    console.log('✅ Footer loaded successfully with contact information');
                    
                    // Check for loading text
                    const loadingItems = Array.from(contactItems).filter(item => 
                        item.textContent.includes('Loading')
                    );
                    
                    if (loadingItems.length > 0) {
                        console.log('⚠️ Some items still showing "Loading..." text');
                    } else {
                        console.log('✅ All contact information loaded successfully');
                    }
                } else {
                    console.log('❌ Footer or contact items not found');
                }
            }, 3000);
        });
    </script>
</body>
</html>
