<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Global Recovery</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0A74DA" offset="0%"></stop>
            <stop stop-color="#003366" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFC107" offset="0%"></stop>
            <stop stop-color="#FD7E14" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Global-Recovery" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="#F8F9FA" cx="400" cy="300" r="250"></circle>
        
        <!-- Globe -->
        <g id="Globe" transform="translate(250.000000, 150.000000)">
            <circle id="Globe-Base" fill="url(#linearGradient-1)" opacity="0.2" cx="150" cy="150" r="150"></circle>
            
            <!-- Latitude Lines -->
            <ellipse id="Latitude-1" stroke="url(#linearGradient-1)" stroke-width="2" cx="150" cy="150" rx="150" ry="30"></ellipse>
            <ellipse id="Latitude-2" stroke="url(#linearGradient-1)" stroke-width="2" cx="150" cy="150" rx="120" ry="60"></ellipse>
            <ellipse id="Latitude-3" stroke="url(#linearGradient-1)" stroke-width="2" cx="150" cy="150" rx="80" ry="100"></ellipse>
            
            <!-- Longitude Lines -->
            <path d="M150,0 C150,0 150,300 150,300" id="Longitude-1" stroke="url(#linearGradient-1)" stroke-width="2"></path>
            <path d="M0,150 C0,150 300,150 300,150" id="Longitude-2" stroke="url(#linearGradient-1)" stroke-width="2"></path>
            <path d="M44,44 C44,44 256,256 256,256" id="Longitude-3" stroke="url(#linearGradient-1)" stroke-width="2"></path>
            <path d="M44,256 C44,256 256,44 256,44" id="Longitude-4" stroke="url(#linearGradient-1)" stroke-width="2"></path>
        </g>
        
        <!-- Money Path -->
        <g id="Money-Path" transform="translate(200.000000, 150.000000)">
            <path d="M50,150 C100,100 150,200 200,100 C250,0 300,50 350,100 C400,150 450,100 500,150" id="Path" stroke="url(#linearGradient-2)" stroke-width="4" stroke-dasharray="8,8"></path>
            
            <!-- Start Point -->
            <circle id="Start" fill="#FFFFFF" stroke="url(#linearGradient-1)" stroke-width="3" cx="50" cy="150" r="15"></circle>
            
            <!-- End Point -->
            <circle id="End" fill="#FFFFFF" stroke="url(#linearGradient-2)" stroke-width="3" cx="500" cy="150" r="15"></circle>
            
            <!-- Money Icons -->
            <g id="Money-1" transform="translate(200.000000, 100.000000)">
                <circle fill="#FFFFFF" cx="20" cy="20" r="20"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="url(#linearGradient-2)">
                    <tspan x="10" y="28">$</tspan>
                </text>
            </g>
            
            <g id="Money-2" transform="translate(350.000000, 100.000000)">
                <circle fill="#FFFFFF" cx="20" cy="20" r="20"></circle>
                <text font-family="Arial-BoldMT, Arial" font-size="24" font-weight="bold" fill="url(#linearGradient-2)">
                    <tspan x="10" y="28">$</tspan>
                </text>
            </g>
        </g>
        
        <!-- Locations -->
        <g id="Locations" transform="translate(200.000000, 150.000000)">
            <!-- Location 1 -->
            <g id="Location-1" transform="translate(50.000000, 150.000000)">
                <path d="M0,-30 C0,-30 -15,-10 -15,0 C-15,15 0,30 0,30 C0,30 15,15 15,0 C15,-10 0,-30 0,-30 Z" fill="url(#linearGradient-1)"></path>
                <circle fill="#FFFFFF" cx="0" cy="0" r="5"></circle>
            </g>
            
            <!-- Location 2 -->
            <g id="Location-2" transform="translate(500.000000, 150.000000)">
                <path d="M0,-30 C0,-30 -15,-10 -15,0 C-15,15 0,30 0,30 C0,30 15,15 15,0 C15,-10 0,-30 0,-30 Z" fill="url(#linearGradient-2)"></path>
                <circle fill="#FFFFFF" cx="0" cy="0" r="5"></circle>
            </g>
        </g>
        
        <!-- Magnifying Glass -->
        <g id="Magnifying-Glass" transform="translate(150.000000, 350.000000)">
            <circle id="Glass" stroke="#343A40" stroke-width="5" fill="#FFFFFF" opacity="0.7" cx="30" cy="30" r="25"></circle>
            <line x1="50" y1="50" x2="70" y2="70" id="Handle" stroke="#343A40" stroke-width="8" stroke-linecap="round"></line>
        </g>
        
        <!-- Shield -->
        <g id="Shield" transform="translate(550.000000, 350.000000)">
            <path d="M50,0 C50,0 90,15 90,35 L90,65 C90,90 50,100 50,100 C50,100 10,90 10,65 L10,35 C10,15 50,0 50,0 Z" id="Shield-Base" fill="url(#linearGradient-1)"></path>
            <path d="M50,10 C50,10 80,22 80,35 L80,65 C80,85 50,90 50,90 C50,90 20,85 20,65 L20,35 C20,22 50,10 50,10 Z" id="Shield-Inner" fill="#FFFFFF"></path>
            <text font-family="Arial-BoldMT, Arial" font-size="36" font-weight="bold" fill="url(#linearGradient-1)">
                <tspan x="38" y="65">✓</tspan>
            </text>
        </g>
        
        <!-- Binary Background -->
        <g id="Binary" transform="translate(200.000000, 100.000000)" fill="#343A40" opacity="0.05">
            <text font-family="Courier" font-size="10" font-weight="normal">
                <tspan x="0" y="10">01001010 01101111 01101000 01101110</tspan>
                <tspan x="0" y="25">01000100 01101111 01100101 00100000</tspan>
                <tspan x="0" y="40">01010011 01100101 01100011 01110101</tspan>
                <tspan x="0" y="55">01110010 01101001 01110100 01111001</tspan>
                <tspan x="0" y="70">01010000 01110010 01101111 01110100</tspan>
                <tspan x="0" y="85">01100101 01100011 01110100 01101001</tspan>
                <tspan x="0" y="100">01101111 01101110 00100000 01000100</tspan>
                <tspan x="0" y="115">01100001 01110100 01100001 00100000</tspan>
                <tspan x="0" y="130">01010011 01100001 01100110 01100101</tspan>
                <tspan x="0" y="145">01000111 01110101 01100001 01110010</tspan>
                <tspan x="0" y="160">01100100 01101001 01100001 01101110</tspan>
                <tspan x="0" y="175">01010011 01100101 01100011 01110101</tspan>
                <tspan x="0" y="190">01110010 01100101 00100000 01000100</tspan>
                <tspan x="0" y="205">01100001 01110100 01100001 00100000</tspan>
                <tspan x="0" y="220">01010000 01110010 01101111 01110100</tspan>
                <tspan x="0" y="235">01100101 01100011 01110100 01101001</tspan>
                <tspan x="0" y="250">01101111 01101110 00100000 01010011</tspan>
                <tspan x="0" y="265">01111001 01110011 01110100 01100101</tspan>
                <tspan x="0" y="280">01101101 01110011 00100000 01010011</tspan>
                <tspan x="0" y="295">01100001 01100110 01100101 01110100</tspan>
                <tspan x="0" y="310">01111001 00100000 01000110 01101001</tspan>
                <tspan x="0" y="325">01110010 01110011 01110100 00100000</tspan>
            </text>
        </g>
    </g>
</svg>