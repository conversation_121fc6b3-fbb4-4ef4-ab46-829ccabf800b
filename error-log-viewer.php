<?php
session_start();
require_once 'admin/includes/db_config.php';
require_once 'admin/includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: admin/login.php');
    exit();
}

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

$error_log_file = __DIR__ . '/error.log';
$php_error_log = ini_get('error_log');
$apache_error_log = 'C:/MAMP/logs/apache_error.log'; // Typical MAMP location

function formatLogEntry($line) {
    // Format log entries for better readability
    $patterns = [
        '/\[([^\]]+)\]/' => '<span class="timestamp">[$1]</span>',
        '/PHP (Fatal error|Parse error|Warning|Notice):/' => '<span class="error-type">PHP $1:</span>',
        '/Stack trace:/' => '<span class="stack-trace">Stack trace:</span>',
        '/in ([^\s]+) on line (\d+)/' => 'in <span class="file-path">$1</span> on line <span class="line-number">$2</span>'
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $line = preg_replace($pattern, $replacement, $line);
    }
    
    return $line;
}

function readLogFile($file, $lines = 50) {
    if (!file_exists($file) || !is_readable($file)) {
        return ["Log file not found or not readable: $file"];
    }
    
    $content = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($content === false) {
        return ["Unable to read log file: $file"];
    }
    
    return array_slice(array_reverse($content), 0, $lines);
}

$action = $_GET['action'] ?? 'view';

if ($action === 'clear' && $_POST) {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $log_to_clear = $_POST['log_file'] ?? '';
        if (in_array($log_to_clear, [$error_log_file, $php_error_log, $apache_error_log])) {
            file_put_contents($log_to_clear, '');
            $message = "Log file cleared successfully.";
        }
    }
}

$csrf_token = generateCSRFToken();
$current_user = getCurrentAdmin();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Log Viewer - Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Manrope', sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .log-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .log-header {
            padding: 1rem 2rem;
            background: #1e40af;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .log-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 1rem;
            background: #1a1a1a;
            color: #e5e5e5;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 4px;
            border-left: 3px solid transparent;
        }
        
        .log-entry.error {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .log-entry.warning {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        
        .timestamp { color: #10b981; }
        .error-type { color: #ef4444; font-weight: bold; }
        .file-path { color: #3b82f6; }
        .line-number { color: #f59e0b; font-weight: bold; }
        .stack-trace { color: #8b5cf6; }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1e40af;
        }
        
        .actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Error Log Viewer</h1>
            <p>Monitor system errors and debugging information</p>
            <div style="margin-top: 1rem;">
                <a href="admin/index.php" class="btn btn-secondary">← Back to Admin</a>
                <a href="?action=refresh" class="btn btn-primary">🔄 Refresh Logs</a>
            </div>
        </div>

        <?php if (isset($message)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?></div>
        <?php endif; ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo count(readLogFile($error_log_file)); ?></div>
                <div>Local Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo file_exists($php_error_log) ? count(readLogFile($php_error_log)) : 0; ?></div>
                <div>PHP Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo file_exists($apache_error_log) ? count(readLogFile($apache_error_log)) : 0; ?></div>
                <div>Apache Errors</div>
            </div>
        </div>

        <!-- Local Error Log -->
        <div class="log-section">
            <div class="log-header">
                <h3>📝 Local Error Log</h3>
                <div class="actions">
                    <form method="POST" action="?action=clear" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                        <input type="hidden" name="log_file" value="<?php echo htmlspecialchars($error_log_file, ENT_QUOTES, 'UTF-8'); ?>">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Clear this log file?')">Clear Log</button>
                    </form>
                </div>
            </div>
            <div class="log-content">
                <?php 
                $local_logs = readLogFile($error_log_file);
                if (empty($local_logs) || $local_logs[0] === "Log file not found or not readable: $error_log_file"):
                ?>
                    <div class="log-entry">No local errors found.</div>
                <?php else: ?>
                    <?php foreach ($local_logs as $line): ?>
                        <div class="log-entry <?php echo (strpos($line, 'Fatal error') !== false || strpos($line, 'Parse error') !== false) ? 'error' : (strpos($line, 'Warning') !== false ? 'warning' : ''); ?>">
                            <?php echo formatLogEntry(htmlspecialchars($line, ENT_QUOTES, 'UTF-8')); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- PHP Error Log -->
        <div class="log-section">
            <div class="log-header">
                <h3>🐘 PHP Error Log</h3>
                <div class="actions">
                    <?php if (file_exists($php_error_log)): ?>
                        <form method="POST" action="?action=clear" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="log_file" value="<?php echo htmlspecialchars($php_error_log, ENT_QUOTES, 'UTF-8'); ?>">
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Clear this log file?')">Clear Log</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            <div class="log-content">
                <?php 
                $php_logs = readLogFile($php_error_log);
                if (empty($php_logs) || strpos($php_logs[0], 'not found') !== false):
                ?>
                    <div class="log-entry">No PHP errors found or log file not accessible.</div>
                <?php else: ?>
                    <?php foreach ($php_logs as $line): ?>
                        <div class="log-entry <?php echo (strpos($line, 'Fatal error') !== false || strpos($line, 'Parse error') !== false) ? 'error' : (strpos($line, 'Warning') !== false ? 'warning' : ''); ?>">
                            <?php echo formatLogEntry(htmlspecialchars($line, ENT_QUOTES, 'UTF-8')); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Apache Error Log -->
        <div class="log-section">
            <div class="log-header">
                <h3>🌐 Apache Error Log</h3>
                <div class="actions">
                    <?php if (file_exists($apache_error_log)): ?>
                        <form method="POST" action="?action=clear" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token, ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="log_file" value="<?php echo htmlspecialchars($apache_error_log, ENT_QUOTES, 'UTF-8'); ?>">
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Clear this log file?')">Clear Log</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            <div class="log-content">
                <?php 
                $apache_logs = readLogFile($apache_error_log);
                if (empty($apache_logs) || strpos($apache_logs[0], 'not found') !== false):
                ?>
                    <div class="log-entry">No Apache errors found or log file not accessible.</div>
                <?php else: ?>
                    <?php foreach ($apache_logs as $line): ?>
                        <div class="log-entry <?php echo (strpos($line, 'error') !== false) ? 'error' : (strpos($line, 'warn') !== false ? 'warning' : ''); ?>">
                            <?php echo formatLogEntry(htmlspecialchars($line, ENT_QUOTES, 'UTF-8')); ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh every 30 seconds
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
