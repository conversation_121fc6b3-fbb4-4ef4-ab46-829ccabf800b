<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Business Email Compromise Recovery - Forensic Involve | Expert BEC Investigation</title>
  <meta name="description" content="Expert Business Email Compromise (BEC) recovery services. Our specialized team helps businesses recover from CEO fraud, vendor impersonation, and wire transfer fraud through advanced investigation.">

  <!-- Favicon -->
  <link rel="icon" href="assets/svg/logo.svg" type="image/svg+xml">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/modern.css?v=2025">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- Header Placeholder -->
  <div id="header-placeholder"></div>

  <main>
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container" style="padding-top: 90px;">
        <div class="row">
          <div class="col-6">
            <div class="hero-content">
              <div class="hero-badge">Business Email Compromise Recovery</div>
              <h1>Expert <span class="highlight">Business Email Compromise</span> Recovery Services</h1>
              <p class="hero-description">Our specialized BEC recovery team helps businesses recover from CEO fraud, vendor impersonation, and wire transfer scams. With rapid response protocols and advanced investigation techniques, we minimize losses and secure business communications.</p>
              <div class="hero-cta">
                <a href="contact.html" class="btn btn-primary btn-lg">
                  <i class="fas fa-building"></i>
                  Emergency Response
                </a>
                <a href="#process" class="btn btn-secondary btn-lg">
                  <i class="fas fa-search"></i>
                  Our Process
                </a>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="hero-image">
              <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background elements -->
                <circle cx="400" cy="80" r="60" fill="#fef2f2" opacity="0.6"/>
                <circle cx="100" cy="320" r="40" fill="#dbeafe" opacity="0.8"/>
                
                <!-- Central BEC attack illustration -->
                <g transform="translate(250, 200)">
                  <!-- Fake CEO email -->
                  <rect x="-35" y="-25" width="70" height="50" rx="5" fill="#fef2f2" opacity="0.9"/>
                  <rect x="-30" y="-20" width="60" height="6" fill="#ef4444" opacity="0.7"/>
                  <rect x="-30" y="-10" width="45" height="4" fill="#ef4444" opacity="0.5"/>
                  <rect x="-30" y="-4" width="50" height="4" fill="#ef4444" opacity="0.5"/>
                  <rect x="-30" y="2" width="35" height="4" fill="#ef4444" opacity="0.5"/>
                  <rect x="-30" y="8" width="40" height="4" fill="#ef4444" opacity="0.5"/>
                  <!-- Crown (fake CEO) -->
                  <path d="M-10 -30 L-5 -35 L0 -30 L5 -35 L10 -30 L5 -25 L-5 -25 Z" fill="#f59e0b"/>
                  <text x="0" y="-40" text-anchor="middle" fill="white" font-size="10">Fake CEO</text>
                </g>
                
                <!-- Employee/victim -->
                <g transform="translate(120, 120)">
                  <circle cx="0" cy="0" r="25" fill="#dbeafe" opacity="0.9"/>
                  <circle cx="0" cy="-5" r="8" fill="#1e40af"/>
                  <path d="M-12 12 C-12 8 -8 6 0 6 C8 6 12 8 12 12" fill="#1e40af"/>
                  <text x="0" y="-35" text-anchor="middle" fill="white" font-size="10">Employee</text>
                </g>
                
                <!-- Wire transfer -->
                <g transform="translate(380, 280)">
                  <circle cx="0" cy="0" r="25" fill="#fef3c7" opacity="0.9"/>
                  <rect x="-8" y="-6" width="16" height="12" rx="2" fill="#f59e0b"/>
                  <text x="0" y="2" text-anchor="middle" fill="white" font-size="8">$</text>
                  <path d="M25 0 L45 0 M41 -4 L45 0 L41 4" stroke="#ef4444" stroke-width="2" fill="none"/>
                  <text x="0" y="40" text-anchor="middle" fill="white" font-size="10">Wire Transfer</text>
                </g>
                
                <!-- Investigation/recovery -->
                <g transform="translate(150, 300)">
                  <circle cx="0" cy="0" r="25" fill="#f0fdf4" opacity="0.9"/>
                  <circle cx="-3" cy="-3" r="8" stroke="#10b981" stroke-width="2" fill="none"/>
                  <path d="M5 5 L12 12" stroke="#10b981" stroke-width="2" stroke-linecap="round"/>
                  <text x="0" y="40" text-anchor="middle" fill="white" font-size="10">Investigation</text>
                </g>
                
                <!-- Email security -->
                <g transform="translate(80, 200)">
                  <circle cx="0" cy="0" r="20" fill="#f3e8ff" opacity="0.9"/>
                  <rect x="-8" y="-4" width="16" height="8" rx="2" stroke="#8b5cf6" stroke-width="2" fill="none"/>
                  <path d="M-8 -4 L0 2 L8 -4" stroke="#8b5cf6" stroke-width="2" fill="none"/>
                </g>
                
                <!-- Business network -->
                <g transform="translate(350, 150)">
                  <circle cx="0" cy="0" r="20" fill="#dbeafe" opacity="0.9"/>
                  <rect x="-6" y="-4" width="12" height="8" rx="1" fill="#1e40af"/>
                  <circle cx="-8" cy="-8" r="2" fill="#1e40af"/>
                  <circle cx="8" cy="-8" r="2" fill="#1e40af"/>
                  <circle cx="-8" cy="8" r="2" fill="#1e40af"/>
                  <circle cx="8" cy="8" r="2" fill="#1e40af"/>
                  <line x1="-6" y1="-6" x2="-6" y2="-4" stroke="#1e40af" stroke-width="1"/>
                  <line x1="6" y1="-6" x2="6" y2="-4" stroke="#1e40af" stroke-width="1"/>
                  <line x1="-6" y1="4" x2="-6" y2="6" stroke="#1e40af" stroke-width="1"/>
                  <line x1="6" y1="4" x2="6" y2="6" stroke="#1e40af" stroke-width="1"/>
                </g>
                
                <!-- Attack flow lines -->
                <path d="M145 135 Q200 160 215 185" stroke="#ef4444" stroke-width="2" fill="none" opacity="0.8" stroke-dasharray="4,4"/>
                <path d="M285 185 Q330 200 355 265" stroke="#ef4444" stroke-width="2" fill="none" opacity="0.6" stroke-dasharray="4,4"/>
                
                <!-- Recovery/protection path -->
                <path d="M355 295 Q280 320 175 315" stroke="#10b981" stroke-width="3" fill="none" opacity="0.8"/>
                <path d="M125 315 Q100 280 100 220" stroke="#1e40af" stroke-width="3" fill="none" opacity="0.8"/>
                
                <!-- Success indicators -->
                <g transform="translate(50, 80)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>
                
                <g transform="translate(450, 320)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>
                
                <!-- Decorative elements -->
                <circle cx="30" cy="30" r="2" fill="#ef4444" opacity="0.4"/>
                <circle cx="470" cy="50" r="3" fill="#1e40af" opacity="0.5"/>
                <circle cx="20" cy="380" r="2" fill="#10b981" opacity="0.6"/>
                <circle cx="480" cy="370" r="2" fill="#f59e0b" opacity="0.4"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Service Overview Section -->
    <section class="section" style="background: white;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Understanding Business Email Compromise</h2>
          <p class="section-subtitle" style="margin: 0;">Business Email Compromise (BEC) is a sophisticated scam targeting businesses that conduct wire transfers and have suppliers abroad, resulting in billions in losses annually.</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; margin-top: 3rem; align-items: center;">
          <div>
            <h3 style="color: #111827; margin-bottom: 1.5rem; font-size: 1.4rem;">How BEC Attacks Work</h3>
            <p style="color: #6b7280; line-height: 1.7; margin-bottom: 1.5rem;">BEC scammers research companies and executives through social media and company websites. They then impersonate CEOs, vendors, or business partners through spoofed emails or compromised accounts to request urgent wire transfers or sensitive information.</p>
            <p style="color: #6b7280; line-height: 1.7; margin-bottom: 1.5rem;">Common tactics include CEO fraud (fake executive requests), vendor impersonation (fake invoice changes), attorney impersonation (fake legal requests), and account compromise (hijacked email accounts). These attacks often target finance departments during busy periods.</p>
            <p style="color: #6b7280; line-height: 1.7; margin: 0;">Our rapid response team specializes in immediate containment, forensic investigation, and coordination with financial institutions and law enforcement to maximize fund recovery and prevent future attacks.</p>
          </div>
          <div style="text-align: center;">
            <div style="background: #f0f8ff; padding: 3rem; border-radius: 1rem;">
              <div style="font-size: 3rem; color: #1e40af; font-weight: 800; margin-bottom: 1rem;">$2.9B</div>
              <div style="color: #6b7280; font-size: 1rem;">Lost to BEC Scams in 2023</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Warning Signs Section -->
    <section class="section" style="background: #f8fafc;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Warning Signs of Business Email Compromise</h2>
          <p class="section-subtitle" style="margin: 0;">Recognize these red flags to protect your business from BEC attacks and wire transfer fraud.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 3rem; margin-top: 3rem;">
          <!-- Warning Sign 1 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 9V13" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 17H12.01" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Urgent Wire Transfer Requests</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Unexpected emails from executives or vendors requesting immediate wire transfers, especially with unusual urgency, secrecy requirements, or pressure to bypass normal approval processes.</p>
          </div>

          <!-- Warning Sign 2 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 9V13" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 17H12.01" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
                <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="#f59e0b" stroke-width="2"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Unusual Email Behavior</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Emails from known contacts with slight differences in email addresses, unusual language or tone, requests to communicate via personal email, or reluctance to discuss over phone.</p>
          </div>

          <!-- Warning Sign 3 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
                <path d="M15 9L9 15" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <path d="M9 9L15 15" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Changed Payment Instructions</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Vendors or business partners suddenly requesting changes to payment methods, bank account details, or wire transfer instructions, especially without prior notice or verification.</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer Placeholder -->
  <div id="footer-placeholder"></div>

  <!-- JavaScript -->
  <script src="js/main.js"></script>
  <script src="js/simple-contact-loader.js"></script>
</body>
</html>
