<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Integration Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            margin: 20px; 
            background: #f8fafc;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result { 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px; 
            font-weight: 500;
        }
        .success { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        .error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .loading { background: #fef3c7; color: #92400e; border: 1px solid #fde68a; }
        .admin-links { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
            margin-top: 20px;
        }
        .admin-link { 
            display: block; 
            padding: 15px; 
            background: #1e40af; 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .admin-link:hover { background: #1d4ed8; }
        .blog-preview { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
            gap: 20px; 
            margin-top: 20px;
        }
        .blog-post-preview { 
            padding: 15px; 
            border: 1px solid #e5e7eb; 
            border-radius: 6px; 
            background: #fafafa;
        }
        .blog-post-preview h3 { margin: 0 0 10px 0; color: #1e40af; font-size: 16px; }
        .blog-post-preview p { margin: 0; color: #6b7280; font-size: 14px; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
            margin-top: 15px;
        }
        .stat-card { 
            padding: 15px; 
            background: linear-gradient(135deg, #1e40af, #3b82f6); 
            color: white; 
            border-radius: 6px; 
            text-align: center;
        }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 14px; opacity: 0.9; }
        code { 
            background: #f1f5f9; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Forensic Involve - System Integration Test</h1>
        <p>Testing all components of the admin dashboard and blog system...</p>

        <div class="test-section">
            <h2>📊 System Status</h2>
            <div id="system-status" class="test-result loading">Running system tests...</div>
            <div class="stats" id="stats-container"></div>
        </div>

        <div class="test-section">
            <h2>🔌 API Connectivity</h2>
            <div id="api-status" class="test-result loading">Testing API endpoints...</div>
        </div>

        <div class="test-section">
            <h2>📝 Blog Content</h2>
            <div id="blog-status" class="test-result loading">Loading blog posts...</div>
            <div class="blog-preview" id="blog-preview"></div>
        </div>

        <div class="test-section">
            <h2>🛡️ Admin Dashboard</h2>
            <div id="admin-status" class="test-result loading">Checking admin access...</div>
            <div class="admin-links">
                <a href="/foresensic/admin/login.php" class="admin-link" target="_blank">
                    🔐 Admin Login<br><small>Username: admin | Password: admin123</small>
                </a>
                <a href="/foresensic/admin/index.php" class="admin-link" target="_blank">
                    📊 Dashboard
                </a>
                <a href="/foresensic/admin/posts.php" class="admin-link" target="_blank">
                    📝 Manage Posts
                </a>
                <a href="/foresensic/admin/add-post.php" class="admin-link" target="_blank">
                    ➕ Add New Post
                </a>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 Frontend Pages</h2>
            <div id="frontend-status" class="test-result loading">Testing frontend integration...</div>
            <div class="admin-links">
                <a href="/foresensic/index.html" class="admin-link" target="_blank">
                    🏠 Homepage<br><small>Dynamic blog section</small>
                </a>
                <a href="/foresensic/news.html" class="admin-link" target="_blank">
                    📰 News Page<br><small>Full blog listing</small>
                </a>
                <a href="/foresensic/api/get-posts.php" class="admin-link" target="_blank">
                    🔗 API Endpoint<br><small>JSON data feed</small>
                </a>
            </div>
        </div>
    </div>

    <script>
        // Test results
        const results = {
            api: false,
            blog: false,
            admin: false,
            frontend: false
        };

        // Update status function
        function updateStatus(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `test-result ${success ? 'success' : 'error'}`;
            element.innerHTML = `${success ? '✅' : '❌'} ${message}`;
        }

        // Test API
        fetch('/foresensic/api/get-posts.php')
            .then(response => {
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                return response.json();
            })
            .then(data => {
                results.api = true;
                results.blog = data.posts && data.posts.length > 0;
                
                updateStatus('api-status', true, 
                    `API working perfectly! Found ${data.posts.length} blog posts`);
                
                if (results.blog) {
                    updateStatus('blog-status', true, 
                        `Blog system operational with ${data.posts.length} published posts`);
                    
                    // Display blog preview
                    const preview = document.getElementById('blog-preview');
                    preview.innerHTML = data.posts.slice(0, 3).map(post => `
                        <div class="blog-post-preview">
                            <h3>${post.title}</h3>
                            <p><strong>Category:</strong> ${post.category}</p>
                            <p><strong>Author:</strong> ${post.author}</p>
                            <p><strong>Date:</strong> ${post.formatted_date}</p>
                            <p>${post.excerpt.substring(0, 150)}...</p>
                        </div>
                    `).join('');
                } else {
                    updateStatus('blog-status', false, 'No blog posts found in database');
                }

                // Update stats
                document.getElementById('stats-container').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${data.posts.length}</div>
                        <div class="stat-label">Published Posts</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.total_pages}</div>
                        <div class="stat-label">Total Pages</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${new Set(data.posts.map(p => p.category)).size}</div>
                        <div class="stat-label">Categories</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${new Set(data.posts.map(p => p.author)).size}</div>
                        <div class="stat-label">Authors</div>
                    </div>
                `;
            })
            .catch(error => {
                updateStatus('api-status', false, `API Error: ${error.message}`);
                updateStatus('blog-status', false, 'Blog system unavailable due to API error');
            });

        // Test admin access (just check if login page loads)
        fetch('/foresensic/admin/login.php')
            .then(response => {
                results.admin = response.ok;
                updateStatus('admin-status', response.ok, 
                    response.ok ? 'Admin system accessible - Ready for login' : 'Admin system unavailable');
            })
            .catch(error => {
                updateStatus('admin-status', false, `Admin Error: ${error.message}`);
            });

        // Test frontend pages
        Promise.all([
            fetch('/foresensic/index.html'),
            fetch('/foresensic/news.html')
        ]).then(responses => {
            const allOk = responses.every(r => r.ok);
            results.frontend = allOk;
            updateStatus('frontend-status', allOk, 
                allOk ? 'Frontend pages loading successfully' : 'Some frontend pages have issues');
        }).catch(error => {
            updateStatus('frontend-status', false, `Frontend Error: ${error.message}`);
        });

        // Update overall system status after tests
        setTimeout(() => {
            const overallSuccess = Object.values(results).every(r => r);
            const successCount = Object.values(results).filter(r => r).length;
            
            updateStatus('system-status', overallSuccess, 
                overallSuccess 
                    ? 'All systems operational! 🎉 Admin dashboard ready for use.' 
                    : `${successCount}/4 systems working. Check failed components above.`);
        }, 3000);
    </script>
</body>
</html>
