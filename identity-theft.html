<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Identity Theft Recovery - Forensic Involve | Expert Identity Restoration Services</title>
  <meta name="description" content="Complete identity theft recovery and restoration services. Our specialized team helps victims restore their identity, secure personal information, and prevent future unauthorized access.">

  <!-- Favicon -->
  <link rel="icon" href="assets/svg/logo.svg" type="image/svg+xml">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&display=swap" rel="stylesheet">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="css/modern.css">

  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
  <!-- Header Placeholder -->
  <div id="header-placeholder"></div>

  <main>
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container" style="padding-top: 90px;">
        <div class="row">
          <div class="col-6">
            <div class="hero-content">
              <div class="hero-badge">Identity Theft Recovery</div>
              <h1>Complete <span class="highlight">Identity Theft</span> Recovery Services</h1>
              <p class="hero-description">Our comprehensive identity restoration team helps victims recover from identity theft, restore credit reports, secure personal information, and implement protection measures to prevent future unauthorized access to accounts and personal data.</p>
              <div class="hero-cta">
                <a href="contact.html" class="btn btn-primary btn-lg">
                  <i class="fas fa-user-shield"></i>
                  Start Recovery
                </a>
                <a href="#process" class="btn btn-secondary btn-lg">
                  <i class="fas fa-shield-alt"></i>
                  Our Process
                </a>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="hero-image">
              <svg width="500" height="400" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <!-- Background elements -->
                <circle cx="400" cy="80" r="60" fill="#f3e8ff" opacity="0.6"/>
                <circle cx="100" cy="320" r="40" fill="#dbeafe" opacity="0.8"/>
                
                <!-- Central identity theft illustration -->
                <g transform="translate(250, 200)">
                  <!-- Stolen identity card -->
                  <rect x="-30" y="-20" width="60" height="40" rx="5" fill="#fef2f2" opacity="0.9"/>
                  <circle cx="-15" cy="-5" r="8" fill="#ef4444" opacity="0.7"/>
                  <rect x="0" y="-10" width="25" height="3" fill="#ef4444" opacity="0.6"/>
                  <rect x="0" y="-5" width="20" height="3" fill="#ef4444" opacity="0.6"/>
                  <rect x="0" y="0" width="22" height="3" fill="#ef4444" opacity="0.6"/>
                  <rect x="0" y="5" width="18" height="3" fill="#ef4444" opacity="0.6"/>
                  <!-- Theft indicator -->
                  <path d="M-35 -25 L-40 -30 M-35 -15 L-40 -10" stroke="#ef4444" stroke-width="2"/>
                  <text x="0" y="-35" text-anchor="middle" fill="white" font-size="10">Stolen Identity</text>
                </g>
                
                <!-- Personal data breach -->
                <g transform="translate(120, 120)">
                  <rect x="-20" y="-15" width="40" height="30" rx="3" fill="#fef2f2" opacity="0.9"/>
                  <rect x="-15" y="-10" width="30" height="4" fill="#ef4444" opacity="0.7"/>
                  <rect x="-15" y="-4" width="25" height="3" fill="#ef4444" opacity="0.5"/>
                  <rect x="-15" y="0" width="28" height="3" fill="#ef4444" opacity="0.5"/>
                  <rect x="-15" y="4" width="20" height="3" fill="#ef4444" opacity="0.5"/>
                  <!-- Breach lines -->
                  <path d="M-25 0 L-35 0 M-35 -5 L-35 5" stroke="#ef4444" stroke-width="2"/>
                  <text x="0" y="-25" text-anchor="middle" fill="white" font-size="10">Data Breach</text>
                </g>
                
                <!-- Credit damage -->
                <g transform="translate(380, 280)">
                  <circle cx="0" cy="0" r="25" fill="#fef3c7" opacity="0.9"/>
                  <!-- Downward arrow (credit score drop) -->
                  <path d="M0 -10 L0 10 M-5 5 L0 10 L5 5" stroke="#ef4444" stroke-width="2" fill="none"/>
                  <text x="0" y="-8" text-anchor="middle" fill="#ef4444" font-size="8">SCORE</text>
                  <text x="0" y="40" text-anchor="middle" fill="white" font-size="10">Credit Damage</text>
                </g>
                
                <!-- Identity restoration -->
                <g transform="translate(150, 300)">
                  <circle cx="0" cy="0" r="25" fill="#f0fdf4" opacity="0.9"/>
                  <circle cx="0" cy="-4" r="8" stroke="#10b981" stroke-width="2" fill="none"/>
                  <path d="M-8 8 C-8 4 -4 2 0 2 C4 2 8 4 8 8" stroke="#10b981" stroke-width="2" fill="none"/>
                  <path d="M-3 -1 L0 2 L6 -4" stroke="#10b981" stroke-width="2" fill="none"/>
                  <text x="0" y="40" text-anchor="middle" fill="white" font-size="10">Restoration</text>
                </g>
                
                <!-- Security shield -->
                <g transform="translate(80, 200)">
                  <circle cx="0" cy="0" r="20" fill="#dbeafe" opacity="0.9"/>
                  <path d="M0 -10 L6 -6 L6 2 C6 6 3 8 0 8 C-3 8 -6 6 -6 2 L-6 -6 Z" fill="#1e40af"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>
                
                <!-- Monitoring system -->
                <g transform="translate(350, 150)">
                  <circle cx="0" cy="0" r="20" fill="#f3e8ff" opacity="0.9"/>
                  <circle cx="0" cy="0" r="8" stroke="#8b5cf6" stroke-width="2" fill="none"/>
                  <circle cx="0" cy="0" r="4" fill="#8b5cf6"/>
                  <path d="M8 -8 L12 -12 M8 8 L12 12 M-8 -8 L-12 -12 M-8 8 L-12 12" stroke="#8b5cf6" stroke-width="1.5"/>
                </g>
                
                <!-- Attack flow lines -->
                <path d="M145 135 Q200 160 225 185" stroke="#ef4444" stroke-width="2" fill="none" opacity="0.8" stroke-dasharray="4,4"/>
                <path d="M275 185 Q330 200 355 265" stroke="#ef4444" stroke-width="2" fill="none" opacity="0.6" stroke-dasharray="4,4"/>
                
                <!-- Recovery/protection path -->
                <path d="M355 295 Q280 320 175 315" stroke="#10b981" stroke-width="3" fill="none" opacity="0.8"/>
                <path d="M125 315 Q100 280 100 220" stroke="#1e40af" stroke-width="3" fill="none" opacity="0.8"/>
                
                <!-- Success indicators -->
                <g transform="translate(50, 80)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>
                
                <g transform="translate(450, 320)">
                  <circle cx="0" cy="0" r="6" fill="#10b981"/>
                  <path d="M-2 0 L0 2 L4 -2" stroke="white" stroke-width="1.5" fill="none"/>
                </g>
                
                <!-- Decorative elements -->
                <circle cx="30" cy="30" r="2" fill="#8b5cf6" opacity="0.4"/>
                <circle cx="470" cy="50" r="3" fill="#1e40af" opacity="0.5"/>
                <circle cx="20" cy="380" r="2" fill="#10b981" opacity="0.6"/>
                <circle cx="480" cy="370" r="2" fill="#ef4444" opacity="0.4"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Service Overview Section -->
    <section class="section" style="background: white;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Understanding Identity Theft</h2>
          <p class="section-subtitle" style="margin: 0;">Identity theft occurs when criminals steal personal information to commit fraud, open accounts, make purchases, or access existing accounts without authorization, causing significant financial and emotional damage.</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; margin-top: 3rem; align-items: center;">
          <div>
            <h3 style="color: #111827; margin-bottom: 1.5rem; font-size: 1.4rem;">How Identity Theft Works</h3>
            <p style="color: #6b7280; line-height: 1.7; margin-bottom: 1.5rem;">Identity thieves obtain personal information through data breaches, phishing attacks, mail theft, social engineering, or by purchasing stolen data on the dark web. They use this information to impersonate victims and commit various forms of fraud.</p>
            <p style="color: #6b7280; line-height: 1.7; margin-bottom: 1.5rem;">Common types include financial identity theft (opening credit accounts), medical identity theft (using insurance), tax identity theft (filing fraudulent returns), and criminal identity theft (using someone's identity when arrested).</p>
            <p style="color: #6b7280; line-height: 1.7; margin: 0;">Recovery requires immediate action to minimize damage, comprehensive restoration of credit and accounts, and implementation of ongoing monitoring to prevent future theft. Our team guides victims through every step of this complex process.</p>
          </div>
          <div style="text-align: center;">
            <div style="background: #f0f8ff; padding: 3rem; border-radius: 1rem;">
              <div style="font-size: 3rem; color: #1e40af; font-weight: 800; margin-bottom: 1rem;">14.4M</div>
              <div style="color: #6b7280; font-size: 1rem;">Identity Theft Victims in 2023</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Warning Signs Section -->
    <section class="section" style="background: #f8fafc;">
      <div class="container">
        <div class="section-header" style="text-align: left;">
          <h2>Warning Signs of Identity Theft</h2>
          <p class="section-subtitle" style="margin: 0;">Recognize these red flags to detect identity theft early and minimize potential damage.</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 3rem; margin-top: 3rem;">
          <!-- Warning Sign 1 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 9V13" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 17H12.01" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Unexpected Credit Changes</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Sudden drops in credit score, unfamiliar accounts on credit reports, or denial of credit applications due to information in your credit file that you don't recognize.</p>
          </div>

          <!-- Warning Sign 2 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 9V13" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
                <path d="M12 17H12.01" stroke="#f59e0b" stroke-width="2" stroke-linecap="round"/>
                <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="#f59e0b" stroke-width="2"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Missing Mail or Bills</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Bills or financial statements stop arriving, you receive bills for accounts you didn't open, or you get unexpected credit cards or account statements in the mail.</p>
          </div>

          <!-- Warning Sign 3 -->
          <div style="text-align: left; padding: 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
            <div style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem;">
              <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
                <path d="M15 9L9 15" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
                <path d="M9 9L15 15" stroke="#ef4444" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </div>
            <h3 style="color: #111827; margin-bottom: 1rem;">Unauthorized Account Activity</h3>
            <p style="color: #6b7280; line-height: 1.6; margin: 0;">Unfamiliar charges on bank or credit card statements, withdrawals you didn't make, or notifications about account changes you didn't authorize.</p>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer Placeholder -->
  <div id="footer-placeholder"></div>

  <!-- JavaScript -->
  <script src="js/main.js"></script>
  <script src="js/contact-loader.js"></script>
</body>
</html>
