<?php
/**
 * Simple API Test Script
 * Tests the API endpoints and returns detailed debugging information
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

try {
    // Test 1: Include the database config
    $config_path = __DIR__ . '/admin/includes/db_config.php';
    if (!file_exists($config_path)) {
        throw new Exception("Database config file not found: $config_path");
    }
    
    require_once $config_path;
    
    // Test 2: Check if database connection variables are set
    if (!isset($mysqli) || !isset($pdo)) {
        throw new Exception("Database connection objects not initialized");
    }
    
    // Test 3: Test database connection
    if ($mysqli->connect_error) {
        throw new Exception("Database connection failed: " . $mysqli->connect_error);
    }
    
    // Test 4: Check if blog_posts table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'blog_posts'");
    if (!$result || $result->num_rows == 0) {
        throw new Exception("blog_posts table does not exist");
    }
    
    // Test 5: Check table structure
    $columns_result = $mysqli->query("SHOW COLUMNS FROM blog_posts");
    $columns = [];
    while ($row = $columns_result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    // Test 6: Try to fetch some posts
    $query = "SELECT * FROM blog_posts WHERE status = 'published' ORDER BY created_at DESC LIMIT 3";
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $posts = $stmt->fetchAll();
    
    // Test 7: Format posts (simplified)
    $formatted_posts = [];
    foreach ($posts as $post) {
        $featured_image = $post['featured_image'] ?? null;
        if ($featured_image) {
            // Fix path format
            if (strpos($featured_image, 'admin/') !== 0 && strpos($featured_image, 'http') !== 0) {
                $featured_image = 'admin/' . ltrim($featured_image, '/');
            }
        }
        
        $formatted_posts[] = [
            'id' => $post['id'],
            'title' => $post['title'] ?? 'Untitled',
            'excerpt' => $post['excerpt'] ?? substr(strip_tags($post['content'] ?? ''), 0, 150) . '...',
            'author' => $post['author'] ?? 'Admin',
            'category' => $post['category'] ?? 'News',
            'featured_image' => $featured_image,
            'created_at' => $post['created_at'] ?? null,
            'formatted_date' => isset($post['created_at']) ? date('F j, Y', strtotime($post['created_at'])) : 'Unknown'
        ];
    }
    
    // Success response
    $response = [
        'success' => true,
        'message' => 'API test completed successfully',
        'posts' => $formatted_posts,
        'debug' => [
            'environment' => $environment ?? 'unknown',
            'database' => $db_name ?? 'unknown',
            'host' => $db_host ?? 'unknown',
            'username' => $db_username ?? 'unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'unknown',
            'php_version' => PHP_VERSION,
            'mysql_version' => $mysqli->server_info ?? 'unknown',
            'table_columns' => $columns,
            'posts_count' => count($posts),
            'timestamp' => date('Y-m-d H:i:s'),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'script_path' => __FILE__
        ]
    ];
    
} catch (Exception $e) {
    // Error response
    $response = [
        'success' => false,
        'error' => $e->getMessage(),
        'debug' => [
            'environment' => $environment ?? 'unknown',
            'server_name' => $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'unknown',
            'php_version' => PHP_VERSION,
            'error_file' => $e->getFile(),
            'error_line' => $e->getLine(),
            'timestamp' => date('Y-m-d H:i:s'),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'script_path' => __FILE__,
            'config_path' => $config_path ?? 'unknown',
            'config_exists' => file_exists($config_path ?? '') ? 'yes' : 'no'
        ]
    ];
    
    http_response_code(500);
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
