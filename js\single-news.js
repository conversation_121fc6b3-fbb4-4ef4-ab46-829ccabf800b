// Single News Article Loader for Forensic Involve

document.addEventListener('DOMContentLoaded', function() {
    loadSingleArticle();
});

function loadSingleArticle() {
    // Get post ID from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const postId = urlParams.get('id');

    if (!postId) {
        showError('No article ID provided');
        return;
    }

    // Show loading state
    showLoading();

    // Fetch the article
    fetch(`api/get-single-post.php?id=${postId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.post) {
                displayArticle(data.post);
                loadRelatedArticles(data.post.category, postId);
            } else {
                showError(data.error || 'Article not found');
            }
        })
        .catch(error => {
            console.error('Error loading article:', error);
            showError('Failed to load article');
        });
}

function displayArticle(post) {
    // Hide loading and error states
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('article-content').style.display = 'block';

    // Update page title and meta
    document.getElementById('page-title').textContent = `${post.title} - Forensic Involve`;
    document.getElementById('page-description').setAttribute('content', post.excerpt);
    document.title = `${post.title} - Forensic Involve`;

    // Update article meta information
    document.getElementById('article-category').textContent = post.category || 'News';
    document.getElementById('article-date').innerHTML = `<i class="fas fa-calendar"></i> ${post.formatted_date}`;
    document.getElementById('article-author').textContent = post.author;

    // Update article content
    document.getElementById('article-title').textContent = post.title;
    document.getElementById('article-excerpt').textContent = post.excerpt;

    // Handle featured image
    if (post.featured_image && post.featured_image.trim() !== '') {
        const imageContainer = document.getElementById('featured-image-container');
        const image = document.getElementById('featured-image');
        const placeholder = document.getElementById('image-placeholder');

        // Fix featured image path - add 'admin/' prefix if not present
        let featuredImagePath = post.featured_image;
        if (!featuredImagePath.startsWith('admin/')) {
            featuredImagePath = 'admin/' + featuredImagePath; // Add 'admin/' prefix
        }

        image.src = featuredImagePath;
        image.alt = post.title;
        imageContainer.style.display = 'block';
        placeholder.style.display = 'none';
    } else {
        // Show placeholder if no featured image
        const imageContainer = document.getElementById('featured-image-container');
        const placeholder = document.getElementById('image-placeholder');

        imageContainer.style.display = 'none';
        placeholder.style.display = 'block';
    }

    // Format and display article content
    document.getElementById('article-body').innerHTML = formatPostContent(post.content);

    // Update social sharing
    updateSocialSharing(post);
}

function formatPostContent(content) {
    // Enhanced content formatting
    let formatted = content
        // Handle markdown-style formatting
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^## (.*$)/gim, '<h2 style="color: #1e40af; margin: 2rem 0 1rem 0; font-size: 1.875rem; font-weight: 700;">$1</h2>')
        .replace(/^### (.*$)/gim, '<h3 style="color: #1e40af; margin: 1.5rem 0 1rem 0; font-size: 1.5rem; font-weight: 600;">$1</h3>')
        .replace(/^#### (.*$)/gim, '<h4 style="color: #374151; margin: 1.5rem 0 1rem 0; font-size: 1.25rem; font-weight: 600;">$1</h4>')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" style="color: #1e40af; text-decoration: underline;">$1</a>');

    // Split into paragraphs and format
    const paragraphs = formatted.split(/\n\s*\n/);
    const formattedParagraphs = paragraphs
        .filter(p => p.trim().length > 0)
        .map(p => {
            // Don't wrap headings in paragraphs
            if (p.trim().startsWith('<h')) {
                return p.trim();
            }
            // Handle lists
            if (p.includes('- ')) {
                const listItems = p.split('\n')
                    .filter(line => line.trim().startsWith('- '))
                    .map(line => `<li style="margin-bottom: 0.5rem;">${line.replace(/^- /, '').trim()}</li>`)
                    .join('');
                return `<ul style="margin: 1rem 0; padding-left: 2rem;">${listItems}</ul>`;
            }
            return `<p style="margin-bottom: 1.5rem; line-height: 1.7;">${p.trim()}</p>`;
        });

    return formattedParagraphs.join('\n');
}

function loadRelatedArticles(category, currentPostId) {
    // Load related articles from the same category
    fetch(`api/get-posts.php?limit=3&category=${encodeURIComponent(category)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.posts.length > 0) {
                // Filter out current post and limit to 3
                const relatedPosts = data.posts
                    .filter(post => post.id != currentPostId)
                    .slice(0, 3);

                if (relatedPosts.length > 0) {
                    displayRelatedArticles(relatedPosts);
                } else {
                    // If no related posts in same category, load latest posts
                    loadLatestArticles(currentPostId);
                }
            } else {
                loadLatestArticles(currentPostId);
            }
        })
        .catch(error => {
            console.error('Error loading related articles:', error);
            loadLatestArticles(currentPostId);
        });
}

function loadLatestArticles(currentPostId) {
    // Load latest articles as fallback
    fetch('api/get-posts.php?limit=3')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.posts.length > 0) {
                const latestPosts = data.posts
                    .filter(post => post.id != currentPostId)
                    .slice(0, 3);

                displayRelatedArticles(latestPosts);
            }
        })
        .catch(error => {
            console.error('Error loading latest articles:', error);
        });
}

function displayRelatedArticles(posts) {
    const container = document.getElementById('related-articles');

    const articlesHtml = posts.map(post => {
        // Fix featured image path - add 'admin/' prefix if not present
        let featuredImagePath = '';
        if (post.featured_image && post.featured_image.trim() !== '') {
            featuredImagePath = post.featured_image;
            if (!featuredImagePath.startsWith('admin/')) {
                featuredImagePath = 'admin/' + featuredImagePath; // Add 'admin/' prefix
            }
        }

        return `
            <div class="card">
                ${featuredImagePath ? `
                    <div style="height: 200px; background: url('${escapeHtml(featuredImagePath)}'); background-size: cover; background-position: center; border-radius: 0.5rem 0.5rem 0 0; margin: -1.5rem -1.5rem 1.5rem -1.5rem;"></div>
                ` : ''}
                <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                    ${post.formatted_date} • ${post.author}
                </div>
                <h3 class="card-title">${escapeHtml(post.title)}</h3>
                <p class="card-description">${escapeHtml(post.excerpt)}</p>
                <a href="single-news.html?id=${post.id}" class="btn btn-primary">Read More</a>
            </div>
        `;
    }).join('');

    container.innerHTML = articlesHtml;
}

function showLoading() {
    document.getElementById('loading-state').style.display = 'block';
    document.getElementById('error-state').style.display = 'none';
    document.getElementById('article-content').style.display = 'none';
}

function showError(message) {
    document.getElementById('loading-state').style.display = 'none';
    document.getElementById('error-state').style.display = 'block';
    document.getElementById('article-content').style.display = 'none';

    // Update error message if needed
    const errorSection = document.getElementById('error-state');
    const errorText = errorSection.querySelector('p');
    if (message && message !== 'Article not found') {
        errorText.textContent = message;
    }
}

function updateSocialSharing(post) {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(post.title);
    const description = encodeURIComponent(post.excerpt);

    // Store for sharing functions
    window.currentPost = {
        url: url,
        title: title,
        description: description
    };
}

function shareOnFacebook() {
    const post = window.currentPost;
    const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${post.url}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter() {
    const post = window.currentPost;
    const shareUrl = `https://twitter.com/intent/tweet?url=${post.url}&text=${post.title}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function shareOnLinkedIn() {
    const post = window.currentPost;
    const shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${post.url}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Add loading animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
