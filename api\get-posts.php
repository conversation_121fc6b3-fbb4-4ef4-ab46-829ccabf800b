<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../admin/includes/db_config.php';

try {
    
    // Get parameters
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 6;
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $category = isset($_GET['category']) ? $_GET['category'] : '';
    $featured = isset($_GET['featured']) ? (bool)$_GET['featured'] : false;
    
    $offset = ($page - 1) * $limit;
      // Build query
    $query = "
        SELECT * FROM blog_posts 
        WHERE status = 'published'
    ";
    
    $params = [];    
    if ($category) {
        $query .= " AND category = ?";
        $params[] = $category;
    }
    
    if ($featured) {
        $query .= " AND is_featured = 1";
    }
    
    $query .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format posts for frontend with improved image path handling
    $formatted_posts = [];
    foreach ($posts as $post) {
        // Handle featured image path consistency
        $featured_image = $post['featured_image'];
        if ($featured_image) {
            // Ensure consistent path format (compatible with older PHP versions)
            if (strpos($featured_image, 'admin/') !== 0 && strpos($featured_image, 'http') !== 0) {
                $featured_image = 'admin/' . ltrim($featured_image, '/');
            }

            // Verify file exists (for better consistency)
            $full_path = '../' . $featured_image;
            if (!file_exists($full_path)) {
                // Try alternative paths
                $alt_paths = [
                    '../admin/uploads/' . basename($featured_image),
                    '../uploads/' . basename($featured_image),
                    '../images/' . basename($featured_image)
                ];

                $featured_image = null; // Default to null if not found
                foreach ($alt_paths as $alt_path) {
                    if (file_exists($alt_path)) {
                        $featured_image = str_replace('../', '', $alt_path);
                        break;
                    }
                }
            }
        }

        $formatted_posts[] = [
            'id' => $post['id'],
            'title' => $post['title'],
            'excerpt' => $post['excerpt'] ?: substr(strip_tags($post['content']), 0, 150) . '...',
            'content' => $post['content'],
            'author' => $post['author'] ?: 'Admin',
            'category' => $post['category'] ?: 'News',
            'featured_image' => $featured_image,
            'created_at' => $post['created_at'],
            'updated_at' => $post['updated_at'],
            'formatted_date' => date('F j, Y', strtotime($post['created_at'])),
            'slug' => $post['slug'] ?: 'post-' . $post['id'],
            'is_featured' => (bool)($post['is_featured'] ?? false)
        ];
    }
      // Get total count for pagination
    $countQuery = "
        SELECT COUNT(*) as total 
        FROM blog_posts 
        WHERE status = 'published'
    ";
    
    $countParams = [];
    if ($category) {
        $countQuery .= " AND category = ?";
        $countParams[] = $category;
    }
    if ($featured) {
        $countQuery .= " AND is_featured = 1";
    }
    
    $stmt = $pdo->prepare($countQuery);
    $stmt->execute($countParams);
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $response = [
        'success' => true,
        'posts' => $formatted_posts,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($total / $limit),
            'total_posts' => $total,
            'per_page' => $limit,
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ],
        'debug' => [
            'environment' => $environment ?? 'unknown',
            'database' => $db_name,
            'timestamp' => date('Y-m-d H:i:s'),
            'query_params' => [
                'limit' => $limit,
                'page' => $page,
                'category' => $category,
                'featured' => $featured
            ]
        ]
    ];
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
