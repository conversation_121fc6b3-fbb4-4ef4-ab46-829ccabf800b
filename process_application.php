<?php
// Enable error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include PHPMailer
require_once 'vendor/autoload.php';

use <PERSON><PERSON>Mail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Load configuration
$config = require_once 'config.php';

// Function to sanitize input
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// Function to format checkbox arrays
function formatCheckboxArray($checkboxArray) {
    if (is_array($checkboxArray)) {
        return implode(', ', array_map('sanitizeInput', $checkboxArray));
    }
    return sanitizeInput($checkboxArray);
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Collect and sanitize form data
        $formData = [
            'first_name' => sanitizeInput($_POST['first_name'] ?? ''),
            'last_name' => sanitizeInput($_POST['last_name'] ?? ''),
            'email' => filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'country' => sanitizeInput($_POST['country'] ?? ''),
            'timezone' => sanitizeInput($_POST['timezone'] ?? ''),
            'contact_method' => sanitizeInput($_POST['contact_method'] ?? ''),
            'service_type' => sanitizeInput($_POST['service_type'] ?? ''),
            'urgency' => sanitizeInput($_POST['urgency'] ?? ''),
            'referral_source' => sanitizeInput($_POST['referral_source'] ?? ''),
            'incident_date' => sanitizeInput($_POST['incident_date'] ?? ''),
            'discovery_date' => sanitizeInput($_POST['discovery_date'] ?? ''),
            'incident_location' => sanitizeInput($_POST['incident_location'] ?? ''),
            'incident_description' => sanitizeInput($_POST['incident_description'] ?? ''),
            'initial_contact' => sanitizeInput($_POST['initial_contact'] ?? ''),
            'detailed_description' => sanitizeInput($_POST['detailed_description'] ?? ''),
            'amount_lost' => sanitizeInput($_POST['amount_lost'] ?? ''),
            'currency' => sanitizeInput($_POST['currency'] ?? ''),
            'payment_methods' => formatCheckboxArray($_POST['payment_methods'] ?? []),
            'bank_details' => sanitizeInput($_POST['bank_details'] ?? ''),
            'destination_account' => sanitizeInput($_POST['destination_account'] ?? ''),
            'evidence_types' => formatCheckboxArray($_POST['evidence_types'] ?? []),
            'scammer_info' => sanitizeInput($_POST['scammer_info'] ?? ''),
            'evidence_details' => sanitizeInput($_POST['evidence_details'] ?? ''),
            'law_enforcement' => sanitizeInput($_POST['law_enforcement'] ?? ''),
            'bank_contact' => sanitizeInput($_POST['bank_contact'] ?? ''),
            'other_services' => sanitizeInput($_POST['other_services'] ?? ''),
            'other_actions' => sanitizeInput($_POST['other_actions'] ?? ''),
            'time_sensitive' => sanitizeInput($_POST['time_sensitive'] ?? ''),
            'desired_outcome' => sanitizeInput($_POST['desired_outcome'] ?? ''),
            'additional_info' => sanitizeInput($_POST['additional_info'] ?? ''),
            'consent_contact' => isset($_POST['consent_contact']) ? 'Yes' : 'No',
            'consent_information' => isset($_POST['consent_information']) ? 'Yes' : 'No',
            'consent_accuracy' => isset($_POST['consent_accuracy']) ? 'Yes' : 'No',
            'consent_consultation' => isset($_POST['consent_consultation']) ? 'Yes' : 'No'
        ];

        // Validate required fields
        $requiredFields = [
            'first_name', 'last_name', 'email', 'phone', 'country', 'contact_method',
            'service_type', 'urgency', 'incident_date', 'incident_description',
            'detailed_description', 'amount_lost'
        ];

        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (empty($formData[$field])) {
                $missingFields[] = str_replace('_', ' ', ucfirst($field));
            }
        }

        // Check consent checkboxes
        $requiredConsents = ['consent_contact', 'consent_information', 'consent_accuracy', 'consent_consultation'];
        foreach ($requiredConsents as $consent) {
            if ($formData[$consent] !== 'Yes') {
                $missingFields[] = 'Required consent: ' . str_replace(['consent_', '_'], ['', ' '], $consent);
            }
        }

        if (!empty($missingFields)) {
            throw new Exception('Missing required fields: ' . implode(', ', $missingFields));
        }

        // Validate email
        if (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address provided.');
        }

        // Create PHPMailer instance
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp']['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp']['username'];
        $mail->Password = $config['smtp']['password'];
        $mail->SMTPSecure = ($config['smtp']['encryption'] === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = $config['smtp']['port'];

        // Recipients
        $mail->setFrom($config['email']['from_address'], $config['email']['from_name']);
        $mail->addAddress($config['email']['to_address'], $config['email']['to_name']);
        $mail->addReplyTo($formData['email'], $formData['first_name'] . ' ' . $formData['last_name']);

        // Content
        $mail->isHTML(true);
        $mail->Subject = 'New Service Application - ' . $formData['first_name'] . ' ' . $formData['last_name'] . ' (' . $formData['service_type'] . ')';

        // Create email body
        $emailBody = generateEmailBody($formData);
        $mail->Body = $emailBody;
        $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $emailBody));

        // Send email
        $mail->send();

        // Success response
        $response = [
            'success' => true,
            'message' => 'Application submitted successfully! We will contact you within 24 hours.',
            'redirect' => $config['app']['success_redirect']
        ];

    } catch (Exception $e) {
        // Error response
        $response = [
            'success' => false,
            'message' => 'Error: ' . $e->getMessage(),
            'error_details' => $mail->ErrorInfo ?? ''
        ];
    }

    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }

    // For regular form submission, redirect or show message
    if ($response['success']) {
        header('Location: application-success.html');
        exit;
    } else {
        // Show error page or redirect back with error
        header('Location: apply.html?error=' . urlencode($response['message']));
        exit;
    }
} else {
    // Not a POST request
    header('Location: apply.html');
    exit;
}

function generateEmailBody($data) {
    $urgencyColors = [
        'emergency' => '#dc2626',
        'urgent' => '#ea580c',
        'high' => '#d97706',
        'normal' => '#059669',
        'consultation' => '#0891b2'
    ];

    $urgencyColor = $urgencyColors[$data['urgency']] ?? '#6b7280';

    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Service Application</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { background: #1e40af; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
            .content { background: #f8fafc; padding: 20px; }
            .section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb; }
            .field { margin: 10px 0; }
            .label { font-weight: bold; color: #374151; }
            .value { margin-left: 10px; color: #6b7280; }
            .urgency { display: inline-block; padding: 4px 12px; border-radius: 20px; color: white; font-weight: bold; }
            .footer { background: #374151; color: white; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>New Service Application Received</h1>
                <p><strong>Submitted:</strong> ' . date('F j, Y \a\t g:i A T') . '</p>
                <p><strong>Urgency:</strong> <span class="urgency" style="background-color: ' . $urgencyColor . ';">' . strtoupper($data['urgency']) . '</span></p>
            </div>

            <div class="content">
                <div class="section">
                    <h2>Personal Information</h2>
                    <div class="field"><span class="label">Name:</span><span class="value">' . $data['first_name'] . ' ' . $data['last_name'] . '</span></div>
                    <div class="field"><span class="label">Email:</span><span class="value">' . $data['email'] . '</span></div>
                    <div class="field"><span class="label">Phone:</span><span class="value">' . $data['phone'] . '</span></div>
                    <div class="field"><span class="label">Country:</span><span class="value">' . $data['country'] . '</span></div>
                    <div class="field"><span class="label">Time Zone:</span><span class="value">' . $data['timezone'] . '</span></div>
                    <div class="field"><span class="label">Preferred Contact:</span><span class="value">' . $data['contact_method'] . '</span></div>
                </div>

                <div class="section">
                    <h2>Service Information</h2>
                    <div class="field"><span class="label">Service Type:</span><span class="value">' . $data['service_type'] . '</span></div>
                    <div class="field"><span class="label">Urgency Level:</span><span class="value">' . $data['urgency'] . '</span></div>
                    <div class="field"><span class="label">How they heard about us:</span><span class="value">' . $data['referral_source'] . '</span></div>
                </div>

                <div class="section">
                    <h2>Incident Details</h2>
                    <div class="field"><span class="label">Incident Date:</span><span class="value">' . $data['incident_date'] . '</span></div>
                    <div class="field"><span class="label">Discovery Date:</span><span class="value">' . ($data['discovery_date'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Location:</span><span class="value">' . ($data['incident_location'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Initial Contact Method:</span><span class="value">' . ($data['initial_contact'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Brief Description:</span><br><span class="value">' . nl2br($data['incident_description']) . '</span></div>
                    <div class="field"><span class="label">Detailed Description:</span><br><span class="value">' . nl2br($data['detailed_description']) . '</span></div>
                </div>

                <div class="section">
                    <h2>Financial Information</h2>
                    <div class="field"><span class="label">Amount Lost:</span><span class="value">$' . number_format($data['amount_lost'], 2) . ' ' . ($data['currency'] ? '(' . $data['currency'] . ')' : '(USD)') . '</span></div>
                    <div class="field"><span class="label">Payment Methods:</span><span class="value">' . ($data['payment_methods'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Bank Details:</span><br><span class="value">' . ($data['bank_details'] ? nl2br($data['bank_details']) : 'Not provided') . '</span></div>
                    <div class="field"><span class="label">Destination Account Info:</span><br><span class="value">' . ($data['destination_account'] ? nl2br($data['destination_account']) : 'Not provided') . '</span></div>
                </div>

                <div class="section">
                    <h2>Evidence & Documentation</h2>
                    <div class="field"><span class="label">Evidence Types:</span><span class="value">' . ($data['evidence_types'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Scammer Information:</span><br><span class="value">' . ($data['scammer_info'] ? nl2br($data['scammer_info']) : 'Not provided') . '</span></div>
                    <div class="field"><span class="label">Additional Evidence Details:</span><br><span class="value">' . ($data['evidence_details'] ? nl2br($data['evidence_details']) : 'Not provided') . '</span></div>
                </div>

                <div class="section">
                    <h2>Previous Actions</h2>
                    <div class="field"><span class="label">Law Enforcement Reported:</span><span class="value">' . ($data['law_enforcement'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Bank Contacted:</span><span class="value">' . ($data['bank_contact'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Other Recovery Services:</span><span class="value">' . ($data['other_services'] ?: 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Other Actions:</span><br><span class="value">' . ($data['other_actions'] ? nl2br($data['other_actions']) : 'Not provided') . '</span></div>
                </div>

                <div class="section">
                    <h2>Additional Information</h2>
                    <div class="field"><span class="label">Time-Sensitive Factors:</span><br><span class="value">' . ($data['time_sensitive'] ? nl2br($data['time_sensitive']) : 'None specified') . '</span></div>
                    <div class="field"><span class="label">Desired Outcome:</span><br><span class="value">' . ($data['desired_outcome'] ? nl2br($data['desired_outcome']) : 'Not specified') . '</span></div>
                    <div class="field"><span class="label">Additional Information:</span><br><span class="value">' . ($data['additional_info'] ? nl2br($data['additional_info']) : 'None provided') . '</span></div>
                </div>

                <div class="section">
                    <h2>Consent & Agreement</h2>
                    <div class="field"><span class="label">Contact Consent:</span><span class="value">' . $data['consent_contact'] . '</span></div>
                    <div class="field"><span class="label">Information Use Consent:</span><span class="value">' . $data['consent_information'] . '</span></div>
                    <div class="field"><span class="label">Accuracy Confirmation:</span><span class="value">' . $data['consent_accuracy'] . '</span></div>
                    <div class="field"><span class="label">Consultation Understanding:</span><span class="value">' . $data['consent_consultation'] . '</span></div>
                </div>
            </div>

            <div class="footer">
                <p><strong>Action Required:</strong> Please respond to this application within 24 hours as promised.</p>
                <p>Reply directly to this email to contact the applicant: ' . $data['email'] . '</p>
            </div>
        </div>
    </body>
    </html>';

    return $html;
}
?>
