<?php
/**
 * Footer Contact Setup Script
 * Ensures site_settings table exists and is populated with contact information
 */

require_once 'admin/includes/db_config.php';

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Footer Contact Setup</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .success { color: #10b981; background: #d1fae5; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: #ef4444; background: #fee2e2; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .info { color: #1e40af; background: #dbeafe; padding: 10px; border-radius: 4px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #d1d5db; padding: 8px; text-align: left; }
        th { background: #f3f4f6; }
        h1, h2 { color: #1e40af; }
        .btn { background: #1e40af; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
    </style>
</head>
<body>";

echo "<h1>🔧 Footer Contact Setup</h1>";
echo "<p>Setting up dynamic footer contact information controlled from admin panel.</p>";

try {
    // Check if site_settings table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'site_settings'");
    
    if ($result->num_rows == 0) {
        echo "<div class='info'>Creating site_settings table...</div>";
        
        // Create site_settings table
        $sql = "
        CREATE TABLE `site_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL UNIQUE,
            `setting_value` text NOT NULL,
            `setting_type` enum('text','email','phone','address','textarea','url') DEFAULT 'text',
            `setting_group` varchar(50) DEFAULT 'general',
            `setting_label` varchar(255) NOT NULL,
            `setting_description` text,
            `is_active` tinyint(1) DEFAULT 1,
            `display_order` int(11) DEFAULT 0,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_setting_key` (`setting_key`),
            KEY `idx_setting_group` (`setting_group`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        if ($mysqli->query($sql)) {
            echo "<div class='success'>✅ site_settings table created successfully</div>";
        } else {
            throw new Exception("Error creating table: " . $mysqli->error);
        }
    } else {
        echo "<div class='success'>✅ site_settings table already exists</div>";
    }
    
    // Default contact settings for footer
    $default_settings = [
        // Contact Information
        [
            'setting_key' => 'phone_general',
            'setting_value' => '+1 (555) 123-DEMO',
            'setting_type' => 'phone',
            'setting_group' => 'contact',
            'setting_label' => 'General Phone Number',
            'setting_description' => 'Main business phone number displayed in footer',
            'display_order' => 1
        ],
        [
            'setting_key' => 'phone_emergency',
            'setting_value' => '(*************',
            'setting_type' => 'phone',
            'setting_group' => 'contact',
            'setting_label' => 'Emergency Hotline',
            'setting_description' => '24/7 emergency contact number',
            'display_order' => 2
        ],
        [
            'setting_key' => 'email_general',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'General Email',
            'setting_description' => 'Main business email address displayed in footer',
            'display_order' => 3
        ],
        [
            'setting_key' => 'email_help',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'Help & Support Email',
            'setting_description' => 'Customer support email address',
            'display_order' => 4
        ],
        [
            'setting_key' => 'email_emergency',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'Emergency Email',
            'setting_description' => 'Emergency response email address',
            'display_order' => 5
        ],
        [
            'setting_key' => 'address_primary',
            'setting_value' => '123 Demo Street, Demo City, DC 12345, United States',
            'setting_type' => 'address',
            'setting_group' => 'contact',
            'setting_label' => 'Primary Office Address',
            'setting_description' => 'Main business address displayed in footer',
            'display_order' => 6
        ],
        [
            'setting_key' => 'address_secondary',
            'setting_value' => '456 Sample Avenue, Example Town, ET 67890, United Kingdom',
            'setting_type' => 'address',
            'setting_group' => 'contact',
            'setting_label' => 'Secondary Office Address',
            'setting_description' => 'Additional office location (optional)',
            'display_order' => 7
        ],
        
        // Social Media Links (Optional)
        [
            'setting_key' => 'social_facebook',
            'setting_value' => '#',
            'setting_type' => 'url',
            'setting_group' => 'social',
            'setting_label' => 'Facebook Page URL',
            'setting_description' => 'Facebook page link (leave as # to hide)',
            'display_order' => 1
        ],
        [
            'setting_key' => 'social_twitter',
            'setting_value' => '#',
            'setting_type' => 'url',
            'setting_group' => 'social',
            'setting_label' => 'Twitter Profile URL',
            'setting_description' => 'Twitter profile link (leave as # to hide)',
            'display_order' => 2
        ],
        [
            'setting_key' => 'social_linkedin',
            'setting_value' => '#',
            'setting_type' => 'url',
            'setting_group' => 'social',
            'setting_label' => 'LinkedIn Company URL',
            'setting_description' => 'LinkedIn company page link (leave as # to hide)',
            'display_order' => 3
        ],
        [
            'setting_key' => 'social_instagram',
            'setting_value' => '#',
            'setting_type' => 'url',
            'setting_group' => 'social',
            'setting_label' => 'Instagram Profile URL',
            'setting_description' => 'Instagram profile link (leave as # to hide)',
            'display_order' => 4
        ],
        
        // Business Information
        [
            'setting_key' => 'business_hours',
            'setting_value' => '24/7 Emergency Support Available',
            'setting_type' => 'text',
            'setting_group' => 'business',
            'setting_label' => 'Business Hours',
            'setting_description' => 'Operating hours or availability information',
            'display_order' => 1
        ],
        [
            'setting_key' => 'business_emergency_note',
            'setting_value' => 'Available 24/7',
            'setting_type' => 'text',
            'setting_group' => 'business',
            'setting_label' => 'Emergency Availability',
            'setting_description' => 'Emergency service availability note',
            'display_order' => 2
        ]
    ];
    
    // Insert default settings if they don't exist
    $inserted_count = 0;
    $updated_count = 0;
    
    foreach ($default_settings as $setting) {
        $stmt = $mysqli->prepare("SELECT COUNT(*) FROM site_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $setting['setting_key']);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_row()[0];
        
        if ($count == 0) {
            $stmt = $mysqli->prepare("
                INSERT INTO site_settings 
                (setting_key, setting_value, setting_type, setting_group, setting_label, setting_description, display_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("ssssssi", 
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['setting_type'],
                $setting['setting_group'],
                $setting['setting_label'],
                $setting['setting_description'],
                $setting['display_order']
            );
            
            if ($stmt->execute()) {
                $inserted_count++;
            }
        } else {
            $updated_count++;
        }
    }
    
    echo "<div class='success'>✅ Inserted $inserted_count new contact settings</div>";
    echo "<div class='info'>ℹ️ Found $updated_count existing settings (not modified)</div>";
    
    // Show current settings
    echo "<h2>📋 Current Contact Settings</h2>";
    $result = $mysqli->query("SELECT * FROM site_settings ORDER BY setting_group, display_order");
    echo "<table>";
    echo "<tr><th>Group</th><th>Label</th><th>Key</th><th>Value</th><th>Type</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['setting_group']) . "</td>";
        echo "<td>" . htmlspecialchars($row['setting_label']) . "</td>";
        echo "<td>" . htmlspecialchars($row['setting_key']) . "</td>";
        echo "<td>" . htmlspecialchars(substr($row['setting_value'], 0, 50)) . (strlen($row['setting_value']) > 50 ? '...' : '') . "</td>";
        echo "<td>" . htmlspecialchars($row['setting_type']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🎉 Footer Contact Setup Complete!</h2>";
    echo "<div class='success'>";
    echo "<p><strong>What's been set up:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Dynamic footer contact information</li>";
    echo "<li>✅ Admin-controlled contact settings</li>";
    echo "<li>✅ Social media links (optional)</li>";
    echo "<li>✅ Automatic contact replacement on all pages</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>📝 Next Steps:</h3>";
    echo "<div class='info'>";
    echo "<ol>";
    echo "<li><strong>Edit Contact Information:</strong> <a href='admin/contact-settings.php' class='btn'>Go to Contact Settings</a></li>";
    echo "<li><strong>Test Footer:</strong> Visit any page to see dynamic contact info</li>";
    echo "<li><strong>Admin Dashboard:</strong> <a href='admin/index.php' class='btn'>Admin Panel</a></li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🔍 Test APIs:</h3>";
    echo "<p><a href='api/get-contact-info.php' target='_blank' class='btn'>Test Contact API</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";

$mysqli->close();
?>
