<?php
/**
 * Contact Management System Setup
 * Creates site_settings table and populates with default contact information
 */

require_once 'admin/includes/db_config.php';

echo "<h2>🔧 Contact Management System Setup</h2>";

try {
    // Check if site_settings table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'site_settings'");
    
    if ($result->num_rows > 0) {
        echo "<p>⚠️ site_settings table already exists</p>";
    } else {
        // Create site_settings table
        $sql = "
        CREATE TABLE `site_settings` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `setting_key` varchar(100) NOT NULL UNIQUE,
            `setting_value` text NOT NULL,
            `setting_type` enum('text','email','phone','address','textarea') DEFAULT 'text',
            `setting_group` varchar(50) DEFAULT 'general',
            `setting_label` varchar(255) NOT NULL,
            `setting_description` text,
            `is_active` tinyint(1) DEFAULT 1,
            `display_order` int(11) DEFAULT 0,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_setting_key` (`setting_key`),
            KEY `idx_setting_group` (`setting_group`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        if ($mysqli->query($sql)) {
            echo "<p>✅ site_settings table created successfully</p>";
        } else {
            throw new Exception("Error creating table: " . $mysqli->error);
        }
    }
    
    // Insert default contact settings
    $default_settings = [
        // Phone Numbers
        [
            'setting_key' => 'phone_emergency',
            'setting_value' => '(*************',
            'setting_type' => 'phone',
            'setting_group' => 'contact',
            'setting_label' => 'Emergency Hotline',
            'setting_description' => '24/7 emergency contact number',
            'display_order' => 1
        ],
        [
            'setting_key' => 'phone_general',
            'setting_value' => '+1 (555) 123-DEMO',
            'setting_type' => 'phone',
            'setting_group' => 'contact',
            'setting_label' => 'General Phone',
            'setting_description' => 'Main business phone number',
            'display_order' => 2
        ],
        
        // Email Addresses
        [
            'setting_key' => 'email_general',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'General Email',
            'setting_description' => 'Main business email address',
            'display_order' => 3
        ],
        [
            'setting_key' => 'email_help',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'Help & Support Email',
            'setting_description' => 'Customer support email address',
            'display_order' => 4
        ],
        [
            'setting_key' => 'email_emergency',
            'setting_value' => '<EMAIL>',
            'setting_type' => 'email',
            'setting_group' => 'contact',
            'setting_label' => 'Emergency Email',
            'setting_description' => 'Emergency response email address',
            'display_order' => 5
        ],
        
        // Physical Addresses
        [
            'setting_key' => 'address_primary',
            'setting_value' => '123 Demo Street, Demo City, DC 12345, United States',
            'setting_type' => 'address',
            'setting_group' => 'contact',
            'setting_label' => 'Primary Office Address',
            'setting_description' => 'Main business address',
            'display_order' => 6
        ],
        [
            'setting_key' => 'address_secondary',
            'setting_value' => '456 Sample Avenue, Example Town, ET 67890, United Kingdom',
            'setting_type' => 'address',
            'setting_group' => 'contact',
            'setting_label' => 'Secondary Office Address',
            'setting_description' => 'International office address',
            'display_order' => 7
        ],
        
        // Business Hours & Additional Info
        [
            'setting_key' => 'business_hours',
            'setting_value' => '24/7 Emergency Support Available',
            'setting_type' => 'text',
            'setting_group' => 'contact',
            'setting_label' => 'Business Hours',
            'setting_description' => 'Operating hours information',
            'display_order' => 8
        ],
        [
            'setting_key' => 'emergency_note',
            'setting_value' => 'Available 24/7',
            'setting_type' => 'text',
            'setting_group' => 'contact',
            'setting_label' => 'Emergency Availability',
            'setting_description' => 'Emergency service availability note',
            'display_order' => 9
        ],
        
        // Social Media (for future use)
        [
            'setting_key' => 'social_facebook',
            'setting_value' => '#',
            'setting_type' => 'text',
            'setting_group' => 'social',
            'setting_label' => 'Facebook URL',
            'setting_description' => 'Facebook page URL',
            'display_order' => 10
        ],
        [
            'setting_key' => 'social_twitter',
            'setting_value' => '#',
            'setting_type' => 'text',
            'setting_group' => 'social',
            'setting_label' => 'Twitter URL',
            'setting_description' => 'Twitter profile URL',
            'display_order' => 11
        ],
        [
            'setting_key' => 'social_linkedin',
            'setting_value' => '#',
            'setting_type' => 'text',
            'setting_group' => 'social',
            'setting_label' => 'LinkedIn URL',
            'setting_description' => 'LinkedIn company page URL',
            'display_order' => 12
        ],
        [
            'setting_key' => 'social_instagram',
            'setting_value' => '#',
            'setting_type' => 'text',
            'setting_group' => 'social',
            'setting_label' => 'Instagram URL',
            'setting_description' => 'Instagram profile URL',
            'display_order' => 13
        ]
    ];
    
    // Check if settings already exist and insert if not
    $inserted_count = 0;
    foreach ($default_settings as $setting) {
        $stmt = $mysqli->prepare("SELECT COUNT(*) FROM site_settings WHERE setting_key = ?");
        $stmt->bind_param("s", $setting['setting_key']);
        $stmt->execute();
        $result = $stmt->get_result();
        $count = $result->fetch_row()[0];
        
        if ($count == 0) {
            $stmt = $mysqli->prepare("
                INSERT INTO site_settings 
                (setting_key, setting_value, setting_type, setting_group, setting_label, setting_description, display_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("ssssssi", 
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['setting_type'],
                $setting['setting_group'],
                $setting['setting_label'],
                $setting['setting_description'],
                $setting['display_order']
            );
            
            if ($stmt->execute()) {
                $inserted_count++;
            }
        }
    }
    
    echo "<p>✅ Inserted $inserted_count new contact settings</p>";
    
    // Show current settings
    echo "<h3>Current Contact Settings:</h3>";
    $result = $mysqli->query("SELECT * FROM site_settings ORDER BY setting_group, display_order");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Key</th><th>Label</th><th>Value</th><th>Type</th><th>Group</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_key']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_label']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_value']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_type']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($row['setting_group']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎉 Contact Management System Setup Complete!</h3>";
    echo "<p><a href='admin/contact-settings.php'>Go to Contact Settings</a> (after creating the admin page)</p>";
    echo "<p><a href='admin/index.php'>Go to Admin Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
}

$mysqli->close();
?>

<style>
    body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        max-width: 1000px; 
        margin: 50px auto; 
        padding: 20px; 
        line-height: 1.6;
    }
    h2 { color: #1e40af; margin-bottom: 20px; }
    h3 { color: #374151; margin-top: 30px; }
    p { margin: 10px 0; }
    a { color: #1e40af; text-decoration: none; }
    a:hover { text-decoration: underline; }
    table { margin: 20px 0; }
    th, td { text-align: left; }
    th { background: #f9fafb; font-weight: 600; }
</style>
