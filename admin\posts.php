<?php
session_start();
require_once 'includes/db_config.php';
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle post deletion
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $post_id = (int)($_POST['post_id'] ?? 0);        if ($post_id > 0) {
            try {
                $stmt = $pdo->prepare("DELETE FROM blog_posts WHERE id = ?");
                if ($stmt->execute([$post_id])) {
                    $message = 'Post deleted successfully.';
                } else {
                    $error = 'Failed to delete post.';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Handle status updates
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $post_id = (int)($_POST['post_id'] ?? 0);
        $new_status = $_POST['status'] ?? '';
          if ($post_id > 0 && in_array($new_status, ['published', 'draft', 'archived'])) {
            try {
                $stmt = $pdo->prepare("UPDATE blog_posts SET status = ? WHERE id = ?");
                if ($stmt->execute([$new_status, $post_id])) {
                    $message = 'Post status updated successfully.';
                } else {
                    $error = 'Failed to update post status.';
                }
            } catch (PDOException $e) {
                $error = 'Database error: ' . $e->getMessage();
            }
        }
    }
}

// Get all posts with pagination - enhanced input validation
$page = isset($_GET['page']) ? max(1, min(1000, (int)$_GET['page'])) : 1; // Limit max page to prevent DoS
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    // Count total posts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM blog_posts");
    $result = $stmt->fetch();
    $total_posts = $result ? $result['total'] : 0;
    $total_pages = ceil($total_posts / $limit);
      // Get posts for current page
    $stmt = $pdo->prepare("
        SELECT bp.*, c.name as category_name
        FROM blog_posts bp
        LEFT JOIN categories c ON bp.category_id = c.id
        ORDER BY bp.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $posts = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    $posts = [];
    $total_pages = 0;
}

$csrf_token = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Posts - Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-dashboard">
    <!-- Navigation -->
    <nav class="admin-nav">
        <div class="nav-brand">
            <h2>Admin Dashboard</h2>
        </div>
        <div class="nav-user">
            <span>Welcome, <?php echo htmlspecialchars(getCurrentAdmin()['username']); ?></span>
            <a href="logout.php" class="btn btn-outline">Logout</a>
        </div>
    </nav>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <div class="sidebar-menu">
            <a href="index.php" class="menu-item">
                <span class="icon">📊</span>
                Dashboard
            </a>
            <a href="posts.php" class="menu-item active">
                <span class="icon">📝</span>
                Manage Posts
            </a>
            <a href="add-post.php" class="menu-item">
                <span class="icon">➕</span>
                Add New Post
            </a>
            <a href="categories.php" class="menu-item">
                <span class="icon">📁</span>
                Categories
            </a>
            <a href="media.php" class="menu-item">
                <span class="icon">🖼️</span>
                Media
            </a>
            <a href="contact-settings.php" class="menu-item">
                <span class="icon">📞</span>
                Contact Settings
            </a>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="admin-header">
            <h1>Manage Posts</h1>
            <p>Edit, delete, and manage all your blog posts and news articles</p>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Posts Management -->
        <div class="posts-management">
            <div class="management-header">
                <h2>All Posts (<?php echo $total_posts; ?>)</h2>
                <a href="add-post.php" class="btn btn-primary">Add New Post</a>
            </div>

            <?php if (empty($posts)): ?>
                <div class="empty-state">
                    <p>No posts found. <a href="add-post.php">Create your first post</a></p>
                </div>
            <?php else: ?>
                <div class="posts-table-container">
                    <div class="posts-table">
                        <div class="table-header">
                            <div class="col-title">Title</div>
                            <div class="col-category">Category</div>
                            <div class="col-status">Status</div>
                            <div class="col-date">Date</div>
                            <div class="col-actions">Actions</div>
                        </div>

                        <?php foreach ($posts as $post): ?>
                            <div class="table-row">
                                <div class="col-title">
                                    <strong><?php echo htmlspecialchars($post['title']); ?></strong>
                                    <?php if ($post['featured_image']): ?>
                                        <span class="has-image">📷</span>
                                    <?php endif; ?>
                                </div>
                                <div class="col-category">
                                    <?php echo htmlspecialchars($post['category_name'] ?? 'Uncategorized'); ?>
                                </div>
                                <div class="col-status">
                                    <form method="POST" class="status-form" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="post_id" value="<?php echo $post['id']; ?>">
                                        <select name="status" onchange="this.form.submit()" class="status-select">
                                            <option value="published" <?php echo $post['status'] === 'published' ? 'selected' : ''; ?>>Published</option>
                                            <option value="draft" <?php echo $post['status'] === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                            <option value="archived" <?php echo $post['status'] === 'archived' ? 'selected' : ''; ?>>Archived</option>
                                        </select>
                                    </form>
                                </div>
                                <div class="col-date">
                                    <?php echo date('M j, Y', strtotime($post['created_at'])); ?>
                                </div>                                <div class="col-actions">
                                    <div class="action-buttons">
                                        <a href="edit-post.php?id=<?php echo urlencode($post['id']); ?>" class="btn btn-sm">Edit</a>
                                        <button type="button" class="btn btn-sm btn-danger"
                                                onclick="confirmDelete(<?php echo (int)$post['id']; ?>, '<?php echo htmlspecialchars($post['title'], ENT_QUOTES, 'UTF-8'); ?>')">
                                            Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo urlencode($page - 1); ?>" class="btn btn-outline">Previous</a>
                        <?php endif; ?>

                        <span class="pagination-info">
                            Page <?php echo (int)$page; ?> of <?php echo (int)$total_pages; ?>
                        </span>

                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo urlencode($page + 1); ?>" class="btn btn-outline">Next</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </main>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>Confirm Deletion</h3>
            <p>Are you sure you want to delete "<span id="deletePostTitle"></span>"?</p>
            <p><strong>This action cannot be undone.</strong></p>
            <div class="modal-actions">
                <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">Cancel</button>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="post_id" id="deletePostId">
                    <button type="submit" class="btn btn-danger">Delete Post</button>
                </form>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
    <script>
        function confirmDelete(postId, postTitle) {
            document.getElementById('deletePostId').value = postId;
            document.getElementById('deletePostTitle').textContent = postTitle;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // Close modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
    </script>

    <style>
        .posts-management {
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
        }

        .management-header h2 {
            margin: 0;
            color: var(--gray-900);
            font-weight: 600;
        }

        .posts-table-container {
            overflow-x: auto;
        }

        .posts-table .table-header,
        .posts-table .table-row {
            grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
        }

        .has-image {
            margin-left: 0.5rem;
            opacity: 0.5;
        }

        .status-select {
            padding: 0.25rem 0.5rem;
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            font-size: 0.8125rem;
            background: var(--white);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-danger {
            background: var(--error);
            color: var(--white);
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid var(--gray-200);
        }

        .pagination-info {
            color: var(--gray-600);
            font-weight: 500;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            box-shadow: var(--shadow-lg);
        }

        .modal-content h3 {
            margin-bottom: 1rem;
            color: var(--gray-900);
        }

        .modal-content p {
            margin-bottom: 1rem;
            color: var(--gray-600);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 1.5rem;
        }
    </style>
</body>
</html>
