<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Cyber Security</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0A74DA" offset="0%"></stop>
            <stop stop-color="#003366" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFC107" offset="0%"></stop>
            <stop stop-color="#FD7E14" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Cyber-Security" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="#F8F9FA" cx="400" cy="300" r="250"></circle>
        <g id="Shield" transform="translate(250.000000, 150.000000)">
            <path d="M150,0 C150,0 275,50 275,100 L275,200 C275,275 150,300 150,300 C150,300 25,275 25,200 L25,100 C25,50 150,0 150,0 Z" id="Shield-Base" fill="url(#linearGradient-1)"></path>
            <path d="M150,25 C150,25 250,65 250,100 L250,200 C250,260 150,280 150,280 C150,280 50,260 50,200 L50,100 C50,65 150,25 150,25 Z" id="Shield-Inner" fill="#FFFFFF"></path>
            <path d="M150,50 C150,50 225,80 225,110 L225,190 C225,240 150,260 150,260 C150,260 75,240 75,190 L75,110 C75,80 150,50 150,50 Z" id="Shield-Core" fill="url(#linearGradient-1)" opacity="0.5"></path>
        </g>
        <g id="Lock" transform="translate(325.000000, 225.000000)">
            <rect id="Lock-Body" fill="url(#linearGradient-2)" x="0" y="50" width="150" height="100" rx="10"></rect>
            <path d="M30,50 L30,30 C30,13.4314575 43.4314575,0 60,0 L90,0 C106.568542,0 120,13.4314575 120,30 L120,50" id="Lock-Shackle" stroke="#343A40" stroke-width="15" stroke-linecap="round"></path>
            <circle id="Lock-Keyhole" fill="#343A40" cx="75" cy="100" r="15"></circle>
        </g>
        <g id="Binary" transform="translate(200.000000, 100.000000)" fill="#343A40" opacity="0.1">
            <text font-family="Courier" font-size="14" font-weight="normal">
                <tspan x="0" y="14">01001010 01101111 01101000 01101110</tspan>
                <tspan x="0" y="34">01000100 01101111 01100101 00100000</tspan>
                <tspan x="0" y="54">01010011 01100101 01100011 01110101</tspan>
                <tspan x="0" y="74">01110010 01101001 01110100 01111001</tspan>
                <tspan x="0" y="94">01010000 01110010 01101111 01110100</tspan>
                <tspan x="0" y="114">01100101 01100011 01110100 01101001</tspan>
                <tspan x="0" y="134">01101111 01101110 00100000 01000100</tspan>
                <tspan x="0" y="154">01100001 01110100 01100001 00100000</tspan>
                <tspan x="0" y="174">01010011 01100001 01100110 01100101</tspan>
                <tspan x="0" y="194">01000111 01110101 01100001 01110010</tspan>
                <tspan x="0" y="214">01100100 01101001 01100001 01101110</tspan>
                <tspan x="0" y="234">01010011 01100101 01100011 01110101</tspan>
                <tspan x="0" y="254">01110010 01100101 00100000 01000100</tspan>
                <tspan x="0" y="274">01100001 01110100 01100001 00100000</tspan>
                <tspan x="0" y="294">01010000 01110010 01101111 01110100</tspan>
                <tspan x="0" y="314">01100101 01100011 01110100 01101001</tspan>
                <tspan x="0" y="334">01101111 01101110 00100000 01010011</tspan>
                <tspan x="0" y="354">01111001 01110011 01110100 01100101</tspan>
                <tspan x="0" y="374">01101101 01110011 00100000 01010011</tspan>
                <tspan x="0" y="394">01100001 01100110 01100101 01110100</tspan>
                <tspan x="0" y="414">01111001 00100000 01000110 01101001</tspan>
                <tspan x="0" y="434">01110010 01110011 01110100 00100000</tspan>
            </text>
        </g>
        <g id="Circles" transform="translate(150.000000, 100.000000)" fill="#0A74DA" opacity="0.1">
            <circle cx="50" cy="50" r="10"></circle>
            <circle cx="100" cy="75" r="5"></circle>
            <circle cx="150" cy="25" r="8"></circle>
            <circle cx="200" cy="100" r="12"></circle>
            <circle cx="250" cy="50" r="7"></circle>
            <circle cx="300" cy="75" r="9"></circle>
            <circle cx="350" cy="25" r="6"></circle>
            <circle cx="400" cy="100" r="11"></circle>
            <circle cx="450" cy="50" r="8"></circle>
            <circle cx="500" cy="75" r="10"></circle>
        </g>
        <g id="Lines" transform="translate(150.000000, 400.000000)" stroke="#0A74DA" stroke-width="2" opacity="0.2">
            <path d="M0,0 L500,0" id="Line-1"></path>
            <path d="M50,25 L450,25" id="Line-2"></path>
            <path d="M100,50 L400,50" id="Line-3"></path>
            <path d="M150,75 L350,75" id="Line-4"></path>
            <path d="M200,100 L300,100" id="Line-5"></path>
        </g>
    </g>
</svg>